"""
性能测试程序

测试系统在高负载下的性能表现
"""

import requests
import time
import threading
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
import random

def test_api_performance():
    """测试API性能"""
    base_url = "http://127.0.0.1:8000"
    
    print("=" * 80)
    print("兼职接单管理系统 - 性能测试")
    print("=" * 80)
    
    # 1. 单个请求响应时间测试
    print("\n📊 步骤1: 单个请求响应时间测试")
    
    endpoints = [
        ("/health", "健康检查"),
        ("/api/v1/customers", "客户列表"),
        ("/api/v1/orders", "订单列表"),
        ("/api/v1/work-logs", "工时记录"),
        ("/api/v1/analytics/overview", "统计分析"),
        ("/api/v1/attachments", "文件列表"),
        ("/api/v1/settings/system/info", "系统信息")
    ]
    
    for endpoint, name in endpoints:
        response_times = []
        for i in range(10):  # 每个端点测试10次
            start_time = time.time()
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                end_time = time.time()
                if response.status_code == 200:
                    response_times.append((end_time - start_time) * 1000)  # 转换为毫秒
            except Exception as e:
                print(f"   ❌ {name} 请求失败: {e}")
                continue
        
        if response_times:
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            print(f"   ✅ {name}: 平均 {avg_time:.2f}ms, 最小 {min_time:.2f}ms, 最大 {max_time:.2f}ms")
        else:
            print(f"   ❌ {name}: 所有请求都失败")
    
    # 2. 并发请求测试
    print("\n🚀 步骤2: 并发请求测试")
    
    def make_request(endpoint):
        """发送单个请求"""
        start_time = time.time()
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            end_time = time.time()
            return {
                'success': response.status_code == 200,
                'response_time': (end_time - start_time) * 1000,
                'status_code': response.status_code
            }
        except Exception as e:
            return {
                'success': False,
                'response_time': None,
                'error': str(e)
            }
    
    # 测试不同并发级别
    concurrency_levels = [5, 10, 20]
    test_endpoint = "/api/v1/customers"
    
    for concurrency in concurrency_levels:
        print(f"\n   测试并发级别: {concurrency}")
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [executor.submit(make_request, test_endpoint) for _ in range(concurrency)]
            results = [future.result() for future in as_completed(futures)]
        end_time = time.time()
        
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        if successful_requests:
            response_times = [r['response_time'] for r in successful_requests]
            avg_time = statistics.mean(response_times)
            total_time = (end_time - start_time) * 1000
            throughput = len(successful_requests) / (total_time / 1000)  # 请求/秒
            
            print(f"   ✅ 成功: {len(successful_requests)}/{concurrency}")
            print(f"   📈 平均响应时间: {avg_time:.2f}ms")
            print(f"   🔥 吞吐量: {throughput:.2f} 请求/秒")
            print(f"   ⏱️ 总耗时: {total_time:.2f}ms")
        
        if failed_requests:
            print(f"   ❌ 失败: {len(failed_requests)}/{concurrency}")
    
    # 3. 数据库压力测试
    print("\n💾 步骤3: 数据库压力测试")
    
    def create_test_customer():
        """创建测试客户"""
        random_id = random.randint(10000, 99999)
        customer_data = {
            "name": f"压力测试客户{random_id}",
            "contact_phone": f"139{random_id:05d}",
            "contact_email": f"stress{random_id}@test.com",
            "source": "direct",
            "company": f"压力测试公司{random_id}",
            "notes": f"压力测试创建的客户 - {random_id}"
        }
        
        try:
            response = requests.post(f"{base_url}/api/v1/customers", json=customer_data, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    # 并发创建客户
    print("   并发创建客户测试...")
    create_concurrency = 10
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=create_concurrency) as executor:
        futures = [executor.submit(create_test_customer) for _ in range(create_concurrency)]
        create_results = [future.result() for future in as_completed(futures)]
    
    end_time = time.time()
    successful_creates = sum(create_results)
    create_time = (end_time - start_time) * 1000
    
    print(f"   ✅ 成功创建: {successful_creates}/{create_concurrency}")
    print(f"   ⏱️ 总耗时: {create_time:.2f}ms")
    print(f"   📈 创建速率: {successful_creates / (create_time / 1000):.2f} 客户/秒")
    
    # 4. 内存和资源使用测试
    print("\n🔧 步骤4: 系统资源监控")
    
    try:
        # 获取系统信息
        response = requests.get(f"{base_url}/api/v1/settings/system/info")
        if response.status_code == 200:
            system_info = response.json().get('data', {})
            print(f"   💾 数据库大小: {system_info.get('database_size', 'unknown')}")
            print(f"   📁 存储使用: {system_info.get('storage_used', 'unknown')}")
            print(f"   🧠 内存使用: {system_info.get('memory_usage', 'unknown')}")
            print(f"   ⚡ 系统状态: {system_info.get('system_status', 'unknown')}")
        else:
            print("   ❌ 无法获取系统信息")
    except Exception as e:
        print(f"   ❌ 系统信息获取错误: {e}")
    
    # 5. 长时间运行测试
    print("\n⏰ 步骤5: 长时间运行测试（30秒）")
    
    def continuous_requests():
        """持续发送请求"""
        request_count = 0
        error_count = 0
        start_time = time.time()
        
        while time.time() - start_time < 30:  # 运行30秒
            try:
                response = requests.get(f"{base_url}/health", timeout=5)
                if response.status_code == 200:
                    request_count += 1
                else:
                    error_count += 1
            except:
                error_count += 1
            
            time.sleep(0.1)  # 每100ms发送一次请求
        
        return request_count, error_count
    
    print("   开始长时间运行测试...")
    request_count, error_count = continuous_requests()
    
    print(f"   ✅ 成功请求: {request_count}")
    print(f"   ❌ 失败请求: {error_count}")
    print(f"   📊 成功率: {(request_count / (request_count + error_count) * 100):.2f}%")
    print(f"   🔥 平均QPS: {request_count / 30:.2f}")
    
    print("\n" + "=" * 80)
    print("🎯 性能测试完成！")
    print("=" * 80)
    
    # 性能评估
    print("\n📋 性能评估报告:")
    print("   🟢 优秀: 响应时间 < 100ms, 成功率 > 99%")
    print("   🟡 良好: 响应时间 < 500ms, 成功率 > 95%") 
    print("   🔴 需要优化: 响应时间 > 500ms, 成功率 < 95%")

if __name__ == "__main__":
    test_api_performance()
