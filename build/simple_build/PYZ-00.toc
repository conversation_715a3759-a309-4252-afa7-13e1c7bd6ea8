('/Users/<USER>/Desktop/你好/build/simple_build/PYZ-00.pyz',
 [('MySQLdb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/__init__.py',
   'PYMODULE'),
  ('MySQLdb._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/_exceptions.py',
   'PYMODULE'),
  ('MySQLdb.connections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/connections.py',
   'PYMODULE'),
  ('MySQLdb.constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/constants/__init__.py',
   'PYMODULE'),
  ('MySQLdb.constants.CLIENT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/constants/CLIENT.py',
   'PYMODULE'),
  ('MySQLdb.constants.FIELD_TYPE',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/constants/FIELD_TYPE.py',
   'PYMODULE'),
  ('MySQLdb.constants.FLAG',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/constants/FLAG.py',
   'PYMODULE'),
  ('MySQLdb.converters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/converters.py',
   'PYMODULE'),
  ('MySQLdb.cursors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/cursors.py',
   'PYMODULE'),
  ('MySQLdb.release',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/release.py',
   'PYMODULE'),
  ('MySQLdb.times',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/MySQLdb/times.py',
   'PYMODULE'),
  ('PIL',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('PIL.features',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PIL/features.py',
   'PYMODULE'),
  ('PyQt6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyQt6/__init__.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_pytest',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/__init__.py',
   'PYMODULE'),
  ('_pytest._argcomplete',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_argcomplete.py',
   'PYMODULE'),
  ('_pytest._code',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_code/__init__.py',
   'PYMODULE'),
  ('_pytest._code.code',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_code/code.py',
   'PYMODULE'),
  ('_pytest._code.source',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_code/source.py',
   'PYMODULE'),
  ('_pytest._io',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_io/__init__.py',
   'PYMODULE'),
  ('_pytest._io.saferepr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_io/saferepr.py',
   'PYMODULE'),
  ('_pytest._io.terminalwriter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_io/terminalwriter.py',
   'PYMODULE'),
  ('_pytest._io.wcwidth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_io/wcwidth.py',
   'PYMODULE'),
  ('_pytest._py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_py/__init__.py',
   'PYMODULE'),
  ('_pytest._py.error',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_py/error.py',
   'PYMODULE'),
  ('_pytest._py.path',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_py/path.py',
   'PYMODULE'),
  ('_pytest._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/_version.py',
   'PYMODULE'),
  ('_pytest.assertion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/assertion/__init__.py',
   'PYMODULE'),
  ('_pytest.assertion.rewrite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/assertion/rewrite.py',
   'PYMODULE'),
  ('_pytest.assertion.truncate',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/assertion/truncate.py',
   'PYMODULE'),
  ('_pytest.assertion.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/assertion/util.py',
   'PYMODULE'),
  ('_pytest.cacheprovider',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/cacheprovider.py',
   'PYMODULE'),
  ('_pytest.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/compat.py',
   'PYMODULE'),
  ('_pytest.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/config/__init__.py',
   'PYMODULE'),
  ('_pytest.config.argparsing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/config/argparsing.py',
   'PYMODULE'),
  ('_pytest.config.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/config/compat.py',
   'PYMODULE'),
  ('_pytest.config.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/config/exceptions.py',
   'PYMODULE'),
  ('_pytest.config.findpaths',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/config/findpaths.py',
   'PYMODULE'),
  ('_pytest.deprecated',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/deprecated.py',
   'PYMODULE'),
  ('_pytest.fixtures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/fixtures.py',
   'PYMODULE'),
  ('_pytest.helpconfig',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/helpconfig.py',
   'PYMODULE'),
  ('_pytest.hookspec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/hookspec.py',
   'PYMODULE'),
  ('_pytest.main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/main.py',
   'PYMODULE'),
  ('_pytest.mark',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/mark/__init__.py',
   'PYMODULE'),
  ('_pytest.mark.expression',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/mark/expression.py',
   'PYMODULE'),
  ('_pytest.mark.structures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/mark/structures.py',
   'PYMODULE'),
  ('_pytest.nodes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/nodes.py',
   'PYMODULE'),
  ('_pytest.outcomes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/outcomes.py',
   'PYMODULE'),
  ('_pytest.pathlib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/pathlib.py',
   'PYMODULE'),
  ('_pytest.python',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/python.py',
   'PYMODULE'),
  ('_pytest.python_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/python_api.py',
   'PYMODULE'),
  ('_pytest.reports',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/reports.py',
   'PYMODULE'),
  ('_pytest.runner',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/runner.py',
   'PYMODULE'),
  ('_pytest.scope',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/scope.py',
   'PYMODULE'),
  ('_pytest.stash',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/stash.py',
   'PYMODULE'),
  ('_pytest.terminal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/terminal.py',
   'PYMODULE'),
  ('_pytest.timing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/timing.py',
   'PYMODULE'),
  ('_pytest.warning_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/warning_types.py',
   'PYMODULE'),
  ('_pytest.warnings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/_pytest/warnings.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/_threading_local.py',
   'PYMODULE'),
  ('annotated_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/annotated_types/__init__.py',
   'PYMODULE'),
  ('anyio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_backends/__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_backends/_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_backends/_trio.py',
   'PYMODULE'),
  ('anyio._core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_tasks.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_tempfile.py',
   'PYMODULE'),
  ('anyio._core._testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/_core/_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/abc/_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/streams/__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/streams/memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/streams/stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/streams/tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/anyio/to_thread.py',
   'PYMODULE'),
  ('app', '/Users/<USER>/Desktop/你好/app/__init__.py', 'PYMODULE'),
  ('app.backend',
   '/Users/<USER>/Desktop/你好/app/backend/__init__.py',
   'PYMODULE'),
  ('app.backend.api',
   '/Users/<USER>/Desktop/你好/app/backend/api/__init__.py',
   'PYMODULE'),
  ('app.backend.api.endpoints',
   '/Users/<USER>/Desktop/你好/app/backend/api/endpoints/__init__.py',
   'PYMODULE'),
  ('app.backend.api.endpoints.analytics',
   '/Users/<USER>/Desktop/你好/app/backend/api/endpoints/analytics.py',
   'PYMODULE'),
  ('app.backend.api.endpoints.attachments',
   '/Users/<USER>/Desktop/你好/app/backend/api/endpoints/attachments.py',
   'PYMODULE'),
  ('app.backend.api.endpoints.customers',
   '/Users/<USER>/Desktop/你好/app/backend/api/endpoints/customers.py',
   'PYMODULE'),
  ('app.backend.api.endpoints.orders',
   '/Users/<USER>/Desktop/你好/app/backend/api/endpoints/orders.py',
   'PYMODULE'),
  ('app.backend.api.endpoints.settings',
   '/Users/<USER>/Desktop/你好/app/backend/api/endpoints/settings.py',
   'PYMODULE'),
  ('app.backend.api.endpoints.work_logs',
   '/Users/<USER>/Desktop/你好/app/backend/api/endpoints/work_logs.py',
   'PYMODULE'),
  ('app.backend.main',
   '/Users/<USER>/Desktop/你好/app/backend/main.py',
   'PYMODULE'),
  ('app.backend.schemas',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/__init__.py',
   'PYMODULE'),
  ('app.backend.schemas.analytics',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/analytics.py',
   'PYMODULE'),
  ('app.backend.schemas.attachment',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/attachment.py',
   'PYMODULE'),
  ('app.backend.schemas.base',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/base.py',
   'PYMODULE'),
  ('app.backend.schemas.customer',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/customer.py',
   'PYMODULE'),
  ('app.backend.schemas.order',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/order.py',
   'PYMODULE'),
  ('app.backend.schemas.setting',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/setting.py',
   'PYMODULE'),
  ('app.backend.schemas.work_log',
   '/Users/<USER>/Desktop/你好/app/backend/schemas/work_log.py',
   'PYMODULE'),
  ('app.backend.services',
   '/Users/<USER>/Desktop/你好/app/backend/services/__init__.py',
   'PYMODULE'),
  ('app.backend.services.analytics_service',
   '/Users/<USER>/Desktop/你好/app/backend/services/analytics_service.py',
   'PYMODULE'),
  ('app.backend.services.attachment_service',
   '/Users/<USER>/Desktop/你好/app/backend/services/attachment_service.py',
   'PYMODULE'),
  ('app.backend.services.customer_service',
   '/Users/<USER>/Desktop/你好/app/backend/services/customer_service.py',
   'PYMODULE'),
  ('app.backend.services.order_service',
   '/Users/<USER>/Desktop/你好/app/backend/services/order_service.py',
   'PYMODULE'),
  ('app.backend.services.setting_service',
   '/Users/<USER>/Desktop/你好/app/backend/services/setting_service.py',
   'PYMODULE'),
  ('app.backend.services.work_log_service',
   '/Users/<USER>/Desktop/你好/app/backend/services/work_log_service.py',
   'PYMODULE'),
  ('app.core', '/Users/<USER>/Desktop/你好/app/core/__init__.py', 'PYMODULE'),
  ('app.core.config',
   '/Users/<USER>/Desktop/你好/app/core/config.py',
   'PYMODULE'),
  ('app.core.database',
   '/Users/<USER>/Desktop/你好/app/core/database.py',
   'PYMODULE'),
  ('app.core.exceptions',
   '/Users/<USER>/Desktop/你好/app/core/exceptions.py',
   'PYMODULE'),
  ('app.frontend',
   '/Users/<USER>/Desktop/你好/app/frontend/__init__.py',
   'PYMODULE'),
  ('app.frontend.controllers',
   '/Users/<USER>/Desktop/你好/app/frontend/controllers/__init__.py',
   'PYMODULE'),
  ('app.frontend.controllers.api_client',
   '/Users/<USER>/Desktop/你好/app/frontend/controllers/api_client.py',
   'PYMODULE'),
  ('app.frontend.ui',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/__init__.py',
   'PYMODULE'),
  ('app.frontend.ui.analytics_view',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/analytics_view.py',
   'PYMODULE'),
  ('app.frontend.ui.customer_manager',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/customer_manager.py',
   'PYMODULE'),
  ('app.frontend.ui.file_manager',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/file_manager.py',
   'PYMODULE'),
  ('app.frontend.ui.main_window',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/main_window.py',
   'PYMODULE'),
  ('app.frontend.ui.order_list',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/order_list.py',
   'PYMODULE'),
  ('app.frontend.ui.settings_view',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/settings_view.py',
   'PYMODULE'),
  ('app.frontend.ui.work_log_view',
   '/Users/<USER>/Desktop/你好/app/frontend/ui/work_log_view.py',
   'PYMODULE'),
  ('app.models',
   '/Users/<USER>/Desktop/你好/app/models/__init__.py',
   'PYMODULE'),
  ('app.models.attachment',
   '/Users/<USER>/Desktop/你好/app/models/attachment.py',
   'PYMODULE'),
  ('app.models.base',
   '/Users/<USER>/Desktop/你好/app/models/base.py',
   'PYMODULE'),
  ('app.models.category',
   '/Users/<USER>/Desktop/你好/app/models/category.py',
   'PYMODULE'),
  ('app.models.communication',
   '/Users/<USER>/Desktop/你好/app/models/communication.py',
   'PYMODULE'),
  ('app.models.customer',
   '/Users/<USER>/Desktop/你好/app/models/customer.py',
   'PYMODULE'),
  ('app.models.order',
   '/Users/<USER>/Desktop/你好/app/models/order.py',
   'PYMODULE'),
  ('app.models.setting',
   '/Users/<USER>/Desktop/你好/app/models/setting.py',
   'PYMODULE'),
  ('app.models.template',
   '/Users/<USER>/Desktop/你好/app/models/template.py',
   'PYMODULE'),
  ('app.models.work_log',
   '/Users/<USER>/Desktop/你好/app/models/work_log.py',
   'PYMODULE'),
  ('app.utils', '-', 'PYMODULE'),
  ('argparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ast.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/windows_utils.py',
   'PYMODULE'),
  ('attr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/_compat.py',
   'PYMODULE'),
  ('attr._config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/_config.py',
   'PYMODULE'),
  ('attr._funcs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/_funcs.py',
   'PYMODULE'),
  ('attr._make',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/filters.py',
   'PYMODULE'),
  ('attr.setters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/setters.py',
   'PYMODULE'),
  ('attr.validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/attr/validators.py',
   'PYMODULE'),
  ('base64',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/base64.py',
   'PYMODULE'),
  ('bcrypt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/bcrypt/__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/bcrypt/__about__.py',
   'PYMODULE'),
  ('bdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bdb.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/calendar.py',
   'PYMODULE'),
  ('certifi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/certifi/core.py',
   'PYMODULE'),
  ('cffi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/cparser.py',
   'PYMODULE'),
  ('cffi.error',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/lock.py',
   'PYMODULE'),
  ('cffi.model',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cffi/verifier.py',
   'PYMODULE'),
  ('cgi',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/assets/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('click',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/__init__.py',
   'PYMODULE'),
  ('click._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/_winconsole.py',
   'PYMODULE'),
  ('click.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/core.py',
   'PYMODULE'),
  ('click.decorators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/formatting.py',
   'PYMODULE'),
  ('click.globals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/globals.py',
   'PYMODULE'),
  ('click.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/termui.py',
   'PYMODULE'),
  ('click.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/types.py',
   'PYMODULE'),
  ('click.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/click/utils.py',
   'PYMODULE'),
  ('cmd',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/cmd.py',
   'PYMODULE'),
  ('code',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/code.py',
   'PYMODULE'),
  ('codeop',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/codeop.py',
   'PYMODULE'),
  ('colorama',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/colorama/__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/colorama/ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/colorama/ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/colorama/initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/colorama/win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/colorama/winterm.py',
   'PYMODULE'),
  ('colorsys',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/colorsys.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/copy.py',
   'PYMODULE'),
  ('cryptography',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/backends/openssl/utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/bindings/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/bindings/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/bindings/openssl/_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/bindings/openssl/binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/asymmetric/x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/ciphers/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/ciphers/aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/ciphers/algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/ciphers/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/ciphers/modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/serialization/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/serialization/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/serialization/pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/hazmat/primitives/serialization/ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/x509/__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/x509/base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/x509/certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/x509/extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/x509/general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/x509/name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cryptography/x509/oid.py',
   'PYMODULE'),
  ('csv',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ctypes/wintypes.py',
   'PYMODULE'),
  ('curses',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/curses/has_key.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/difflib.py',
   'PYMODULE'),
  ('dis',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/dis.py',
   'PYMODULE'),
  ('distutils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/cmd.py',
   'PYMODULE'),
  ('distutils.command',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/command/__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/command/bdist.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/command/build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/command/sdist.py',
   'PYMODULE'),
  ('distutils.config',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/config.py',
   'PYMODULE'),
  ('distutils.core',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/core.py',
   'PYMODULE'),
  ('distutils.debug',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/dist.py',
   'PYMODULE'),
  ('distutils.errors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/errors.py',
   'PYMODULE'),
  ('distutils.extension',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/filelist.py',
   'PYMODULE'),
  ('distutils.log',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/text_file.py',
   'PYMODULE'),
  ('distutils.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/util.py',
   'PYMODULE'),
  ('distutils.version',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/distutils/versionpredicate.py',
   'PYMODULE'),
  ('dns',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/__init__.py',
   'PYMODULE'),
  ('dns._asyncbackend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/_asyncbackend.py',
   'PYMODULE'),
  ('dns._asyncio_backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/_asyncio_backend.py',
   'PYMODULE'),
  ('dns._ddr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/_ddr.py',
   'PYMODULE'),
  ('dns._features',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/_features.py',
   'PYMODULE'),
  ('dns._immutable_ctx',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/_immutable_ctx.py',
   'PYMODULE'),
  ('dns._trio_backend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/_trio_backend.py',
   'PYMODULE'),
  ('dns.asyncbackend',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/asyncbackend.py',
   'PYMODULE'),
  ('dns.asyncquery',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/asyncquery.py',
   'PYMODULE'),
  ('dns.asyncresolver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/asyncresolver.py',
   'PYMODULE'),
  ('dns.dnssectypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/dnssectypes.py',
   'PYMODULE'),
  ('dns.edns',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/edns.py',
   'PYMODULE'),
  ('dns.entropy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/entropy.py',
   'PYMODULE'),
  ('dns.enum',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/enum.py',
   'PYMODULE'),
  ('dns.exception',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/exception.py',
   'PYMODULE'),
  ('dns.flags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/flags.py',
   'PYMODULE'),
  ('dns.grange',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/grange.py',
   'PYMODULE'),
  ('dns.immutable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/immutable.py',
   'PYMODULE'),
  ('dns.inet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/inet.py',
   'PYMODULE'),
  ('dns.ipv4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/ipv4.py',
   'PYMODULE'),
  ('dns.ipv6',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/ipv6.py',
   'PYMODULE'),
  ('dns.message',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/message.py',
   'PYMODULE'),
  ('dns.name',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/name.py',
   'PYMODULE'),
  ('dns.nameserver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/nameserver.py',
   'PYMODULE'),
  ('dns.node',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/node.py',
   'PYMODULE'),
  ('dns.opcode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/opcode.py',
   'PYMODULE'),
  ('dns.query',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/query.py',
   'PYMODULE'),
  ('dns.quic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/quic/__init__.py',
   'PYMODULE'),
  ('dns.quic._asyncio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/quic/_asyncio.py',
   'PYMODULE'),
  ('dns.quic._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/quic/_common.py',
   'PYMODULE'),
  ('dns.quic._sync',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/quic/_sync.py',
   'PYMODULE'),
  ('dns.quic._trio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/quic/_trio.py',
   'PYMODULE'),
  ('dns.rcode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rcode.py',
   'PYMODULE'),
  ('dns.rdata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdata.py',
   'PYMODULE'),
  ('dns.rdataclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdataclass.py',
   'PYMODULE'),
  ('dns.rdataset',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdataset.py',
   'PYMODULE'),
  ('dns.rdatatype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdatatype.py',
   'PYMODULE'),
  ('dns.rdtypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/AFSDB.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/DS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L32',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/L32.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L64',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/L64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LP',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/LP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NID',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/NID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/OPT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RESINFO',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/RESINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/RP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/RRSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SMIMEA',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/SMIMEA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/SOA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TKEY',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/TKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.WALLET',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/WALLET.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ZONEMD',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/ANY/ZONEMD.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/CH/__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/CH/A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/APL.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.HTTPS',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/HTTPS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/KX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/PX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SVCB',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/SVCB.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/IN/WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/dnskeybase.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/dsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.svcbbase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/svcbbase.py',
   'PYMODULE'),
  ('dns.rdtypes.tlsabase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/tlsabase.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rdtypes/util.py',
   'PYMODULE'),
  ('dns.renderer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/renderer.py',
   'PYMODULE'),
  ('dns.resolver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/resolver.py',
   'PYMODULE'),
  ('dns.reversename',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/reversename.py',
   'PYMODULE'),
  ('dns.rrset',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/rrset.py',
   'PYMODULE'),
  ('dns.serial',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/serial.py',
   'PYMODULE'),
  ('dns.set',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/set.py',
   'PYMODULE'),
  ('dns.tokenizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/tokenizer.py',
   'PYMODULE'),
  ('dns.transaction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/transaction.py',
   'PYMODULE'),
  ('dns.tsig',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/tsig.py',
   'PYMODULE'),
  ('dns.ttl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/ttl.py',
   'PYMODULE'),
  ('dns.update',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/update.py',
   'PYMODULE'),
  ('dns.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/version.py',
   'PYMODULE'),
  ('dns.win32util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/win32util.py',
   'PYMODULE'),
  ('dns.wire',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/wire.py',
   'PYMODULE'),
  ('dns.xfr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/xfr.py',
   'PYMODULE'),
  ('dns.zone',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/zone.py',
   'PYMODULE'),
  ('dns.zonefile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/zonefile.py',
   'PYMODULE'),
  ('dns.zonetypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dns/zonetypes.py',
   'PYMODULE'),
  ('dotenv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dotenv/__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dotenv/ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dotenv/main.py',
   'PYMODULE'),
  ('dotenv.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dotenv/parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/dotenv/variables.py',
   'PYMODULE'),
  ('email',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/email/utils.py',
   'PYMODULE'),
  ('email_validator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/email_validator/__init__.py',
   'PYMODULE'),
  ('email_validator.deliverability',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/email_validator/deliverability.py',
   'PYMODULE'),
  ('email_validator.exceptions_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/email_validator/exceptions_types.py',
   'PYMODULE'),
  ('email_validator.rfc_constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/email_validator/rfc_constants.py',
   'PYMODULE'),
  ('email_validator.syntax',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/email_validator/syntax.py',
   'PYMODULE'),
  ('email_validator.validate_email',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/email_validator/validate_email.py',
   'PYMODULE'),
  ('email_validator.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/email_validator/version.py',
   'PYMODULE'),
  ('exceptiongroup',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/exceptiongroup/__init__.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/exceptiongroup/_catch.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/exceptiongroup/_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/exceptiongroup/_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/exceptiongroup/_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/exceptiongroup/_version.py',
   'PYMODULE'),
  ('fastapi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/__init__.py',
   'PYMODULE'),
  ('fastapi._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/_compat.py',
   'PYMODULE'),
  ('fastapi.applications',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/applications.py',
   'PYMODULE'),
  ('fastapi.background',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/background.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/concurrency.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/datastructures.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/dependencies/__init__.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/dependencies/models.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/dependencies/utils.py',
   'PYMODULE'),
  ('fastapi.encoders',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/encoders.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/exception_handlers.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/exceptions.py',
   'PYMODULE'),
  ('fastapi.logger',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/logger.py',
   'PYMODULE'),
  ('fastapi.middleware',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/middleware/__init__.py',
   'PYMODULE'),
  ('fastapi.middleware.cors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/middleware/cors.py',
   'PYMODULE'),
  ('fastapi.openapi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/openapi/__init__.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/openapi/constants.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/openapi/docs.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/openapi/models.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/openapi/utils.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/param_functions.py',
   'PYMODULE'),
  ('fastapi.params',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/params.py',
   'PYMODULE'),
  ('fastapi.requests',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/requests.py',
   'PYMODULE'),
  ('fastapi.responses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/responses.py',
   'PYMODULE'),
  ('fastapi.routing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/routing.py',
   'PYMODULE'),
  ('fastapi.security',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/security/__init__.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/security/api_key.py',
   'PYMODULE'),
  ('fastapi.security.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/security/base.py',
   'PYMODULE'),
  ('fastapi.security.http',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/security/http.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/security/oauth2.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/security/open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/security/utils.py',
   'PYMODULE'),
  ('fastapi.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/types.py',
   'PYMODULE'),
  ('fastapi.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/utils.py',
   'PYMODULE'),
  ('fastapi.websockets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/fastapi/websockets.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gettext.py',
   'PYMODULE'),
  ('glob',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/glob.py',
   'PYMODULE'),
  ('gunicorn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/__init__.py',
   'PYMODULE'),
  ('gunicorn.arbiter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/arbiter.py',
   'PYMODULE'),
  ('gunicorn.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/errors.py',
   'PYMODULE'),
  ('gunicorn.http',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/http/__init__.py',
   'PYMODULE'),
  ('gunicorn.http.body',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/http/body.py',
   'PYMODULE'),
  ('gunicorn.http.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/http/errors.py',
   'PYMODULE'),
  ('gunicorn.http.message',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/http/message.py',
   'PYMODULE'),
  ('gunicorn.http.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/http/parser.py',
   'PYMODULE'),
  ('gunicorn.http.unreader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/http/unreader.py',
   'PYMODULE'),
  ('gunicorn.http.wsgi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/http/wsgi.py',
   'PYMODULE'),
  ('gunicorn.pidfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/pidfile.py',
   'PYMODULE'),
  ('gunicorn.reloader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/reloader.py',
   'PYMODULE'),
  ('gunicorn.sock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/sock.py',
   'PYMODULE'),
  ('gunicorn.systemd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/systemd.py',
   'PYMODULE'),
  ('gunicorn.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/util.py',
   'PYMODULE'),
  ('gunicorn.workers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/workers/__init__.py',
   'PYMODULE'),
  ('gunicorn.workers.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/workers/base.py',
   'PYMODULE'),
  ('gunicorn.workers.workertmp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/gunicorn/workers/workertmp.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/gzip.py',
   'PYMODULE'),
  ('h11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_connection.py',
   'PYMODULE'),
  ('h11._events',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_events.py',
   'PYMODULE'),
  ('h11._headers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_headers.py',
   'PYMODULE'),
  ('h11._readers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_state.py',
   'PYMODULE'),
  ('h11._util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_util.py',
   'PYMODULE'),
  ('h11._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_version.py',
   'PYMODULE'),
  ('h11._writers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/h11/_writers.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/hmac.py',
   'PYMODULE'),
  ('html',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/html/entities.py',
   'PYMODULE'),
  ('http',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/cookies.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/http/server.py',
   'PYMODULE'),
  ('httpcore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_api.py',
   'PYMODULE'),
  ('httpcore._async',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_async/socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_backends/__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_backends/anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_backends/auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_backends/base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_backends/mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_backends/sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_backends/trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_sync/socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpcore/_utils.py',
   'PYMODULE'),
  ('httpx',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/__version__.py',
   'PYMODULE'),
  ('httpx._api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_api.py',
   'PYMODULE'),
  ('httpx._auth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_auth.py',
   'PYMODULE'),
  ('httpx._client',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_client.py',
   'PYMODULE'),
  ('httpx._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_compat.py',
   'PYMODULE'),
  ('httpx._config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_config.py',
   'PYMODULE'),
  ('httpx._content',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_main.py',
   'PYMODULE'),
  ('httpx._models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_transports/__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_transports/asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_transports/base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_transports/default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_transports/mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_transports/wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/httpx/_utils.py',
   'PYMODULE'),
  ('idna',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('imp',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/imp.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/_common.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/resources.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/util.py',
   'PYMODULE'),
  ('importlib_metadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/importlib_metadata/_text.py',
   'PYMODULE'),
  ('iniconfig',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/iniconfig/__init__.py',
   'PYMODULE'),
  ('iniconfig._parse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/iniconfig/_parse.py',
   'PYMODULE'),
  ('iniconfig.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/iniconfig/exceptions.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ipaddress.py',
   'PYMODULE'),
  ('json',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/__init__.py',
   'PYMODULE'),
  ('logging.config',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/config.py',
   'PYMODULE'),
  ('logging.handlers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/logging/handlers.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lzma.py',
   'PYMODULE'),
  ('markdown_it',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/__init__.py',
   'PYMODULE'),
  ('markdown_it._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/_compat.py',
   'PYMODULE'),
  ('markdown_it._punycode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/_punycode.py',
   'PYMODULE'),
  ('markdown_it.common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/common/__init__.py',
   'PYMODULE'),
  ('markdown_it.common.entities',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/common/entities.py',
   'PYMODULE'),
  ('markdown_it.common.html_blocks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/common/html_blocks.py',
   'PYMODULE'),
  ('markdown_it.common.html_re',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/common/html_re.py',
   'PYMODULE'),
  ('markdown_it.common.normalize_url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/common/normalize_url.py',
   'PYMODULE'),
  ('markdown_it.common.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/common/utils.py',
   'PYMODULE'),
  ('markdown_it.helpers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/helpers/__init__.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_destination',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/helpers/parse_link_destination.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_label',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/helpers/parse_link_label.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_title',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/helpers/parse_link_title.py',
   'PYMODULE'),
  ('markdown_it.main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/main.py',
   'PYMODULE'),
  ('markdown_it.parser_block',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/parser_block.py',
   'PYMODULE'),
  ('markdown_it.parser_core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/parser_core.py',
   'PYMODULE'),
  ('markdown_it.parser_inline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/parser_inline.py',
   'PYMODULE'),
  ('markdown_it.presets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/presets/__init__.py',
   'PYMODULE'),
  ('markdown_it.presets.commonmark',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/presets/commonmark.py',
   'PYMODULE'),
  ('markdown_it.presets.default',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/presets/default.py',
   'PYMODULE'),
  ('markdown_it.presets.zero',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/presets/zero.py',
   'PYMODULE'),
  ('markdown_it.renderer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/renderer.py',
   'PYMODULE'),
  ('markdown_it.ruler',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/ruler.py',
   'PYMODULE'),
  ('markdown_it.rules_block',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_block.blockquote',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/blockquote.py',
   'PYMODULE'),
  ('markdown_it.rules_block.code',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/code.py',
   'PYMODULE'),
  ('markdown_it.rules_block.fence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/fence.py',
   'PYMODULE'),
  ('markdown_it.rules_block.heading',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/heading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.hr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/hr.py',
   'PYMODULE'),
  ('markdown_it.rules_block.html_block',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/html_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.lheading',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/lheading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.list',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/list.py',
   'PYMODULE'),
  ('markdown_it.rules_block.paragraph',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/paragraph.py',
   'PYMODULE'),
  ('markdown_it.rules_block.reference',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/reference.py',
   'PYMODULE'),
  ('markdown_it.rules_block.state_block',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/state_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_block/table.py',
   'PYMODULE'),
  ('markdown_it.rules_core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_core.block',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/block.py',
   'PYMODULE'),
  ('markdown_it.rules_core.inline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/inline.py',
   'PYMODULE'),
  ('markdown_it.rules_core.linkify',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_core.normalize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/normalize.py',
   'PYMODULE'),
  ('markdown_it.rules_core.replacements',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/replacements.py',
   'PYMODULE'),
  ('markdown_it.rules_core.smartquotes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/smartquotes.py',
   'PYMODULE'),
  ('markdown_it.rules_core.state_core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/state_core.py',
   'PYMODULE'),
  ('markdown_it.rules_core.text_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_core/text_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.autolink',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/autolink.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.backticks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/backticks.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.balance_pairs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/balance_pairs.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.emphasis',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/emphasis.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.entity',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/entity.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.escape',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/escape.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.fragments_join',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/fragments_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.html_inline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/html_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.image',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/image.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.link',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/link.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.linkify',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.newline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/newline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.state_inline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/state_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.strikethrough',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/strikethrough.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/rules_inline/text.py',
   'PYMODULE'),
  ('markdown_it.token',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/token.py',
   'PYMODULE'),
  ('markdown_it.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/markdown_it/utils.py',
   'PYMODULE'),
  ('mdurl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mdurl/__init__.py',
   'PYMODULE'),
  ('mdurl._decode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mdurl/_decode.py',
   'PYMODULE'),
  ('mdurl._encode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mdurl/_encode.py',
   'PYMODULE'),
  ('mdurl._format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mdurl/_format.py',
   'PYMODULE'),
  ('mdurl._parse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mdurl/_parse.py',
   'PYMODULE'),
  ('mdurl._url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mdurl/_url.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/mimetypes.py',
   'PYMODULE'),
  ('multipart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/multipart/__init__.py',
   'PYMODULE'),
  ('multipart.multipart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/multipart/multipart.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/util.py',
   'PYMODULE'),
  ('mypy.bogus_type',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mypy/bogus_type.py',
   'PYMODULE'),
  ('mypy.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mypy/version.py',
   'PYMODULE'),
  ('mypy_extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mypy_extensions.py',
   'PYMODULE'),
  ('mysql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/__init__.py',
   'PYMODULE'),
  ('mysql.connector',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/__init__.py',
   'PYMODULE'),
  ('mysql.connector.abstracts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/abstracts.py',
   'PYMODULE'),
  ('mysql.connector.authentication',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/authentication.py',
   'PYMODULE'),
  ('mysql.connector.charsets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/charsets.py',
   'PYMODULE'),
  ('mysql.connector.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/connection.py',
   'PYMODULE'),
  ('mysql.connector.connection_cext',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/connection_cext.py',
   'PYMODULE'),
  ('mysql.connector.constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/constants.py',
   'PYMODULE'),
  ('mysql.connector.conversion',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/conversion.py',
   'PYMODULE'),
  ('mysql.connector.cursor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/cursor.py',
   'PYMODULE'),
  ('mysql.connector.cursor_cext',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/cursor_cext.py',
   'PYMODULE'),
  ('mysql.connector.custom_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/custom_types.py',
   'PYMODULE'),
  ('mysql.connector.dbapi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/dbapi.py',
   'PYMODULE'),
  ('mysql.connector.errorcode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/errorcode.py',
   'PYMODULE'),
  ('mysql.connector.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/errors.py',
   'PYMODULE'),
  ('mysql.connector.locales',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/locales/__init__.py',
   'PYMODULE'),
  ('mysql.connector.network',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/network.py',
   'PYMODULE'),
  ('mysql.connector.optionfiles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/optionfiles.py',
   'PYMODULE'),
  ('mysql.connector.pooling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/pooling.py',
   'PYMODULE'),
  ('mysql.connector.protocol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/protocol.py',
   'PYMODULE'),
  ('mysql.connector.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/utils.py',
   'PYMODULE'),
  ('mysql.connector.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/mysql/connector/version.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/opcode.py',
   'PYMODULE'),
  ('optparse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/optparse.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/packaging/version.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pathlib.py',
   'PYMODULE'),
  ('pdb',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pdb.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/packaging/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/_vendor/pyparsing.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/pkg_resources/extern/__init__.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/plistlib.py',
   'PYMODULE'),
  ('pluggy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/__init__.py',
   'PYMODULE'),
  ('pluggy._callers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/_callers.py',
   'PYMODULE'),
  ('pluggy._hooks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/_hooks.py',
   'PYMODULE'),
  ('pluggy._manager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/_manager.py',
   'PYMODULE'),
  ('pluggy._result',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/_result.py',
   'PYMODULE'),
  ('pluggy._tracing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/_tracing.py',
   'PYMODULE'),
  ('pluggy._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/_version.py',
   'PYMODULE'),
  ('pluggy._warnings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pluggy/_warnings.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pprint.py',
   'PYMODULE'),
  ('psutil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('psutil._compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psutil/_compat.py',
   'PYMODULE'),
  ('psutil._psosx',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psutil/_psosx.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('psycopg2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psycopg2/__init__.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psycopg2/_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psycopg2/_json.py',
   'PYMODULE'),
  ('psycopg2._range',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psycopg2/_range.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psycopg2/extensions.py',
   'PYMODULE'),
  ('psycopg2.extras',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psycopg2/extras.py',
   'PYMODULE'),
  ('psycopg2.sql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/psycopg2/sql.py',
   'PYMODULE'),
  ('pty',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pty.py',
   'PYMODULE'),
  ('py',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/py.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/py_compile.py',
   'PYMODULE'),
  ('pycparser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/ply/__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/ply/lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/ply/yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pycparser/yacctab.py',
   'PYMODULE'),
  ('pydantic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_generics.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._std_types_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_std_types_schema.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_internal/_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/alias_generators.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/color.py',
   'PYMODULE'),
  ('pydantic.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/deprecated/tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/errors.py',
   'PYMODULE'),
  ('pydantic.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/generics.py',
   'PYMODULE'),
  ('pydantic.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/plugin/__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/plugin/_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/plugin/_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/types.py',
   'PYMODULE'),
  ('pydantic.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/v1/version.py',
   'PYMODULE'),
  ('pydantic.validate_call',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/validate_call.py',
   'PYMODULE'),
  ('pydantic.validators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/validators.py',
   'PYMODULE'),
  ('pydantic.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic/warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic_core/__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic_core/core_schema.py',
   'PYMODULE'),
  ('pydantic_settings',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic_settings/__init__.py',
   'PYMODULE'),
  ('pydantic_settings.main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic_settings/main.py',
   'PYMODULE'),
  ('pydantic_settings.sources',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic_settings/sources.py',
   'PYMODULE'),
  ('pydantic_settings.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic_settings/utils.py',
   'PYMODULE'),
  ('pydantic_settings.version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pydantic_settings/version.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/pydoc_data/topics.py',
   'PYMODULE'),
  ('pygments',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/__init__.py',
   'PYMODULE'),
  ('pygments.console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/console.py',
   'PYMODULE'),
  ('pygments.filter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/filter.py',
   'PYMODULE'),
  ('pygments.filters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/filters/__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/formatters/terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/lexers/zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/scanner.py',
   'PYMODULE'),
  ('pygments.style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/style.py',
   'PYMODULE'),
  ('pygments.styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/styles/zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/token.py',
   'PYMODULE'),
  ('pygments.unistring',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/unistring.py',
   'PYMODULE'),
  ('pygments.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pygments/util.py',
   'PYMODULE'),
  ('pymysql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/constants/__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/constants/CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/constants/COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/constants/CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/constants/ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/constants/FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/constants/SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pymysql/times.py',
   'PYMODULE'),
  ('python_multipart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/python_multipart/__init__.py',
   'PYMODULE'),
  ('python_multipart.decoders',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/python_multipart/decoders.py',
   'PYMODULE'),
  ('python_multipart.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/python_multipart/exceptions.py',
   'PYMODULE'),
  ('python_multipart.multipart',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/python_multipart/multipart.py',
   'PYMODULE'),
  ('queue',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/queue.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/random.py',
   'PYMODULE'),
  ('requests',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('requests.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.auth',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests.certs',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/certs.py',
   'PYMODULE'),
  ('requests.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/compat.py',
   'PYMODULE'),
  ('requests.cookies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.models',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/models.py',
   'PYMODULE'),
  ('requests.packages',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/packages.py',
   'PYMODULE'),
  ('requests.sessions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/requests/utils.py',
   'PYMODULE'),
  ('rich',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/__init__.py',
   'PYMODULE'),
  ('rich.__main__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/__main__.py',
   'PYMODULE'),
  ('rich._cell_widths',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_cell_widths.py',
   'PYMODULE'),
  ('rich._emoji_codes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_emoji_codes.py',
   'PYMODULE'),
  ('rich._emoji_replace',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_emoji_replace.py',
   'PYMODULE'),
  ('rich._export_format',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_export_format.py',
   'PYMODULE'),
  ('rich._extension',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_extension.py',
   'PYMODULE'),
  ('rich._fileno',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_fileno.py',
   'PYMODULE'),
  ('rich._inspect',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_inspect.py',
   'PYMODULE'),
  ('rich._log_render',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_log_render.py',
   'PYMODULE'),
  ('rich._loop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_loop.py',
   'PYMODULE'),
  ('rich._null_file',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_null_file.py',
   'PYMODULE'),
  ('rich._palettes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_palettes.py',
   'PYMODULE'),
  ('rich._pick',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_pick.py',
   'PYMODULE'),
  ('rich._ratio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_ratio.py',
   'PYMODULE'),
  ('rich._spinners',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_spinners.py',
   'PYMODULE'),
  ('rich._stack',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_stack.py',
   'PYMODULE'),
  ('rich._timer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_timer.py',
   'PYMODULE'),
  ('rich._win32_console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_win32_console.py',
   'PYMODULE'),
  ('rich._windows',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_windows.py',
   'PYMODULE'),
  ('rich._windows_renderer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_windows_renderer.py',
   'PYMODULE'),
  ('rich._wrap',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/_wrap.py',
   'PYMODULE'),
  ('rich.abc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/abc.py',
   'PYMODULE'),
  ('rich.align',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/align.py',
   'PYMODULE'),
  ('rich.ansi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/ansi.py',
   'PYMODULE'),
  ('rich.box',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/box.py',
   'PYMODULE'),
  ('rich.cells',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/cells.py',
   'PYMODULE'),
  ('rich.color',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/color.py',
   'PYMODULE'),
  ('rich.color_triplet',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/color_triplet.py',
   'PYMODULE'),
  ('rich.columns',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/columns.py',
   'PYMODULE'),
  ('rich.console',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/console.py',
   'PYMODULE'),
  ('rich.constrain',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/constrain.py',
   'PYMODULE'),
  ('rich.containers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/containers.py',
   'PYMODULE'),
  ('rich.control',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/control.py',
   'PYMODULE'),
  ('rich.default_styles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/default_styles.py',
   'PYMODULE'),
  ('rich.emoji',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/emoji.py',
   'PYMODULE'),
  ('rich.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/errors.py',
   'PYMODULE'),
  ('rich.file_proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/file_proxy.py',
   'PYMODULE'),
  ('rich.filesize',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/filesize.py',
   'PYMODULE'),
  ('rich.highlighter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/highlighter.py',
   'PYMODULE'),
  ('rich.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/json.py',
   'PYMODULE'),
  ('rich.jupyter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/jupyter.py',
   'PYMODULE'),
  ('rich.live',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/live.py',
   'PYMODULE'),
  ('rich.live_render',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/live_render.py',
   'PYMODULE'),
  ('rich.markdown',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/markdown.py',
   'PYMODULE'),
  ('rich.markup',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/markup.py',
   'PYMODULE'),
  ('rich.measure',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/measure.py',
   'PYMODULE'),
  ('rich.padding',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/padding.py',
   'PYMODULE'),
  ('rich.pager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/pager.py',
   'PYMODULE'),
  ('rich.palette',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/palette.py',
   'PYMODULE'),
  ('rich.panel',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/panel.py',
   'PYMODULE'),
  ('rich.pretty',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/pretty.py',
   'PYMODULE'),
  ('rich.progress',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/progress.py',
   'PYMODULE'),
  ('rich.progress_bar',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/progress_bar.py',
   'PYMODULE'),
  ('rich.protocol',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/protocol.py',
   'PYMODULE'),
  ('rich.region',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/region.py',
   'PYMODULE'),
  ('rich.repr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/repr.py',
   'PYMODULE'),
  ('rich.rule',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/rule.py',
   'PYMODULE'),
  ('rich.scope',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/scope.py',
   'PYMODULE'),
  ('rich.screen',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/screen.py',
   'PYMODULE'),
  ('rich.segment',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/segment.py',
   'PYMODULE'),
  ('rich.spinner',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/spinner.py',
   'PYMODULE'),
  ('rich.status',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/status.py',
   'PYMODULE'),
  ('rich.style',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/style.py',
   'PYMODULE'),
  ('rich.styled',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/styled.py',
   'PYMODULE'),
  ('rich.syntax',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/syntax.py',
   'PYMODULE'),
  ('rich.table',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/table.py',
   'PYMODULE'),
  ('rich.terminal_theme',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/terminal_theme.py',
   'PYMODULE'),
  ('rich.text',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/text.py',
   'PYMODULE'),
  ('rich.theme',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/theme.py',
   'PYMODULE'),
  ('rich.themes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/themes.py',
   'PYMODULE'),
  ('rich.traceback',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/rich/traceback.py',
   'PYMODULE'),
  ('rlcompleter',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/selectors.py',
   'PYMODULE'),
  ('setuptools',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools._vendor',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/_typing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/_vendor/pyparsing.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/command/py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/config.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/extern/__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/signal.py',
   'PYMODULE'),
  ('site',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/site.py',
   'PYMODULE'),
  ('smtplib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/smtplib.py',
   'PYMODULE'),
  ('sniffio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sniffio/__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sniffio/_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sniffio/_version.py',
   'PYMODULE'),
  ('socket',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/socketserver.py',
   'PYMODULE'),
  ('socks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/socks.py',
   'PYMODULE'),
  ('sqlalchemy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/connectors/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/connectors/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/cyextension/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mssql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mssql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mssql/information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mssql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mssql/pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mssql/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/mysql/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/oracle/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/oracle/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/oracle/cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/oracle/dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/oracle/oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/oracle/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/postgresql/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/sqlite/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/sqlite/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/sqlite/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/sqlite/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/sqlite/pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/dialects/sqlite/pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/engine/util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/event/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/event/api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/event/attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/event/base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/event/legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/event/registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/asyncio/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/asyncio/base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/asyncio/engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/asyncio/exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/asyncio/result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/asyncio/scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/asyncio/session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/compiler.py',
   'PYMODULE'),
  ('sqlalchemy.ext.declarative',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/declarative/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.declarative.extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/ext/declarative/extensions.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/future/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/future/engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/orm/writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/pool/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/pool/base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/pool/events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/pool/impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/sql/visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/sqlalchemy/util/typing.py',
   'PYMODULE'),
  ('sqlite3',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sqlite3/dump.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py',
   'PYMODULE'),
  ('starlette',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/__init__.py',
   'PYMODULE'),
  ('starlette._exception_handler',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/_exception_handler.py',
   'PYMODULE'),
  ('starlette._utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/_utils.py',
   'PYMODULE'),
  ('starlette.applications',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/applications.py',
   'PYMODULE'),
  ('starlette.background',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/background.py',
   'PYMODULE'),
  ('starlette.concurrency',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/concurrency.py',
   'PYMODULE'),
  ('starlette.convertors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/convertors.py',
   'PYMODULE'),
  ('starlette.datastructures',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/datastructures.py',
   'PYMODULE'),
  ('starlette.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/exceptions.py',
   'PYMODULE'),
  ('starlette.formparsers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/formparsers.py',
   'PYMODULE'),
  ('starlette.middleware',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/middleware/__init__.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/middleware/base.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/middleware/cors.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/middleware/errors.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/middleware/exceptions.py',
   'PYMODULE'),
  ('starlette.requests',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/requests.py',
   'PYMODULE'),
  ('starlette.responses',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/responses.py',
   'PYMODULE'),
  ('starlette.routing',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/routing.py',
   'PYMODULE'),
  ('starlette.status',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/status.py',
   'PYMODULE'),
  ('starlette.types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/types.py',
   'PYMODULE'),
  ('starlette.websockets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/starlette/websockets.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/threading.py',
   'PYMODULE'),
  ('token',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tokenize.py',
   'PYMODULE'),
  ('tomli',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/tomli/__init__.py',
   'PYMODULE'),
  ('tomli._parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/tomli/_parser.py',
   'PYMODULE'),
  ('tomli._re',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/tomli/_re.py',
   'PYMODULE'),
  ('tomli._types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/tomli/_types.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tracemalloc.py',
   'PYMODULE'),
  ('tty',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/tty.py',
   'PYMODULE'),
  ('typing',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/urllib/response.py',
   'PYMODULE'),
  ('urllib3',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/emscripten/response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/http2/probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('uu',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uu.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/uuid.py',
   'PYMODULE'),
  ('uvicorn',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/__init__.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/__main__.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/_subprocess.py',
   'PYMODULE'),
  ('uvicorn._types',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/_types.py',
   'PYMODULE'),
  ('uvicorn.config',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/config.py',
   'PYMODULE'),
  ('uvicorn.importer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/importer.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/lifespan/__init__.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/lifespan/off.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/lifespan/on.py',
   'PYMODULE'),
  ('uvicorn.logging',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/logging.py',
   'PYMODULE'),
  ('uvicorn.loops',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/loops/__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/loops/asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/loops/auto.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/loops/uvloop.py',
   'PYMODULE'),
  ('uvicorn.main',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/main.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/middleware/__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/middleware/asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/middleware/message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/middleware/proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/middleware/wsgi.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/http/__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/http/auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/http/flow_control.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/http/h11_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/http/httptools_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/websockets/__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/websockets/auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/websockets/websockets_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_sansio_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/websockets/websockets_sansio_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/protocols/websockets/wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.server',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/server.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/supervisors/__init__.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/supervisors/basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/supervisors/multiprocess.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/supervisors/statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/supervisors/watchfilesreload.py',
   'PYMODULE'),
  ('uvicorn.workers',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/uvicorn/workers.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/xmlrpc/client.py',
   'PYMODULE'),
  ('yaml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/emitter.py',
   'PYMODULE'),
  ('yaml.error',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/error.py',
   'PYMODULE'),
  ('yaml.events',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/events.py',
   'PYMODULE'),
  ('yaml.loader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/parser.py',
   'PYMODULE'),
  ('yaml.reader',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/reader.py',
   'PYMODULE'),
  ('yaml.representer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/yaml/tokens.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipfile.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/zipimport.py',
   'PYMODULE'),
  ('zipp',
   '/Users/<USER>/Library/Python/3.9/lib/python/site-packages/zipp.py',
   'PYMODULE')])
