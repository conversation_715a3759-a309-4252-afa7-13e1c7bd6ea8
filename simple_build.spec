# -*- mode: python ; coding: utf-8 -*-

"""
兼职接单管理系统 - 简化的 PyInstaller 配置文件
"""

import os
import sys

# 应用程序信息
APP_NAME = "兼职接单管理系统"

# 主程序入口
main_script = "main.py"

# 数据文件
datas = [
    ('data/database.db', 'data'),
    ('app', 'app'),
    ('data', 'data'),
    ('uploads', 'uploads'),
]

# 隐藏导入的模块
hiddenimports = [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'fastapi',
    'uvicorn',
    'uvicorn.main',
    'sqlalchemy',
    'sqlalchemy.ext.declarative',
    'pydantic',
    'requests',
    'psutil',
    'app.core',
    'app.core.database',
    'app.core.config',
    'app.models',
    'app.backend',
    'app.backend.main',
    'app.frontend',
    'app.frontend.ui',
    'app.frontend.controllers',
    'app.utils',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'test',
    'tests',
    'unittest',
]

# 分析配置
a = Analysis(
    [main_script],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=APP_NAME,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# macOS 应用程序包
if sys.platform == 'darwin':
    app = BUNDLE(
        exe,
        name=f'{APP_NAME}.app',
        icon=None,
        bundle_identifier=f'com.freelance.management.app',
        version='1.0.0',
    )
