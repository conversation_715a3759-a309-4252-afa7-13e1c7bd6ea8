#!/usr/bin/env python3
"""
兼职接单管理系统 - 自动化打包脚本

这个脚本用于自动化构建和打包应用程序
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AppBuilder:
    """应用程序构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        self.spec_file = self.project_root / "build_config.spec"
        self.app_name = "兼职接单管理系统"
        self.version = "1.0.0"
        
    def clean_build_dirs(self):
        """清理构建目录"""
        logger.info("清理构建目录...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                logger.info(f"已删除目录: {dir_path}")
        
        # 清理 __pycache__ 目录
        for pycache_dir in self.project_root.rglob("__pycache__"):
            shutil.rmtree(pycache_dir)
            logger.info(f"已删除缓存目录: {pycache_dir}")
    
    def check_dependencies(self):
        """检查依赖项"""
        logger.info("检查依赖项...")
        
        required_packages = [
            'PyQt6',
            'fastapi',
            'uvicorn',
            'sqlalchemy',
            'pydantic',
            'requests',
            'psutil',
            'pyinstaller'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.lower().replace('-', '_'))
                logger.info(f"✓ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"✗ {package} 未安装")
        
        if missing_packages:
            logger.error(f"缺少依赖项: {', '.join(missing_packages)}")
            logger.info("请运行: pip install " + " ".join(missing_packages))
            return False
        
        logger.info("所有依赖项检查通过")
        return True
    
    def prepare_resources(self):
        """准备资源文件"""
        logger.info("准备资源文件...")
        
        # 确保必要的目录存在
        dirs_to_create = [
            self.project_root / "data",
            self.project_root / "uploads",
            self.project_root / "app" / "frontend" / "resources"
        ]
        
        for dir_path in dirs_to_create:
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"确保目录存在: {dir_path}")
        
        # 检查数据库文件
        db_file = self.project_root / "freelance_management.db"
        if not db_file.exists():
            logger.warning(f"数据库文件不存在: {db_file}")
            logger.info("将在运行时创建新的数据库文件")
        else:
            logger.info(f"数据库文件存在: {db_file}")
    
    def build_application(self):
        """构建应用程序"""
        logger.info("开始构建应用程序...")
        
        # 构建命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            str(self.spec_file)
        ]
        
        logger.info(f"执行构建命令: {' '.join(cmd)}")
        
        try:
            # 执行构建
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            if result.returncode == 0:
                logger.info("应用程序构建成功!")
                logger.info("构建输出:")
                logger.info(result.stdout)
                return True
            else:
                logger.error("应用程序构建失败!")
                logger.error("错误输出:")
                logger.error(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("构建超时!")
            return False
        except Exception as e:
            logger.error(f"构建过程中发生错误: {e}")
            return False
    
    def post_build_tasks(self):
        """构建后任务"""
        logger.info("执行构建后任务...")
        
        # 检查构建结果
        if platform.system() == "Darwin":  # macOS
            app_path = self.dist_dir / f"{self.app_name}.app"
            if app_path.exists():
                logger.info(f"macOS 应用程序包创建成功: {app_path}")
                
                # 创建 DMG 文件（可选）
                self.create_dmg(app_path)
            else:
                logger.error("macOS 应用程序包创建失败")
                return False
        else:
            exe_path = self.dist_dir / self.app_name / f"{self.app_name}.exe"
            if exe_path.exists():
                logger.info(f"可执行文件创建成功: {exe_path}")
            else:
                logger.error("可执行文件创建失败")
                return False
        
        # 复制额外文件
        self.copy_additional_files()
        
        return True
    
    def create_dmg(self, app_path):
        """创建 macOS DMG 文件"""
        try:
            dmg_name = f"{self.app_name}-{self.version}.dmg"
            dmg_path = self.dist_dir / dmg_name
            
            # 删除已存在的 DMG 文件
            if dmg_path.exists():
                dmg_path.unlink()
            
            # 创建 DMG 命令
            cmd = [
                "hdiutil", "create",
                "-volname", self.app_name,
                "-srcfolder", str(app_path),
                "-ov", "-format", "UDZO",
                str(dmg_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"DMG 文件创建成功: {dmg_path}")
            else:
                logger.warning(f"DMG 文件创建失败: {result.stderr}")
                
        except Exception as e:
            logger.warning(f"创建 DMG 文件时发生错误: {e}")
    
    def copy_additional_files(self):
        """复制额外文件"""
        logger.info("复制额外文件...")
        
        # 要复制的文件
        files_to_copy = [
            ("README.md", "使用说明.md"),
            ("requirements.txt", "依赖列表.txt"),
            ("FINAL_TEST_REPORT.md", "测试报告.md")
        ]
        
        for src_name, dst_name in files_to_copy:
            src_path = self.project_root / src_name
            if src_path.exists():
                dst_path = self.dist_dir / dst_name
                shutil.copy2(src_path, dst_path)
                logger.info(f"已复制文件: {src_name} -> {dst_name}")
    
    def generate_build_info(self):
        """生成构建信息文件"""
        logger.info("生成构建信息...")
        
        build_info = f"""# 兼职接单管理系统 - 构建信息

## 应用程序信息
- 名称: {self.app_name}
- 版本: {self.version}
- 构建时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 构建平台: {platform.system()} {platform.release()}
- Python 版本: {sys.version}

## 构建配置
- PyInstaller 版本: {subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], capture_output=True, text=True).stdout.strip()}
- 构建模式: 单文件模式
- 控制台: 隐藏
- UPX 压缩: 启用

## 安装说明

### macOS
1. 双击 `{self.app_name}.app` 启动应用程序
2. 如果遇到安全提示，请在系统偏好设置 > 安全性与隐私中允许运行

### Windows
1. 双击 `{self.app_name}.exe` 启动应用程序
2. 如果遇到 Windows Defender 提示，请选择"仍要运行"

### Linux
1. 在终端中运行: `./{self.app_name}`
2. 确保文件具有执行权限: `chmod +x {self.app_name}`

## 功能特性
- 订单管理: 创建、编辑、跟踪订单状态
- 客户管理: 管理客户信息和联系方式
- 工时记录: 自动计时和工时统计
- 统计分析: 收入分析和业务报表
- 文件管理: 上传和管理项目文件
- 系统设置: 个性化配置选项

## 技术支持
如有问题，请查看使用说明或联系技术支持。
"""
        
        build_info_path = self.dist_dir / "构建信息.md"
        with open(build_info_path, 'w', encoding='utf-8') as f:
            f.write(build_info)
        
        logger.info(f"构建信息已保存: {build_info_path}")
    
    def build(self):
        """执行完整的构建流程"""
        logger.info("=" * 60)
        logger.info(f"开始构建 {self.app_name} v{self.version}")
        logger.info("=" * 60)
        
        try:
            # 1. 清理构建目录
            self.clean_build_dirs()
            
            # 2. 检查依赖项
            if not self.check_dependencies():
                return False
            
            # 3. 准备资源文件
            self.prepare_resources()
            
            # 4. 构建应用程序
            if not self.build_application():
                return False
            
            # 5. 构建后任务
            if not self.post_build_tasks():
                return False
            
            # 6. 生成构建信息
            self.generate_build_info()
            
            logger.info("=" * 60)
            logger.info("构建完成!")
            logger.info(f"输出目录: {self.dist_dir}")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"构建过程中发生未知错误: {e}")
            return False

def main():
    """主函数"""
    builder = AppBuilder()
    success = builder.build()
    
    if success:
        print("\n🎉 应用程序构建成功!")
        print(f"📁 输出目录: {builder.dist_dir}")
        sys.exit(0)
    else:
        print("\n❌ 应用程序构建失败!")
        print("📋 请查看 build.log 获取详细信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
