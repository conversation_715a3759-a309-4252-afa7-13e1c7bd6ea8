# 兼职接单管理系统

## 项目概述

兼职接单管理系统是一个专为程序员兼职接单设计的全流程管理工具，支持代码开发、论文写作、内容创作等多种项目类型。系统提供订单管理、客户维护、收入统计、工时跟踪等核心功能，帮助提高接单效率和收入管理水平。

**核心价值：**
- 🎯 **全流程管理**：从订单录入到项目交付的完整生命周期管理
- 📊 **数据化决策**：基于历史数据分析，优化接单策略和报价
- 💰 **收入优化**：多维度收入统计，识别高价值客户和项目类型
- ⏰ **时间管理**：精确工时记录，提升时间利用效率
- 🔒 **数据安全**：本地存储，保护客户隐私和商业机密

## 系统架构

### 技术架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                    兼职接单管理系统                              │
│  ┌─────────────────────────┐ ┌─────────────────────────────────┐ │
│  │      PyQt6前端界面       │ │        FastAPI后端服务          │ │
│  │  • 订单管理界面          │◄┤  • RESTful API接口             │ │
│  │  • 客户管理界面          │ │  • 业务逻辑处理                 │ │
│  │  • 收入统计界面          │ │  • 数据验证与序列化             │ │
│  │  • 工时记录界面          │ │  • 文件管理服务                 │ │
│  │  • 系统设置界面          │ │  • 统计分析引擎                 │ │
│  └─────────────────────────┘ └─────────────────────────────────┘ │
│           │                                   │                 │
│  ┌─────────────────────────┐ ┌─────────────────────────────────┐ │
│  │     应用控制器           │ │        SQLite数据库             │ │
│  │  • 前后端协调            │◄┤  • 订单数据存储                 │ │
│  │  • 状态同步              │ │  • 客户信息管理                 │ │
│  │  • 文件处理              │ │  • 工时记录存储                 │ │
│  │  • 提醒服务              │ │  • 系统配置管理                 │ │
│  └─────────────────────────┘ └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 核心特性
- **多项目类型支持**：代码开发、论文写作、内容创作、技术咨询
- **智能分类管理**：按项目类型自动适配字段和计费方式
- **实时工时跟踪**：内置计时器，精确记录工作时间
- **收入分析报表**：多维度统计分析，支持Excel导出
- **客户关系管理**：维护客户偏好，促进重复合作
- **截止日期提醒**：系统托盘提醒，避免项目延期
- **模板化操作**：常用项目类型快速创建模板
- **数据安全保护**：本地加密存储，隐私信息保护

## 数据库设计

### 核心数据表结构

| 表名 | 用途 | 主要字段 | 关联关系 |
|:----:|:----:|:--------:|:--------:|
| **orders** | 订单核心信息 | id, title, description, customer_id, category_id, status, price, deadline, created_at | 外键关联customers, categories |
| **customers** | 客户信息管理 | id, name, contact_info, source, preferences, satisfaction_rating, created_at | 一对多关联orders |
| **categories** | 项目分类管理 | id, name, type, billing_method, default_rate, description | 一对多关联orders |
| **work_logs** | 工时记录 | id, order_id, start_time, end_time, duration, description, hourly_rate | 外键关联orders |
| **attachments** | 文件附件 | id, order_id, file_name, file_path, file_type, file_size, upload_time | 外键关联orders |
| **communications** | 沟通记录 | id, order_id, content, communication_type, created_at | 外键关联orders |
| **templates** | 订单模板 | id, name, category_id, template_data, is_active | 外键关联categories |
| **settings** | 系统配置 | id, config_key, config_value, description, category | 无关联 |

### 订单状态流转
```
待开始 → 进行中 → 修改中 → 待验收 → 已完成 → 已结算
   ↓        ↓        ↓        ↓        ↓        ↓
 可取消   可暂停   可重做   可退回   可追加   已归档
```

## 功能模块

### 后端模块架构

| 模块名称 | 功能描述 | 主要API端点 | 核心业务逻辑 |
|:--------:|:--------:|:-----------:|:------------:|
| **订单管理** | 订单CRUD、状态跟踪、查询筛选 | `/api/orders/*` | 订单生命周期管理、状态流转控制、截止日期计算 |
| **客户管理** | 客户信息、历史记录、偏好分析 | `/api/customers/*` | 客户价值分析、满意度统计、合作历史追踪 |
| **项目分类** | 分类管理、模板系统、计费规则 | `/api/categories/*` | 分类统计分析、模板管理、计费方式配置 |
| **工时管理** | 时间记录、计时器、效率分析 | `/api/work-logs/*` | 工时统计计算、时薪分析、工作效率评估 |
| **统计分析** | 收入分析、趋势预测、报表生成 | `/api/analytics/*` | 数据聚合分析、趋势计算、报表数据生成 |
| **文件管理** | 附件上传、版本控制、安全管理 | `/api/files/*` | 文件存储管理、版本控制、安全检查 |
| **系统配置** | 设置管理、数据备份、系统维护 | `/api/settings/*` | 配置项管理、数据备份恢复、系统状态监控 |

### 前端界面设计

| 界面名称 | 界面用途 | 核心组件 | 主要功能 |
|:--------:|:--------:|:--------:|:--------:|
| **主窗口** | 应用框架、导航管理 | QMainWindow, QTabWidget | 标签页导航、菜单管理、状态栏显示 |
| **订单列表** | 订单展示、批量操作 | QTableWidget, QComboBox | 分类筛选、状态更新、批量导出 |
| **订单详情** | 订单编辑、工时记录 | QFormLayout, QTimer | 动态表单、计时器、文件管理 |
| **客户管理** | 客户信息、关系维护 | QTreeWidget, QListWidget | 客户档案、历史查看、偏好设置 |
| **收入统计** | 数据分析、图表展示 | QChart, QTableView | 收入趋势、类型分析、报表导出 |
| **工时记录** | 时间跟踪、效率分析 | QCalendarWidget, QProgressBar | 日历视图、工时统计、效率分析 |
| **系统设置** | 配置管理、主题切换 | QSettings, QStyleSheet | 参数配置、主题管理、数据备份 |

## 技术栈

### 后端技术栈
- **FastAPI** (高性能API框架) - 自动文档生成、数据验证、异步支持
- **SQLAlchemy** (ORM框架) - 数据模型定义、关系映射、查询优化
- **Pydantic** (数据验证) - 类型安全、数据序列化、参数校验
- **SQLite** (内嵌数据库) - 零配置、高性能、事务支持
- **Uvicorn** (ASGI服务器) - 异步处理、高并发支持

### 前端技术栈
- **PyQt6** (GUI框架) - 原生界面、丰富组件、跨平台支持
- **Qt Designer** (界面设计) - 可视化设计、快速原型、样式定制
- **QThread** (多线程处理) - 异步任务、界面响应、后台处理
- **QChart** (图表组件) - 数据可视化、交互图表、统计展示
- **QSettings** (配置管理) - 用户偏好、系统设置、状态保存

### 打包部署
- **PyInstaller** (应用打包) - 单文件打包、依赖自动检测、跨平台支持
- **一键打包脚本** - 自动化构建、资源处理、版本管理
- **独立exe文件** - 零环境依赖、即装即用、绿色部署

## 开发进度跟踪

### 数据库开发状态
- [ ] 数据库设计与ER图
- [ ] SQLAlchemy模型定义
- [ ] 数据库初始化脚本
- [ ] 测试数据准备

### 后端开发状态
- [ ] FastAPI项目框架搭建
- [ ] 订单管理模块API
- [ ] 客户管理模块API
- [ ] 统计分析模块API
- [ ] 工时管理模块API
- [ ] 文件管理模块API
- [ ] 系统配置模块API

### 前端开发状态
- [ ] PyQt6界面框架搭建
- [ ] 订单列表界面开发
- [ ] 订单详情界面开发
- [ ] 客户管理界面开发
- [ ] 收入统计界面开发
- [ ] 工时记录界面开发
- [ ] 系统设置界面开发

### 打包部署状态
- [ ] PyInstaller打包脚本配置
- [ ] 资源文件处理优化
- [ ] 一键打包测试验证
- [ ] 部署文档编写

## 项目目录结构

```
兼职接单管理系统/
├── app/                          # 应用主目录
│   ├── backend/                  # 后端服务
│   │   ├── api/                  # API路由
│   │   │   ├── __init__.py
│   │   │   └── endpoints/        # 具体接口
│   │   │       ├── orders.py     # 订单管理API
│   │   │       ├── customers.py  # 客户管理API
│   │   │       ├── analytics.py  # 统计分析API
│   │   │       └── work_logs.py  # 工时管理API
│   │   ├── models/               # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── database.py       # 数据库配置
│   │   │   ├── order.py          # 订单模型
│   │   │   ├── customer.py       # 客户模型
│   │   │   └── work_log.py       # 工时模型
│   │   ├── services/             # 业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── order_service.py  # 订单业务逻辑
│   │   │   ├── customer_service.py # 客户业务逻辑
│   │   │   └── analytics_service.py # 统计分析逻辑
│   │   ├── schemas/              # Pydantic模型
│   │   │   ├── __init__.py
│   │   │   ├── order.py          # 订单数据模型
│   │   │   ├── customer.py       # 客户数据模型
│   │   │   └── analytics.py      # 统计数据模型
│   │   └── main.py               # FastAPI应用入口
│   ├── frontend/                 # 前端界面
│   │   ├── ui/                   # 界面文件
│   │   │   ├── __init__.py
│   │   │   ├── main_window.py    # 主窗口
│   │   │   ├── order_list.py     # 订单列表
│   │   │   ├── order_detail.py   # 订单详情
│   │   │   ├── customer_manager.py # 客户管理
│   │   │   ├── analytics_view.py # 统计分析
│   │   │   └── settings_view.py  # 系统设置
│   │   ├── widgets/              # 自定义组件
│   │   │   ├── __init__.py
│   │   │   ├── order_table.py    # 订单表格组件
│   │   │   ├── chart_widget.py   # 图表组件
│   │   │   └── timer_widget.py   # 计时器组件
│   │   ├── resources/            # 资源文件
│   │   │   ├── icons/            # 图标文件
│   │   │   ├── styles/           # 样式表
│   │   │   │   ├── dark_theme.qss # 深色主题
│   │   │   │   └── light_theme.qss # 浅色主题
│   │   │   └── ui_files/         # Qt Designer文件
│   │   └── controllers/          # 控制器
│   │       ├── __init__.py
│   │       ├── app_controller.py # 应用控制器
│   │       ├── order_controller.py # 订单控制器
│   │       └── api_client.py     # API客户端
│   ├── core/                     # 核心模块
│   │   ├── __init__.py
│   │   ├── config.py             # 配置管理
│   │   ├── database.py           # 数据库连接
│   │   ├── exceptions.py         # 异常定义
│   │   └── security.py           # 安全模块
│   └── utils/                    # 工具函数
│       ├── __init__.py
│       ├── helpers.py            # 辅助函数
│       ├── validators.py         # 验证器
│       └── file_manager.py       # 文件管理
├── data/                         # 数据目录
│   ├── database.db               # SQLite数据库
│   ├── attachments/              # 附件文件
│   └── logs/                     # 日志文件
├── tests/                        # 测试文件
│   ├── __init__.py
│   ├── test_backend/             # 后端测试
│   └── test_frontend/            # 前端测试
├── build/                        # 打包输出
├── requirements.txt              # Python依赖
├── main.py                       # 应用入口
├── build.py                      # 打包脚本
└── README.md                     # 项目文档
```

## 模块技术方案

### 订单管理模块
*此部分将在模块开发阶段更新详细技术方案*

### 客户管理模块
*此部分将在模块开发阶段更新详细技术方案*

### 统计分析模块
*此部分将在模块开发阶段更新详细技术方案*

### 工时管理模块
*此部分将在模块开发阶段更新详细技术方案*

## 快速启动

### 开发环境
1. 安装Python 3.8+
2. 创建虚拟环境：`python -m venv venv`
3. 激活虚拟环境：`venv\Scripts\activate` (Windows)
4. 安装依赖：`pip install -r requirements.txt`
5. 运行应用：`python main.py`

### 打包部署
1. 运行打包脚本：`python build.py`
2. 在build/dist目录找到exe文件
3. 双击运行，无需安装任何环境

## 使用说明

### 基本操作流程
1. **订单录入**：选择项目类型，填写订单信息，设置截止日期
2. **工时记录**：使用内置计时器记录实际工作时间
3. **进度跟踪**：实时更新订单状态，记录沟通内容
4. **收入分析**：查看收入统计，分析项目盈利情况
5. **客户维护**：记录客户偏好，维护长期合作关系

### 高级功能
- **批量操作**：支持订单批量导入、状态批量更新
- **模板系统**：创建常用项目模板，快速录入订单
- **数据导出**：支持Excel格式导出，便于财务管理
- **提醒功能**：截止日期提醒，避免项目延期
- **主题切换**：支持深色/浅色主题，护眼模式

## 常见问题与解决方案

### 开发环境问题
- **依赖安装失败**：检查Python版本，使用国内镜像源
- **数据库连接错误**：确认SQLite文件权限，检查路径配置
- **界面显示异常**：验证PyQt6安装，检查系统DPI设置

### 使用过程问题
- **数据丢失**：定期备份数据库文件，启用自动备份功能
- **性能问题**：清理历史日志，优化数据库索引
- **文件管理**：规范附件命名，定期整理文件目录

*更多问题解决方案将在开发过程中持续更新*
