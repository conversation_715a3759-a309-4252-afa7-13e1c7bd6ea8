# 兼职接单管理系统

## 项目概述

兼职接单管理系统是一个专为程序员兼职接单设计的全流程管理工具，支持代码开发、论文写作、内容创作等多种项目类型。系统提供订单管理、客户维护、收入统计、工时跟踪等核心功能，帮助提高接单效率和收入管理水平。

**核心价值：**
- 🎯 **全流程管理**：从订单录入到项目交付的完整生命周期管理
- 📊 **数据化决策**：基于历史数据分析，优化接单策略和报价
- 💰 **收入优化**：多维度收入统计，识别高价值客户和项目类型
- ⏰ **时间管理**：精确工时记录，提升时间利用效率
- 🔒 **数据安全**：本地存储，保护客户隐私和商业机密

## 系统架构

### 技术架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                    兼职接单管理系统                              │
│  ┌─────────────────────────┐ ┌─────────────────────────────────┐ │
│  │      PyQt6前端界面       │ │        FastAPI后端服务          │ │
│  │  • 订单管理界面          │◄┤  • RESTful API接口             │ │
│  │  • 客户管理界面          │ │  • 业务逻辑处理                 │ │
│  │  • 收入统计界面          │ │  • 数据验证与序列化             │ │
│  │  • 工时记录界面          │ │  • 文件管理服务                 │ │
│  │  • 系统设置界面          │ │  • 统计分析引擎                 │ │
│  └─────────────────────────┘ └─────────────────────────────────┘ │
│           │                                   │                 │
│  ┌─────────────────────────┐ ┌─────────────────────────────────┐ │
│  │     应用控制器           │ │        SQLite数据库             │ │
│  │  • 前后端协调            │◄┤  • 订单数据存储                 │ │
│  │  • 状态同步              │ │  • 客户信息管理                 │ │
│  │  • 文件处理              │ │  • 工时记录存储                 │ │
│  │  • 提醒服务              │ │  • 系统配置管理                 │ │
│  └─────────────────────────┘ └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 核心特性
- **多项目类型支持**：代码开发、论文写作、内容创作、技术咨询
- **智能分类管理**：按项目类型自动适配字段和计费方式
- **实时工时跟踪**：内置计时器，精确记录工作时间
- **收入分析报表**：多维度统计分析，支持Excel导出
- **客户关系管理**：维护客户偏好，促进重复合作
- **截止日期提醒**：系统托盘提醒，避免项目延期
- **模板化操作**：常用项目类型快速创建模板
- **数据安全保护**：本地加密存储，隐私信息保护

## 数据库设计

### 核心数据表结构

| 表名 | 用途 | 主要字段 | 关联关系 |
|:----:|:----:|:--------:|:--------:|
| **orders** | 订单核心信息 | id, title, description, customer_id, category_id, status, price, deadline, created_at | 外键关联customers, categories |
| **customers** | 客户信息管理 | id, name, contact_info, source, preferences, satisfaction_rating, created_at | 一对多关联orders |
| **categories** | 项目分类管理 | id, name, type, billing_method, default_rate, description | 一对多关联orders |
| **work_logs** | 工时记录 | id, order_id, start_time, end_time, duration, description, hourly_rate | 外键关联orders |
| **attachments** | 文件附件 | id, order_id, file_name, file_path, file_type, file_size, upload_time | 外键关联orders |
| **communications** | 沟通记录 | id, order_id, content, communication_type, created_at | 外键关联orders |
| **templates** | 订单模板 | id, name, category_id, template_data, is_active | 外键关联categories |
| **settings** | 系统配置 | id, config_key, config_value, description, category | 无关联 |

### 订单状态流转
```
待开始 → 进行中 → 修改中 → 待验收 → 已完成 → 已结算
   ↓        ↓        ↓        ↓        ↓        ↓
 可取消   可暂停   可重做   可退回   可追加   已归档
```

## 功能模块

### 后端模块架构

| 模块名称 | 功能描述 | 主要API端点 | 核心业务逻辑 |
|:--------:|:--------:|:-----------:|:------------:|
| **订单管理** | 订单CRUD、状态跟踪、查询筛选 | `/api/orders/*` | 订单生命周期管理、状态流转控制、截止日期计算 |
| **客户管理** | 客户信息、历史记录、偏好分析 | `/api/customers/*` | 客户价值分析、满意度统计、合作历史追踪 |
| **项目分类** | 分类管理、模板系统、计费规则 | `/api/categories/*` | 分类统计分析、模板管理、计费方式配置 |
| **工时管理** | 时间记录、计时器、效率分析 | `/api/work-logs/*` | 工时统计计算、时薪分析、工作效率评估 |
| **统计分析** | 收入分析、趋势预测、报表生成 | `/api/analytics/*` | 数据聚合分析、趋势计算、报表数据生成 |
| **文件管理** | 附件上传、版本控制、安全管理 | `/api/files/*` | 文件存储管理、版本控制、安全检查 |
| **系统配置** | 设置管理、数据备份、系统维护 | `/api/settings/*` | 配置项管理、数据备份恢复、系统状态监控 |

### 前端界面设计

| 界面名称 | 界面用途 | 核心组件 | 主要功能 |
|:--------:|:--------:|:--------:|:--------:|
| **主窗口** | 应用框架、导航管理 | QMainWindow, QTabWidget | 标签页导航、菜单管理、状态栏显示 |
| **订单列表** | 订单展示、批量操作 | QTableWidget, QComboBox | 分类筛选、状态更新、批量导出 |
| **订单详情** | 订单编辑、工时记录 | QFormLayout, QTimer | 动态表单、计时器、文件管理 |
| **客户管理** | 客户信息、关系维护 | QTreeWidget, QListWidget | 客户档案、历史查看、偏好设置 |
| **收入统计** | 数据分析、图表展示 | QChart, QTableView | 收入趋势、类型分析、报表导出 |
| **工时记录** | 时间跟踪、效率分析 | QCalendarWidget, QProgressBar | 日历视图、工时统计、效率分析 |
| **系统设置** | 配置管理、主题切换 | QSettings, QStyleSheet | 参数配置、主题管理、数据备份 |

## 技术栈

### 后端技术栈
- **FastAPI** (高性能API框架) - 自动文档生成、数据验证、异步支持
- **SQLAlchemy** (ORM框架) - 数据模型定义、关系映射、查询优化
- **Pydantic** (数据验证) - 类型安全、数据序列化、参数校验
- **SQLite** (内嵌数据库) - 零配置、高性能、事务支持
- **Uvicorn** (ASGI服务器) - 异步处理、高并发支持

### 前端技术栈
- **PyQt6** (GUI框架) - 原生界面、丰富组件、跨平台支持
- **Qt Designer** (界面设计) - 可视化设计、快速原型、样式定制
- **QThread** (多线程处理) - 异步任务、界面响应、后台处理
- **QChart** (图表组件) - 数据可视化、交互图表、统计展示
- **QSettings** (配置管理) - 用户偏好、系统设置、状态保存

### 打包部署
- **PyInstaller** (应用打包) - 单文件打包、依赖自动检测、跨平台支持
- **一键打包脚本** - 自动化构建、资源处理、版本管理
- **独立exe文件** - 零环境依赖、即装即用、绿色部署

## 开发进度跟踪

### 数据库开发状态
- [x] 数据库设计与ER图
- [x] SQLAlchemy模型定义
- [x] 数据库初始化脚本
- [x] 测试数据准备

### 后端开发状态
- [x] FastAPI项目框架搭建
- [x] 客户管理模块API
- [x] 订单管理模块API
- [x] 工时管理模块API
- [x] 统计分析模块API
- [ ] 文件管理模块API
- [ ] 系统配置模块API

### 前端开发状态
- [ ] PyQt6界面框架搭建
- [ ] 订单列表界面开发
- [ ] 订单详情界面开发
- [ ] 客户管理界面开发
- [ ] 收入统计界面开发
- [ ] 工时记录界面开发
- [ ] 系统设置界面开发

### 打包部署状态
- [ ] PyInstaller打包脚本配置
- [ ] 资源文件处理优化
- [ ] 一键打包测试验证
- [ ] 部署文档编写

## 项目目录结构

```
兼职接单管理系统/
├── app/                          # 应用主目录
│   ├── backend/                  # 后端服务
│   │   ├── api/                  # API路由
│   │   │   ├── __init__.py
│   │   │   └── endpoints/        # 具体接口
│   │   │       ├── orders.py     # 订单管理API
│   │   │       ├── customers.py  # 客户管理API
│   │   │       ├── analytics.py  # 统计分析API
│   │   │       └── work_logs.py  # 工时管理API
│   │   ├── models/               # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── database.py       # 数据库配置
│   │   │   ├── order.py          # 订单模型
│   │   │   ├── customer.py       # 客户模型
│   │   │   └── work_log.py       # 工时模型
│   │   ├── services/             # 业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── order_service.py  # 订单业务逻辑
│   │   │   ├── customer_service.py # 客户业务逻辑
│   │   │   └── analytics_service.py # 统计分析逻辑
│   │   ├── schemas/              # Pydantic模型
│   │   │   ├── __init__.py
│   │   │   ├── order.py          # 订单数据模型
│   │   │   ├── customer.py       # 客户数据模型
│   │   │   └── analytics.py      # 统计数据模型
│   │   └── main.py               # FastAPI应用入口
│   ├── frontend/                 # 前端界面
│   │   ├── ui/                   # 界面文件
│   │   │   ├── __init__.py
│   │   │   ├── main_window.py    # 主窗口
│   │   │   ├── order_list.py     # 订单列表
│   │   │   ├── order_detail.py   # 订单详情
│   │   │   ├── customer_manager.py # 客户管理
│   │   │   ├── analytics_view.py # 统计分析
│   │   │   └── settings_view.py  # 系统设置
│   │   ├── widgets/              # 自定义组件
│   │   │   ├── __init__.py
│   │   │   ├── order_table.py    # 订单表格组件
│   │   │   ├── chart_widget.py   # 图表组件
│   │   │   └── timer_widget.py   # 计时器组件
│   │   ├── resources/            # 资源文件
│   │   │   ├── icons/            # 图标文件
│   │   │   ├── styles/           # 样式表
│   │   │   │   ├── dark_theme.qss # 深色主题
│   │   │   │   └── light_theme.qss # 浅色主题
│   │   │   └── ui_files/         # Qt Designer文件
│   │   └── controllers/          # 控制器
│   │       ├── __init__.py
│   │       ├── app_controller.py # 应用控制器
│   │       ├── order_controller.py # 订单控制器
│   │       └── api_client.py     # API客户端
│   ├── core/                     # 核心模块
│   │   ├── __init__.py
│   │   ├── config.py             # 配置管理
│   │   ├── database.py           # 数据库连接
│   │   ├── exceptions.py         # 异常定义
│   │   └── security.py           # 安全模块
│   └── utils/                    # 工具函数
│       ├── __init__.py
│       ├── helpers.py            # 辅助函数
│       ├── validators.py         # 验证器
│       └── file_manager.py       # 文件管理
├── data/                         # 数据目录
│   ├── database.db               # SQLite数据库
│   ├── attachments/              # 附件文件
│   └── logs/                     # 日志文件
├── tests/                        # 测试文件
│   ├── __init__.py
│   ├── test_backend/             # 后端测试
│   └── test_frontend/            # 前端测试
├── build/                        # 打包输出
├── requirements.txt              # Python依赖
├── main.py                       # 应用入口
├── build.py                      # 打包脚本
└── README.md                     # 项目文档
```

## 模块技术方案

### 数据库设计模块

#### 技术架构
- **ORM框架**：SQLAlchemy 2.0 - 现代化异步ORM，支持类型提示
- **数据库引擎**：SQLite - 零配置，高性能，支持并发读取
- **连接池**：SQLAlchemy连接池 - 优化数据库连接管理
- **迁移工具**：Alembic - 数据库版本控制和迁移管理

#### 核心数据模型设计

**1. 订单模型 (Order)**
```python
# 核心字段设计
- id: 主键，UUID格式
- title: 订单标题，VARCHAR(200)
- description: 项目描述，TEXT
- customer_id: 客户外键，关联customers表
- category_id: 分类外键，关联categories表
- status: 订单状态，ENUM类型
- billing_method: 计费方式，ENUM(fixed/hourly/word_count/page_count)
- unit_price: 单价，DECIMAL(10,2)
- total_amount: 总金额，DECIMAL(10,2)
- deposit_ratio: 定金比例，DECIMAL(3,2)
- deposit_amount: 定金金额，DECIMAL(10,2)
- final_amount: 尾款金额，DECIMAL(10,2)
- actual_amount: 实际收款，DECIMAL(10,2)
- start_date: 开始日期，DATE
- deadline: 截止日期，DATETIME
- completed_at: 完成时间，DATETIME
- created_at: 创建时间，DATETIME
- updated_at: 更新时间，DATETIME
```

**2. 客户模型 (Customer)**
```python
# 核心字段设计
- id: 主键，UUID格式
- name: 客户姓名，VARCHAR(100)
- contact_phone: 联系电话，VARCHAR(20)
- contact_email: 邮箱地址，VARCHAR(100)
- contact_wechat: 微信号，VARCHAR(50)
- source: 客户来源，ENUM(friend/platform/direct/referral)
- preferences: 客户偏好，JSON格式存储
- satisfaction_rating: 满意度评级，INTEGER(1-5)
- total_orders: 总订单数，INTEGER
- total_amount: 总交易金额，DECIMAL(12,2)
- last_order_date: 最后订单日期，DATE
- created_at: 创建时间，DATETIME
- updated_at: 更新时间，DATETIME
```

**3. 项目分类模型 (Category)**
```python
# 核心字段设计
- id: 主键，UUID格式
- name: 分类名称，VARCHAR(100)
- type: 分类类型，ENUM(development/writing/content/consulting)
- parent_id: 父分类ID，支持分层分类
- billing_method: 默认计费方式，ENUM
- default_rate: 默认费率，DECIMAL(8,2)
- description: 分类描述，TEXT
- is_active: 是否启用，BOOLEAN
- sort_order: 排序权重，INTEGER
- created_at: 创建时间，DATETIME
```

**4. 工时记录模型 (WorkLog)**
```python
# 核心字段设计
- id: 主键，UUID格式
- order_id: 订单外键，关联orders表
- start_time: 开始时间，DATETIME
- end_time: 结束时间，DATETIME
- duration: 工作时长(分钟)，INTEGER
- hourly_rate: 时薪，DECIMAL(8,2)
- description: 工作内容描述，TEXT
- work_type: 工作类型，VARCHAR(50)
- created_at: 创建时间，DATETIME
```

#### 数据库索引策略
- **订单表**：status + deadline 复合索引，customer_id 索引
- **客户表**：name 索引，source 索引，last_order_date 索引
- **工时表**：order_id + start_time 复合索引
- **分类表**：type + is_active 复合索引

#### 数据完整性约束
- 外键约束：确保数据引用完整性
- 检查约束：金额字段非负，评级范围1-5
- 唯一约束：客户邮箱唯一性（可选）
- 默认值：创建时间自动设置，状态默认值

### 订单管理模块

#### 技术架构
- **状态管理**：8种订单状态，完整的状态流转控制
- **计费系统**：支持固定价格、按小时、按字数、按页数四种计费方式
- **金额计算**：自动计算总金额、定金、尾款，支持实际收款跟踪
- **业务规则**：状态转换验证、时间逻辑检查、计费信息验证

#### 核心功能特性

**1. 订单生命周期管理**
```python
# 订单状态流转
PENDING → IN_PROGRESS → REVISING → REVIEWING → COMPLETED → SETTLED
         ↓           ↓         ↓          ↓
       PAUSED    CANCELLED  CANCELLED  CANCELLED
```

**2. 多维度计费支持**
- **固定价格**：适用于整包项目
- **按小时计费**：适用于咨询和开发服务
- **按字数计费**：适用于文档和翻译服务
- **按页数计费**：适用于设计和排版服务

**3. 智能金额计算**
- 根据计费方式自动计算总金额
- 支持自定义定金比例（0-100%）
- 自动计算定金和尾款金额
- 实际收款跟踪和对账功能

**4. 项目管理功能**
- 技术栈标记和分类
- 交付物清单管理
- 工时预估与实际对比
- 优先级管理（1-5级）
- 标签系统支持

#### API接口设计

**订单管理核心接口：**
- `POST /orders` - 创建订单
- `GET /orders/{id}` - 获取订单详情
- `PUT /orders/{id}` - 更新订单信息
- `DELETE /orders/{id}` - 删除订单
- `PUT /orders/{id}/status` - 更新订单状态

**查询和统计接口：**
- `GET /orders` - 订单列表（支持分页、筛选、排序）
- `GET /orders/statistics` - 订单统计信息
- `GET /orders/active/list` - 活跃订单列表
- `GET /orders/overdue/list` - 逾期订单列表
- `POST /orders/calculate-amount` - 金额计算

**批量操作接口：**
- `POST /orders/batch/update` - 批量更新订单
- `POST /orders/batch/status` - 批量状态更新
- `POST /orders/batch/delete` - 批量删除订单

#### 业务规则验证

**状态转换规则：**
- 严格的状态流转控制，防止无效状态转换
- 自动记录状态变更日志和时间戳
- 支持状态回退（特定场景下）

**数据验证规则：**
- 截止日期不能早于开始日期
- 按量计费时必须设置单价和数量
- 定金比例必须在0-1之间
- 客户和分类必须存在且有效

**统计信息自动更新：**
- 订单变更时自动更新客户统计
- 订单变更时自动更新分类统计
- 支持手动触发统计信息重新计算

### 工时管理模块

#### 技术架构
- **精确时间跟踪**：毫秒级计时精度，支持计时器暂停/恢复
- **智能计算引擎**：自动时长计算、收入统计、效率评分
- **多维度分析**：按订单、客户、工作类型的工时分析
- **实时状态管理**：计时器状态跟踪、并发控制、异常处理

#### 核心功能特性

**1. 计时器系统**
```python
# 计时器操作流程
START → RUNNING → PAUSE → RESUME → RUNNING → STOP
                ↓                           ↓
              STOP                        STOP
```

**2. 工时记录管理**
- **自动时长计算**：根据开始/结束时间自动计算工作时长
- **手动时间录入**：支持手动输入工作时间和时长
- **工作类型分类**：前端开发、后端开发、设计、测试等
- **计费状态控制**：可计费/不可计费工时区分

**3. 效率分析系统**
- **效率评分算法**：基于工作时长、描述完整性的综合评分
- **工作模式分析**：最佳工作时段、工作习惯分析
- **生产力统计**：每日/周/月工作效率趋势

**4. 统计报表功能**
- **实时统计**：总工时、可计费工时、收入统计
- **时间分布**：每日工时分布、工作类型分布
- **收入分析**：平均时薪、收入趋势、项目收益

#### API接口设计

**工时记录核心接口：**
- `POST /work-logs` - 创建工时记录
- `GET /work-logs/{id}` - 获取工时详情
- `PUT /work-logs/{id}` - 更新工时信息
- `DELETE /work-logs/{id}` - 删除工时记录

**计时器管理接口：**
- `GET /work-logs/timer/status` - 获取计时器状态
- `POST /work-logs/timer/operation` - 计时器操作（开始/停止/暂停/恢复）
- `GET /work-logs/running/list` - 获取正在运行的计时器
- `POST /work-logs/timer/stop-all` - 停止所有计时器

**统计分析接口：**
- `GET /work-logs/statistics` - 工时统计信息
- `GET /work-logs/summary/daily/{date}` - 每日工作摘要
- `GET /work-logs/summary/weekly/{week_start}` - 每周工作摘要
- `GET /work-logs/summary/monthly/{year}/{month}` - 每月工作摘要

**查询和筛选接口：**
- `GET /work-logs` - 工时记录列表（支持分页、筛选、排序）
- `GET /work-logs/order/{order_id}/list` - 订单工时记录
- `POST /work-logs/batch/update` - 批量更新工时
- `POST /work-logs/batch/delete` - 批量删除工时

#### 业务规则验证

**时间逻辑验证：**
- 结束时间不能早于开始时间
- 单次工作时长不能超过24小时
- 正在计时的记录不能修改时间信息

**计时器控制规则：**
- 每个订单同时只能有一个运行中的计时器
- 计时器状态转换严格控制
- 支持计时器异常恢复和状态修复

**数据完整性保证：**
- 工时变更时自动更新订单实际工时
- 删除工时记录时同步更新统计信息
- 支持工时数据的批量导入导出

#### 效率分析算法

**效率评分计算：**
```python
效率得分 = 基础分(50) × 时长因子 × 描述因子 × 类型因子

时长因子：
- 30分钟-4小时：1.0（最佳）
- <30分钟：时长/30（递减）
- >4小时：240/时长（递减）

描述因子：
- 详细描述(≥20字)：1.2
- 简单描述(≥10字)：1.1
- 无描述：0.8
```

**工作模式识别：**
- 最高效工作时段分析
- 工作习惯模式识别
- 项目类型效率对比

### 统计分析模块

#### 技术架构
- **多维度数据整合**：整合客户、订单、工时等多源数据
- **实时计算引擎**：动态计算关键指标和趋势分析
- **智能洞察算法**：基于数据模式识别的业务洞察生成
- **可视化数据接口**：为前端图表提供结构化数据支持

#### 核心功能特性

**1. 综合业务仪表板**
```python
# 关键业务指标
- 客户指标：总数、活跃数、增长率、满意度
- 订单指标：总数、完成率、逾期数、平均价值
- 收入指标：总收入、月收入、增长率、预测
- 效率指标：工时、时薪、效率得分、可计费比例
```

**2. 多维度分析体系**
- **时间维度**：日/周/月/季/年度趋势分析
- **业务维度**：客户价值、项目类型、收入来源分析
- **效率维度**：工作模式、生产力、资源利用率分析
- **财务维度**：收入、成本、利润、现金流分析

**3. 智能业务洞察**
- **趋势识别**：收入增长、客户流失、效率变化趋势
- **异常检测**：业务指标异常波动预警
- **机会发现**：高价值客户、盈利项目、市场机会识别
- **风险预警**：项目逾期、客户流失、收入下降预警

**4. 绩效评估体系**
- **KPI指标**：财务、客户、运营、效率四大维度KPI
- **综合评分**：基于多指标加权的综合绩效评分
- **对标分析**：历史同期对比、行业基准对比
- **改进建议**：基于数据分析的具体改进建议

#### API接口设计

**核心分析接口：**
- `GET /analytics/overview` - 业务概览仪表板
- `GET /analytics/revenue` - 收入分析报告
- `GET /analytics/customers` - 客户分析报告
- `GET /analytics/projects` - 项目分析报告
- `GET /analytics/efficiency` - 工作效率分析

**综合报告接口：**
- `GET /analytics/performance` - 绩效指标评估
- `GET /analytics/report` - 综合分析报告
- `POST /analytics/custom` - 自定义分析查询

**可视化支持接口：**
- `GET /analytics/dashboard/widgets` - 仪表板组件数据
- `GET /analytics/insights` - 智能业务洞察

#### 分析算法设计

**绩效评分算法：**
```python
综合绩效得分 = (
    收入增长权重 × 收入增长率 +
    利润率权重 × 利润率 +
    客户满意度权重 × 客户满意度 +
    交付率权重 × 项目交付率 +
    效率权重 × 工作效率得分
) / 权重总和

绩效等级：
- 优秀：≥80分
- 良好：70-79分
- 合格：60-69分
- 需改进：<60分
```

**客户价值分析：**
```python
客户生命周期价值 = 平均订单价值 × 订单频率 × 客户生命周期

客户细分：
- 高价值客户：CLV > 10,000元
- 中价值客户：1,000 ≤ CLV ≤ 10,000元
- 低价值客户：CLV < 1,000元
```

**趋势预测模型：**
- 基于历史数据的线性回归预测
- 季节性模式识别和调整
- 异常值检测和数据清洗
- 置信区间计算和风险评估

#### 业务洞察引擎

**洞察规则库：**
- 收入增长超过20%：建议扩大市场投入
- 客户流失率超过20%：建议加强客户关系维护
- 项目交付率低于80%：建议优化项目管理流程
- 工作效率低于70分：建议关注高效工作时段

**预警机制：**
- 实时监控关键指标变化
- 自动触发预警通知
- 分级预警处理机制
- 历史预警记录和分析

### 订单管理模块
*此部分将在模块开发阶段更新详细技术方案*

### 客户管理模块
*此部分将在模块开发阶段更新详细技术方案*

### 统计分析模块
*此部分将在模块开发阶段更新详细技术方案*

### 工时管理模块
*此部分将在模块开发阶段更新详细技术方案*

## 快速启动

### 开发环境
1. 安装Python 3.8+
2. 创建虚拟环境：`python -m venv venv`
3. 激活虚拟环境：`venv\Scripts\activate` (Windows)
4. 安装依赖：`pip install -r requirements.txt`
5. 运行应用：`python main.py`

### 打包部署
1. 运行打包脚本：`python build.py`
2. 在build/dist目录找到exe文件
3. 双击运行，无需安装任何环境

## 使用说明

### 基本操作流程
1. **订单录入**：选择项目类型，填写订单信息，设置截止日期
2. **工时记录**：使用内置计时器记录实际工作时间
3. **进度跟踪**：实时更新订单状态，记录沟通内容
4. **收入分析**：查看收入统计，分析项目盈利情况
5. **客户维护**：记录客户偏好，维护长期合作关系

### 高级功能
- **批量操作**：支持订单批量导入、状态批量更新
- **模板系统**：创建常用项目模板，快速录入订单
- **数据导出**：支持Excel格式导出，便于财务管理
- **提醒功能**：截止日期提醒，避免项目延期
- **主题切换**：支持深色/浅色主题，护眼模式

## 常见问题与解决方案

### 开发环境问题
- **依赖安装失败**：检查Python版本，使用国内镜像源
- **数据库连接错误**：确认SQLite文件权限，检查路径配置
- **界面显示异常**：验证PyQt6安装，检查系统DPI设置

### 使用过程问题
- **数据丢失**：定期备份数据库文件，启用自动备份功能
- **性能问题**：清理历史日志，优化数据库索引
- **文件管理**：规范附件命名，定期整理文件目录

*更多问题解决方案将在开发过程中持续更新*
