# -*- mode: python ; coding: utf-8 -*-

"""
兼职接单管理系统 - PyInstaller 配置文件

这个配置文件用于将整个应用程序打包成独立的可执行文件
"""

import os
import sys
from pathlib import Path

# 获取项目根目录
import os
project_root = Path(os.getcwd()).absolute()

# 定义应用程序信息
APP_NAME = "兼职接单管理系统"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "专业的兼职接单管理系统，支持订单管理、客户管理、工时记录、统计分析等功能"
APP_AUTHOR = "AI Assistant"

# 主程序入口
main_script = str(project_root / "main.py")

# 数据文件和资源文件
datas = [
    # 数据库文件
    (str(project_root / "freelance_management.db"), "."),
    # 配置文件
    (str(project_root / "app" / "core" / "config.py"), "app/core"),
    # 前端资源文件
    (str(project_root / "app" / "frontend" / "resources"), "app/frontend/resources"),
    # 数据目录
    (str(project_root / "data"), "data"),
    # 上传目录
    (str(project_root / "uploads"), "uploads"),
]

# 隐藏导入的模块
hiddenimports = [
    # PyQt6 相关
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.sip',
    
    # FastAPI 相关
    'fastapi',
    'uvicorn',
    'uvicorn.main',
    'uvicorn.config',
    'uvicorn.server',
    'uvicorn.protocols',
    'uvicorn.protocols.http',
    'uvicorn.protocols.websockets',
    'uvicorn.lifespan',
    'uvicorn.lifespan.on',
    'uvicorn.middleware',
    'uvicorn.middleware.proxy_headers',
    
    # SQLAlchemy 相关
    'sqlalchemy',
    'sqlalchemy.ext',
    'sqlalchemy.ext.declarative',
    'sqlalchemy.orm',
    'sqlalchemy.sql',
    'sqlalchemy.engine',
    'sqlalchemy.dialects',
    'sqlalchemy.dialects.sqlite',
    
    # Pydantic 相关
    'pydantic',
    'pydantic.fields',
    'pydantic.validators',
    'pydantic.typing',
    'pydantic.json',
    
    # 其他依赖
    'requests',
    'psutil',
    'email_validator',
    'python_multipart',
    'starlette',
    'starlette.applications',
    'starlette.middleware',
    'starlette.routing',
    'starlette.responses',
    'starlette.requests',
    
    # 应用程序模块
    'app.core',
    'app.core.database',
    'app.core.config',
    'app.models',
    'app.backend',
    'app.backend.main',
    'app.backend.api',
    'app.backend.services',
    'app.backend.schemas',
    'app.frontend',
    'app.frontend.ui',
    'app.frontend.widgets',
    'app.frontend.controllers',
    'app.utils',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'test',
    'tests',
    'unittest',
]

# 二进制文件
binaries = []

# 分析配置
a = Analysis(
    [main_script],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 处理重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=APP_NAME,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version=None,
    uac_admin=False,
    uac_uiaccess=False,
    icon=None,  # 可以添加图标文件路径
)

# macOS 应用程序包配置
if sys.platform == 'darwin':
    app = BUNDLE(
        exe,
        name=f'{APP_NAME}.app',
        icon=None,  # 可以添加 .icns 图标文件
        bundle_identifier=f'com.freelance.management.{APP_NAME.lower().replace(" ", "")}',
        version=APP_VERSION,
        info_plist={
            'CFBundleName': APP_NAME,
            'CFBundleDisplayName': APP_NAME,
            'CFBundleVersion': APP_VERSION,
            'CFBundleShortVersionString': APP_VERSION,
            'CFBundleIdentifier': f'com.freelance.management.{APP_NAME.lower().replace(" ", "")}',
            'CFBundleInfoDictionaryVersion': '6.0',
            'CFBundlePackageType': 'APPL',
            'CFBundleSignature': 'FLMG',
            'NSHighResolutionCapable': True,
            'NSRequiresAquaSystemAppearance': False,
            'LSMinimumSystemVersion': '10.14.0',
            'NSHumanReadableCopyright': f'Copyright © 2025 {APP_AUTHOR}. All rights reserved.',
            'CFBundleDocumentTypes': [],
            'NSAppTransportSecurity': {
                'NSAllowsArbitraryLoads': True
            }
        }
    )

# 收集文件配置
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name=APP_NAME
)
