#!/bin/bash

# 兼职接单管理系统 - 简化打包脚本
# 用于快速打包应用程序

echo "🚀 开始打包兼职接单管理系统..."

# 设置变量
APP_NAME="兼职接单管理系统"
VERSION="1.0.0"
BUILD_DIR="build"
DIST_DIR="dist"

# 清理旧的构建文件
echo "🧹 清理旧的构建文件..."
rm -rf "$BUILD_DIR" "$DIST_DIR"
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# 检查 PyInstaller
if ! command -v pyinstaller &> /dev/null; then
    echo "❌ PyInstaller 未安装，正在安装..."
    python3 -m pip install pyinstaller
fi

# 创建基本的 spec 文件（如果不存在）
if [ ! -f "build_config.spec" ]; then
    echo "📝 创建基本的 spec 文件..."
    python3 -m PyInstaller --name="$APP_NAME" --onefile --windowed main.py
    mv "$APP_NAME.spec" build_config.spec
fi

# 执行打包
echo "📦 开始打包应用程序..."
python3 -m PyInstaller --clean --noconfirm build_config.spec

# 检查打包结果
if [ -d "$DIST_DIR" ]; then
    echo "✅ 打包完成!"
    echo "📁 输出目录: $DIST_DIR"
    
    # 显示文件大小
    if [ -f "$DIST_DIR/$APP_NAME" ]; then
        SIZE=$(du -h "$DIST_DIR/$APP_NAME" | cut -f1)
        echo "📊 应用程序大小: $SIZE"
    elif [ -d "$DIST_DIR/$APP_NAME.app" ]; then
        SIZE=$(du -h "$DIST_DIR/$APP_NAME.app" | tail -1 | cut -f1)
        echo "📊 应用程序大小: $SIZE"
    fi
    
    # 列出输出文件
    echo "📋 输出文件:"
    ls -la "$DIST_DIR"
    
else
    echo "❌ 打包失败!"
    exit 1
fi

echo "🎉 打包完成!"
