"""
系统配置管理API路由

提供系统配置相关的RESTful API接口
包含配置管理、系统信息、备份恢复等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.config import Settings, get_settings
from app.backend.schemas.setting import (
    SettingCreate, SettingUpdate, SettingResponse, SettingListParams,
    SettingSummary, SettingGroup, SystemInfo, ConfigBackup,
    SettingValidation, UserPreferences
)
from app.backend.schemas.base import (
    APIResponse, APIListResponse, SuccessResponse,
    create_response, create_list_response, create_pagination_info
)
from app.backend.services.setting_service import SettingService

# 创建路由器
router = APIRouter()

@router.get("/settings/system/info", response_model=APIResponse[SystemInfo], summary="获取系统信息")
async def get_system_info(
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取系统信息
    
    返回系统运行状态、数据库信息、存储使用情况等
    """
    service = SettingService(db, settings)
    system_info = service.get_system_info()
    
    return create_response(
        success=True,
        data=system_info,
        message="系统信息获取成功"
    )

@router.get("/settings/groups", response_model=APIResponse[List[SettingGroup]], summary="获取分组配置")
async def get_settings_groups(
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取按分类分组的配置列表
    
    返回所有配置按分类分组的结构化数据
    """
    service = SettingService(db, settings)
    groups = service.get_settings_grouped()
    
    return create_response(
        success=True,
        data=groups,
        message="分组配置获取成功"
    )

@router.post("/settings", response_model=APIResponse[SettingResponse], summary="创建配置")
async def create_setting(
    setting_data: SettingCreate = Body(...),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    创建新的系统配置
    
    **参数说明：**
    - config_key: 配置键（唯一标识）
    - config_value: 配置值
    - default_value: 默认值
    - description: 配置描述
    - category: 配置分类
    - value_type: 值类型（string/integer/float/boolean/json/text）
    - is_system: 是否为系统配置
    - is_readonly: 是否只读
    - is_required: 是否必需
    """
    service = SettingService(db, settings)
    setting = service.create_setting(setting_data)
    
    return create_response(
        success=True,
        data=SettingResponse.model_validate(setting),
        message="配置创建成功"
    )

@router.get("/settings/{setting_id}", response_model=APIResponse[SettingResponse], summary="获取配置详情")
async def get_setting(
    setting_id: str = Path(..., description="配置ID"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    根据ID获取配置详细信息
    
    返回配置的完整信息，包括值、类型、分类等
    """
    service = SettingService(db, settings)
    setting = service.get_setting(setting_id)
    
    return create_response(
        success=True,
        data=SettingResponse.model_validate(setting),
        message="配置信息获取成功"
    )

@router.put("/settings/{setting_id}", response_model=APIResponse[SettingResponse], summary="更新配置")
async def update_setting(
    setting_id: str = Path(..., description="配置ID"),
    setting_data: SettingUpdate = Body(...),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    更新系统配置
    
    可以更新配置值、描述、分类等信息
    注意：只读配置和系统配置的某些字段不能修改
    """
    service = SettingService(db, settings)
    setting = service.update_setting(setting_id, setting_data)
    
    return create_response(
        success=True,
        data=SettingResponse.model_validate(setting),
        message="配置更新成功"
    )

@router.delete("/settings/{setting_id}", response_model=SuccessResponse, summary="删除配置")
async def delete_setting(
    setting_id: str = Path(..., description="配置ID"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    删除系统配置
    
    注意：系统配置和必需配置不能删除
    """
    service = SettingService(db, settings)
    service.delete_setting(setting_id)
    
    return SuccessResponse(message="配置删除成功")

@router.get("/settings", response_model=APIListResponse[SettingResponse], summary="获取配置列表")
async def list_settings(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    keyword: str = Query(default=None, description="搜索关键词"),
    category: str = Query(default=None, description="配置分类筛选"),
    value_type: str = Query(default=None, description="值类型筛选"),
    is_system: bool = Query(default=None, description="是否为系统配置筛选"),
    is_readonly: bool = Query(default=None, description="是否只读筛选"),
    is_required: bool = Query(default=None, description="是否必需筛选"),
    has_value: bool = Query(default=None, description="是否有配置值筛选"),
    is_default: bool = Query(default=None, description="是否为默认值筛选"),
    sort_by: str = Query(default=None, description="排序字段"),
    sort_order: str = Query(default="asc", pattern="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取配置列表
    
    支持分页、搜索、筛选和排序功能
    
    **筛选条件:**
    - keyword: 在配置键、描述中搜索
    - category: 按分类筛选
    - value_type: 按值类型筛选
    - is_system: 筛选系统配置
    - is_readonly: 筛选只读配置
    - is_required: 筛选必需配置
    - has_value: 筛选有值的配置
    - is_default: 筛选默认值配置
    
    **排序字段:**
    - config_key: 按配置键排序
    - category: 按分类排序
    - value_type: 按值类型排序
    - created_at: 按创建时间排序
    """
    params = SettingListParams(
        page=page,
        page_size=page_size,
        keyword=keyword,
        category=category,
        value_type=value_type,
        is_system=is_system,
        is_readonly=is_readonly,
        is_required=is_required,
        has_value=has_value,
        is_default=is_default,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    service = SettingService(db, settings)
    settings_list, total = service.list_settings(params)
    
    # 转换为响应模型
    setting_responses = [SettingResponse.model_validate(setting) for setting in settings_list]
    
    # 创建分页信息
    pagination = create_pagination_info(page, page_size, total)
    
    return create_list_response(
        data=setting_responses,
        pagination=pagination,
        message="配置列表获取成功"
    )

@router.get("/settings/category/{category}", response_model=APIResponse[List[SettingResponse]], summary="获取分类配置")
async def get_settings_by_category(
    category: str = Path(..., description="配置分类"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    根据分类获取配置列表
    
    返回指定分类下的所有配置项
    """
    service = SettingService(db, settings)
    category_settings = service.get_settings_by_category(category)
    
    setting_responses = [SettingResponse.model_validate(setting) for setting in category_settings]
    
    return create_response(
        success=True,
        data=setting_responses,
        message="分类配置获取成功"
    )

@router.post("/settings/validate", response_model=APIResponse[SettingValidation], summary="验证配置值")
async def validate_setting_value(
    config_key: str = Body(..., description="配置键"),
    config_value: str = Body(..., description="配置值"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    验证配置值是否有效
    
    根据配置的值类型和业务规则验证配置值
    """
    service = SettingService(db, settings)
    validation = service.validate_setting_value(config_key, config_value)
    
    return create_response(
        success=True,
        data=validation,
        message="配置值验证完成"
    )

@router.put("/settings/key/{config_key}/value", response_model=APIResponse[SettingResponse], summary="设置配置值")
async def set_config_value(
    config_key: str = Path(..., description="配置键"),
    config_value: str = Body(..., description="配置值"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    设置配置值
    
    如果配置不存在则创建，如果存在则更新
    """
    service = SettingService(db, settings)
    setting = service.set_config_value(config_key, config_value)
    
    return create_response(
        success=True,
        data=SettingResponse.model_validate(setting),
        message="配置值设置成功"
    )

@router.get("/settings/key/{config_key}/value", response_model=APIResponse[str], summary="获取配置值")
async def get_config_value(
    config_key: str = Path(..., description="配置键"),
    default_value: str = Query(default=None, description="默认值"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取配置值
    
    如果配置不存在则返回默认值
    """
    service = SettingService(db, settings)
    value = service.get_config_value(config_key, default_value)
    
    return create_response(
        success=True,
        data=str(value) if value is not None else None,
        message="配置值获取成功"
    )

@router.post("/settings/backup", response_model=APIResponse[ConfigBackup], summary="创建配置备份")
async def create_config_backup(
    backup_name: str = Body(default=None, description="备份名称"),
    description: str = Body(default=None, description="备份描述"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    创建配置备份
    
    备份所有系统配置到文件
    """
    service = SettingService(db, settings)
    backup = service.create_config_backup(backup_name, description)
    
    return create_response(
        success=True,
        data=backup,
        message="配置备份创建成功"
    )

@router.get("/settings/backups", response_model=APIResponse[List[ConfigBackup]], summary="获取备份列表")
async def list_config_backups(
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取配置备份列表
    
    返回所有可用的配置备份
    """
    service = SettingService(db, settings)
    backups = service.list_config_backups()
    
    return create_response(
        success=True,
        data=backups,
        message="备份列表获取成功"
    )

@router.post("/settings/restore/{backup_id}", response_model=SuccessResponse, summary="恢复配置备份")
async def restore_config_backup(
    backup_id: str = Path(..., description="备份ID"),
    restore_categories: List[str] = Body(default=None, description="要恢复的分类"),
    overwrite_existing: bool = Body(default=False, description="是否覆盖现有配置"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    恢复配置备份
    
    从备份文件恢复配置
    """
    service = SettingService(db, settings)
    service.restore_config_backup(backup_id, restore_categories, overwrite_existing)
    
    return SuccessResponse(message="配置恢复成功")

@router.post("/settings/reset", response_model=SuccessResponse, summary="重置配置")
async def reset_settings_to_defaults(
    category: str = Body(default=None, description="要重置的分类"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    重置配置为默认值
    
    将配置重置为默认值，可以指定分类
    """
    service = SettingService(db, settings)
    service.reset_to_defaults(category)
    
    return SuccessResponse(message="配置重置成功")

@router.post("/settings/initialize", response_model=SuccessResponse, summary="初始化默认配置")
async def initialize_default_settings(
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    初始化默认配置
    
    创建系统默认的配置项
    """
    service = SettingService(db, settings)
    success = service.initialize_default_settings()
    
    if success:
        return SuccessResponse(message="默认配置初始化成功")
    else:
        return SuccessResponse(message="默认配置初始化失败", success=False)
