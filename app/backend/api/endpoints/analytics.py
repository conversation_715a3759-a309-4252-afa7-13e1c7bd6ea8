"""
统计分析API路由

提供综合统计分析相关的RESTful API接口
包含业务概览、趋势分析、绩效评估等功能
"""

from typing import List, Optional
from datetime import date
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.backend.schemas.analytics import (
    BusinessOverview, RevenueAnalysis, CustomerAnalysis,
    ProjectAnalysis, WorkEfficiencyAnalysis, TrendAnalysis,
    PerformanceMetrics, AnalyticsQuery, AnalyticsReport
)
from app.backend.schemas.base import (
    APIResponse, create_response
)
from app.backend.services.analytics_service import AnalyticsService

# 创建路由器
router = APIRouter()

@router.get("/analytics/overview", response_model=APIResponse[BusinessOverview], summary="获取业务概览")
async def get_business_overview(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取业务概览
    
    综合展示关键业务指标，包括：
    - 客户和订单统计
    - 收入和工时指标
    - 增长率和预警信息
    
    如果不提供日期范围，默认显示当前月份数据
    """
    service = AnalyticsService(db)
    overview = service.get_business_overview(start_date, end_date)
    
    return create_response(
        success=True,
        data=overview,
        message="业务概览获取成功"
    )

@router.get("/analytics/revenue", response_model=APIResponse[RevenueAnalysis], summary="获取收入分析")
async def get_revenue_analysis(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取收入分析
    
    详细的收入统计和趋势分析，包括：
    - 收入统计和分布
    - 按客户、分类、计费方式的收入分组
    - 收入趋势和预测
    - 收入质量指标
    """
    service = AnalyticsService(db)
    revenue_analysis = service.get_revenue_analysis(start_date, end_date)
    
    return create_response(
        success=True,
        data=revenue_analysis,
        message="收入分析获取成功"
    )

@router.get("/analytics/customers", response_model=APIResponse[CustomerAnalysis], summary="获取客户分析")
async def get_customer_analysis(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取客户分析
    
    客户价值、行为和满意度分析，包括：
    - 客户统计和价值分析
    - 客户生命周期价值
    - 客户行为和留存分析
    - 满意度和获客渠道分析
    """
    service = AnalyticsService(db)
    customer_analysis = service.get_customer_analysis(start_date, end_date)
    
    return create_response(
        success=True,
        data=customer_analysis,
        message="客户分析获取成功"
    )

@router.get("/analytics/projects", response_model=APIResponse[ProjectAnalysis], summary="获取项目分析")
async def get_project_analysis(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取项目分析
    
    订单和项目的执行效率分析，包括：
    - 项目统计和执行效率
    - 项目分布和分类分析
    - 工时对比和效率分析
    - 盈利分析和利润率
    """
    service = AnalyticsService(db)
    project_analysis = service.get_project_analysis(start_date, end_date)
    
    return create_response(
        success=True,
        data=project_analysis,
        message="项目分析获取成功"
    )

@router.get("/analytics/efficiency", response_model=APIResponse[WorkEfficiencyAnalysis], summary="获取工作效率分析")
async def get_work_efficiency_analysis(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取工作效率分析
    
    工时和生产力的深度分析，包括：
    - 工时统计和效率指标
    - 工作模式和生产力趋势
    - 工作类型效率分析
    - 效率改进建议
    """
    service = AnalyticsService(db)
    efficiency_analysis = service.get_work_efficiency_analysis(start_date, end_date)
    
    return create_response(
        success=True,
        data=efficiency_analysis,
        message="工作效率分析获取成功"
    )

@router.get("/analytics/performance", response_model=APIResponse[PerformanceMetrics], summary="获取绩效指标")
async def get_performance_metrics(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取绩效指标
    
    关键绩效指标(KPI)的综合评估，包括：
    - 财务、客户、运营KPI
    - 效率和质量指标
    - 综合绩效评分
    - 改进建议
    """
    service = AnalyticsService(db)
    performance_metrics = service.get_performance_metrics(start_date, end_date)
    
    return create_response(
        success=True,
        data=performance_metrics,
        message="绩效指标获取成功"
    )

@router.get("/analytics/report", response_model=APIResponse[AnalyticsReport], summary="生成综合分析报告")
async def generate_analytics_report(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    report_type: str = Query(default="comprehensive", description="报告类型"),
    db: Session = Depends(get_db)
):
    """
    生成综合分析报告
    
    包含所有分析模块的完整报告：
    - 执行摘要和关键洞察
    - 业务概览和各维度分析
    - 趋势预测和改进建议
    - 数据质量评估
    """
    service = AnalyticsService(db)
    report = service.generate_analytics_report(start_date, end_date, report_type)
    
    return create_response(
        success=True,
        data=report,
        message="综合分析报告生成成功"
    )

@router.post("/analytics/custom", response_model=APIResponse[dict], summary="自定义分析查询")
async def get_custom_analytics(
    query: AnalyticsQuery,
    db: Session = Depends(get_db)
):
    """
    自定义分析查询
    
    根据用户自定义的查询参数进行数据分析：
    - 灵活的时间范围和筛选条件
    - 多维度分组和聚合
    - 自定义指标计算
    - 支持同期比较
    """
    service = AnalyticsService(db)
    result = service.get_custom_analytics(query)
    
    return create_response(
        success=True,
        data=result,
        message="自定义分析查询成功"
    )

@router.get("/analytics/dashboard/widgets", response_model=APIResponse[List[dict]], summary="获取仪表板组件数据")
async def get_dashboard_widgets(
    widget_types: Optional[str] = Query(default=None, description="组件类型列表（逗号分隔）"),
    db: Session = Depends(get_db)
):
    """
    获取仪表板组件数据
    
    为前端仪表板提供各种组件的数据：
    - 关键指标卡片
    - 图表数据
    - 趋势组件
    - 预警信息
    """
    service = AnalyticsService(db)
    
    # 获取基础数据
    overview = service.get_business_overview()
    revenue = service.get_revenue_analysis()
    
    # 构建组件数据
    widgets = []
    
    # 收入概览组件
    widgets.append({
        'widget_id': 'revenue_overview',
        'widget_type': 'metric_card',
        'title': '收入概览',
        'data': {
            'total_revenue': float(revenue.total_revenue),
            'monthly_revenue': float(revenue.monthly_revenue),
            'growth_rate': revenue.growth_rate
        }
    })
    
    # 客户统计组件
    widgets.append({
        'widget_id': 'customer_stats',
        'widget_type': 'metric_card',
        'title': '客户统计',
        'data': {
            'total_customers': overview.total_customers,
            'active_customers': overview.active_customers,
            'growth_rate': overview.customer_growth_rate
        }
    })
    
    # 订单状态组件
    widgets.append({
        'widget_id': 'order_status',
        'widget_type': 'pie_chart',
        'title': '订单状态分布',
        'data': {
            'total_orders': overview.total_orders,
            'active_orders': overview.active_orders,
            'completed_orders': overview.completed_orders,
            'overdue_orders': overview.overdue_orders
        }
    })
    
    # 收入趋势组件
    widgets.append({
        'widget_id': 'revenue_trend',
        'widget_type': 'line_chart',
        'title': '收入趋势',
        'data': {
            'trend_data': revenue.daily_revenue_trend
        }
    })
    
    # 工作效率组件
    widgets.append({
        'widget_id': 'work_efficiency',
        'widget_type': 'gauge_chart',
        'title': '工作效率',
        'data': {
            'efficiency_score': overview.efficiency_score,
            'work_hours': overview.total_work_hours,
            'hourly_rate': overview.average_hourly_rate
        }
    })
    
    return create_response(
        success=True,
        data=widgets,
        message="仪表板组件数据获取成功"
    )

@router.get("/analytics/insights", response_model=APIResponse[List[str]], summary="获取业务洞察")
async def get_business_insights(
    start_date: Optional[date] = Query(default=None, description="开始日期"),
    end_date: Optional[date] = Query(default=None, description="结束日期"),
    db: Session = Depends(get_db)
):
    """
    获取业务洞察
    
    基于数据分析生成的业务洞察和建议：
    - 关键趋势识别
    - 异常情况预警
    - 优化建议
    - 机会识别
    """
    service = AnalyticsService(db)
    
    # 获取各项分析数据
    overview = service.get_business_overview(start_date, end_date)
    revenue = service.get_revenue_analysis(start_date, end_date)
    customer = service.get_customer_analysis(start_date, end_date)
    project = service.get_project_analysis(start_date, end_date)
    efficiency = service.get_work_efficiency_analysis(start_date, end_date)
    
    insights = []
    
    # 收入洞察
    if revenue.growth_rate > 20:
        insights.append("收入增长强劲，建议扩大市场投入")
    elif revenue.growth_rate < 0:
        insights.append("收入出现下降，需要关注市场变化")
    
    # 客户洞察
    if customer.churn_rate > 20:
        insights.append("客户流失率较高，建议加强客户关系维护")
    
    if customer.average_satisfaction < 3.5:
        insights.append("客户满意度偏低，需要改进服务质量")
    
    # 项目洞察
    if project.on_time_delivery_rate < 80:
        insights.append("项目交付及时率偏低，建议优化项目管理流程")
    
    if project.overdue_projects > 5:
        insights.append(f"当前有{project.overdue_projects}个逾期项目，需要重点关注")
    
    # 效率洞察
    if efficiency.billable_ratio < 70:
        insights.append("可计费工时比例偏低，建议优化时间分配")
    
    if efficiency.average_efficiency_score < 70:
        insights.append("工作效率有提升空间，建议关注高效工作时段")
    
    # 机会识别
    top_customers = revenue.revenue_by_customer[:3]
    if top_customers:
        insights.append(f"重点客户{top_customers[0]['customer_name']}贡献了主要收入，建议深化合作")
    
    if not insights:
        insights.append("整体业务表现良好，继续保持当前策略")
    
    return create_response(
        success=True,
        data=insights,
        message="业务洞察获取成功"
    )
