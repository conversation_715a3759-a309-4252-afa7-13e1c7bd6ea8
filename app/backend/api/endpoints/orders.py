"""
订单管理API路由

提供订单相关的RESTful API接口
包含订单CRUD操作、状态管理、统计查询等功能
"""

from typing import List
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.backend.schemas.order import (
    OrderCreate, OrderUpdate, OrderResponse, OrderListParams,
    OrderSummary, OrderStatistics, OrderStatusUpdate, OrderAmountCalculation,
    OrderBatchUpdate, OrderBatchStatusUpdate, OrderBatchDelete
)
from app.backend.schemas.base import (
    APIResponse, APIListResponse, SuccessResponse,
    create_response, create_list_response, create_pagination_info
)
from app.backend.services.order_service import OrderService

# 创建路由器
router = APIRouter()

@router.post("/orders", response_model=APIResponse[OrderResponse], summary="创建订单")
async def create_order(
    order_data: OrderCreate,
    db: Session = Depends(get_db)
):
    """
    创建新订单
    
    - **title**: 订单标题（必填）
    - **customer_id**: 客户ID（必填）
    - **category_id**: 项目分类ID（必填）
    - **description**: 项目描述
    - **requirements**: 客户要求
    - **billing_method**: 计费方式
    - **total_amount**: 总金额
    - **deadline**: 截止日期
    - **priority**: 优先级(1-5)
    """
    service = OrderService(db)
    order = service.create_order(order_data)
    
    return create_response(
        success=True,
        data=OrderResponse.model_validate(order),
        message="订单创建成功"
    )

@router.get("/orders/statistics", response_model=APIResponse[OrderStatistics], summary="获取订单统计信息")
async def get_order_statistics(db: Session = Depends(get_db)):
    """
    获取订单统计信息

    包含订单总数、状态分布、金额统计等数据
    """
    service = OrderService(db)
    statistics = service.get_order_statistics()

    return create_response(
        success=True,
        data=statistics,
        message="订单统计信息获取成功"
    )

@router.get("/orders/{order_id}", response_model=APIResponse[OrderResponse], summary="获取订单详情")
async def get_order(
    order_id: str = Path(..., description="订单ID"),
    db: Session = Depends(get_db)
):
    """
    根据ID获取订单详细信息

    返回订单的完整信息，包括客户和分类信息
    """
    service = OrderService(db)
    order = service.get_order(order_id)

    return create_response(
        success=True,
        data=OrderResponse.model_validate(order),
        message="订单信息获取成功"
    )

@router.put("/orders/{order_id}", response_model=APIResponse[OrderResponse], summary="更新订单信息")
async def update_order(
    order_id: str = Path(..., description="订单ID"),
    order_data: OrderUpdate = Body(...),
    db: Session = Depends(get_db)
):
    """
    更新订单信息
    
    可以部分更新订单的任意字段
    """
    service = OrderService(db)
    order = service.update_order(order_id, order_data)
    
    return create_response(
        success=True,
        data=OrderResponse.model_validate(order),
        message="订单信息更新成功"
    )

@router.delete("/orders/{order_id}", response_model=SuccessResponse, summary="删除订单")
async def delete_order(
    order_id: str = Path(..., description="订单ID"),
    force: bool = Query(default=False, description="是否强制删除（即使有关联数据）"),
    db: Session = Depends(get_db)
):
    """
    删除订单
    
    - **force**: 是否强制删除，如果为false且订单有关联数据则删除失败
    """
    service = OrderService(db)
    service.delete_order(order_id, force=force)
    
    return SuccessResponse(message="订单删除成功")

@router.get("/orders", response_model=APIListResponse[OrderResponse], summary="获取订单列表")
async def list_orders(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    keyword: str = Query(default=None, description="搜索关键词"),
    customer_id: str = Query(default=None, description="客户ID筛选"),
    category_id: str = Query(default=None, description="分类ID筛选"),
    status: str = Query(default=None, description="状态筛选"),
    priority: int = Query(default=None, ge=1, le=5, description="优先级筛选"),
    billing_method: str = Query(default=None, description="计费方式筛选"),
    is_overdue: bool = Query(default=None, description="是否逾期筛选"),
    is_active: bool = Query(default=None, description="是否活跃订单筛选"),
    sort_by: str = Query(default=None, description="排序字段"),
    sort_order: str = Query(default="desc", pattern="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db)
):
    """
    获取订单列表
    
    支持分页、搜索、筛选和排序功能
    
    **筛选条件:**
    - keyword: 在标题、描述、技术栈、标签中搜索
    - customer_id: 按客户筛选
    - category_id: 按分类筛选
    - status: 按状态筛选
    - priority: 按优先级筛选
    - billing_method: 按计费方式筛选
    - is_overdue: 筛选逾期订单
    - is_active: 筛选活跃订单
    
    **排序字段:**
    - title: 按标题排序
    - created_at: 按创建时间排序
    - deadline: 按截止日期排序
    - total_amount: 按金额排序
    - priority: 按优先级排序
    """
    params = OrderListParams(
        page=page,
        page_size=page_size,
        keyword=keyword,
        customer_id=customer_id,
        category_id=category_id,
        status=status,
        priority=priority,
        billing_method=billing_method,
        is_overdue=is_overdue,
        is_active=is_active,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    service = OrderService(db)
    orders, total = service.list_orders(params)
    
    # 转换为响应模型
    order_responses = [OrderResponse.model_validate(order) for order in orders]
    
    # 创建分页信息
    pagination = create_pagination_info(page, page_size, total)
    
    return create_list_response(
        data=order_responses,
        pagination=pagination,
        message="订单列表获取成功"
    )

@router.put("/orders/{order_id}/status", response_model=APIResponse[OrderResponse], summary="更新订单状态")
async def update_order_status(
    order_id: str = Path(..., description="订单ID"),
    status_data: OrderStatusUpdate = Body(...),
    db: Session = Depends(get_db)
):
    """
    更新订单状态
    
    支持订单状态流转控制和业务规则验证
    """
    service = OrderService(db)
    order = service.update_order_status(order_id, status_data)
    
    return create_response(
        success=True,
        data=OrderResponse.model_validate(order),
        message="订单状态更新成功"
    )

@router.post("/orders/calculate-amount", response_model=APIResponse[OrderAmountCalculation], summary="计算订单金额")
async def calculate_order_amount(
    calculation_data: OrderAmountCalculation,
    db: Session = Depends(get_db)
):
    """
    计算订单金额
    
    根据计费方式、单价、数量等信息计算总金额、定金、尾款
    """
    service = OrderService(db)
    result = service.calculate_order_amount(calculation_data)
    
    return create_response(
        success=True,
        data=result,
        message="金额计算成功"
    )



@router.get("/orders/active/list", response_model=APIResponse[List[OrderResponse]], summary="获取活跃订单列表")
async def get_active_orders(db: Session = Depends(get_db)):
    """
    获取活跃订单列表
    
    返回所有进行中的订单，按截止日期排序
    """
    service = OrderService(db)
    active_orders = service.get_active_orders()
    
    order_responses = [OrderResponse.model_validate(order) for order in active_orders]
    
    return create_response(
        success=True,
        data=order_responses,
        message="活跃订单列表获取成功"
    )

@router.get("/orders/overdue/list", response_model=APIResponse[List[OrderResponse]], summary="获取逾期订单列表")
async def get_overdue_orders(db: Session = Depends(get_db)):
    """
    获取逾期订单列表
    
    返回所有已逾期的活跃订单
    """
    service = OrderService(db)
    overdue_orders = service.get_overdue_orders()
    
    order_responses = [OrderResponse.model_validate(order) for order in overdue_orders]
    
    return create_response(
        success=True,
        data=order_responses,
        message="逾期订单列表获取成功"
    )

@router.get("/orders/summary/list", response_model=APIResponse[List[OrderSummary]], summary="获取订单摘要列表")
async def get_order_summaries(
    keyword: str = Query(default=None, description="搜索关键词"),
    limit: int = Query(default=50, ge=1, le=100, description="返回数量限制"),
    db: Session = Depends(get_db)
):
    """
    获取订单摘要列表
    
    用于下拉选择等场景，返回简化的订单信息
    """
    service = OrderService(db)
    
    if keyword:
        orders = service.search_orders(keyword, limit)
    else:
        # 获取最近的订单
        params = OrderListParams(page=1, page_size=limit, sort_by="created_at", sort_order="desc")
        orders, _ = service.list_orders(params)
    
    summaries = []
    for order in orders:
        summary = OrderSummary(
            id=order.id,
            title=order.title,
            display_title=order.display_title,
            status=order.status,
            customer_name=order.customer.name if order.customer else "未知客户",
            total_amount=order.total_amount,
            deadline=order.deadline,
            is_overdue=order.is_overdue
        )
        summaries.append(summary)
    
    return create_response(
        success=True,
        data=summaries,
        message="订单摘要列表获取成功"
    )

@router.post("/orders/batch/update", response_model=APIResponse[List[OrderResponse]], summary="批量更新订单")
async def batch_update_orders(
    batch_data: OrderBatchUpdate,
    db: Session = Depends(get_db)
):
    """
    批量更新订单信息
    
    可以同时更新多个订单的相同字段
    """
    service = OrderService(db)
    orders = service.batch_update_orders(batch_data.order_ids, batch_data.update_data)
    
    order_responses = [OrderResponse.model_validate(order) for order in orders]
    
    return create_response(
        success=True,
        data=order_responses,
        message=f"成功更新 {len(orders)} 个订单"
    )

@router.post("/orders/batch/status", response_model=APIResponse[List[OrderResponse]], summary="批量更新订单状态")
async def batch_update_order_status(
    batch_data: OrderBatchStatusUpdate,
    db: Session = Depends(get_db)
):
    """
    批量更新订单状态
    
    可以同时更新多个订单的状态
    """
    service = OrderService(db)
    orders = service.batch_update_status(batch_data.order_ids, batch_data.status, batch_data.notes)
    
    order_responses = [OrderResponse.model_validate(order) for order in orders]
    
    return create_response(
        success=True,
        data=order_responses,
        message=f"成功更新 {len(orders)} 个订单状态"
    )

@router.post("/orders/batch/delete", response_model=SuccessResponse, summary="批量删除订单")
async def batch_delete_orders(
    batch_data: OrderBatchDelete,
    db: Session = Depends(get_db)
):
    """
    批量删除订单
    
    可以同时删除多个订单
    """
    service = OrderService(db)
    deleted_count = 0
    
    for order_id in batch_data.order_ids:
        try:
            service.delete_order(order_id, force=batch_data.force_delete)
            deleted_count += 1
        except Exception:
            # 跳过删除失败的订单
            continue
    
    return SuccessResponse(message=f"成功删除 {deleted_count} 个订单")

@router.get("/orders/search", response_model=APIResponse[List[OrderSummary]], summary="搜索订单")
async def search_orders(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(default=10, ge=1, le=50, description="返回数量限制"),
    db: Session = Depends(get_db)
):
    """
    搜索订单
    
    在订单标题、描述、技术栈、标签中进行模糊搜索
    """
    service = OrderService(db)
    orders = service.search_orders(q, limit)
    
    summaries = []
    for order in orders:
        summary = OrderSummary(
            id=order.id,
            title=order.title,
            display_title=order.display_title,
            status=order.status,
            customer_name=order.customer.name if order.customer else "未知客户",
            total_amount=order.total_amount,
            deadline=order.deadline,
            is_overdue=order.is_overdue
        )
        summaries.append(summary)
    
    return create_response(
        success=True,
        data=summaries,
        message=f"找到 {len(summaries)} 个匹配的订单"
    )
