"""
工时管理API路由

提供工时记录相关的RESTful API接口
包含工时CRUD操作、计时器管理、统计分析等功能
"""

from typing import List
from datetime import date
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.backend.schemas.work_log import (
    WorkLogCreate, WorkLogUpdate, WorkLogResponse, WorkLogListParams,
    WorkLogSummary, WorkLogStatistics, TimerOperation, TimerStatus,
    DailyWorkSummary, WeeklyWorkSummary, MonthlyWorkSummary,
    WorkLogBatchUpdate, WorkLogBatchDelete
)
from app.backend.schemas.base import (
    APIResponse, APIListResponse, SuccessResponse,
    create_response, create_list_response, create_pagination_info
)
from app.backend.services.work_log_service import WorkLogService

# 创建路由器
router = APIRouter()

@router.post("/work-logs", response_model=APIResponse[WorkLogResponse], summary="创建工时记录")
async def create_work_log(
    work_log_data: WorkLogCreate,
    db: Session = Depends(get_db)
):
    """
    创建新工时记录
    
    - **order_id**: 订单ID（必填）
    - **start_time**: 开始时间（必填）
    - **end_time**: 结束时间（可选，用于计时器）
    - **duration**: 工作时长（分钟，可选，会自动计算）
    - **description**: 工作内容描述
    - **work_type**: 工作类型
    - **hourly_rate**: 时薪
    - **billable**: 是否计费
    """
    service = WorkLogService(db)
    work_log = service.create_work_log(work_log_data)
    
    return create_response(
        success=True,
        data=WorkLogResponse.model_validate(work_log),
        message="工时记录创建成功"
    )

@router.get("/work-logs/timer/status", response_model=APIResponse[TimerStatus], summary="获取计时器状态")
async def get_timer_status(
    order_id: str = Query(default=None, description="订单ID（可选）"),
    db: Session = Depends(get_db)
):
    """
    获取计时器状态
    
    如果提供order_id，返回该订单的计时器状态
    如果不提供，返回所有正在运行的计时器状态
    """
    service = WorkLogService(db)
    timer_status = service.get_timer_status(order_id)
    
    return create_response(
        success=True,
        data=timer_status,
        message="计时器状态获取成功"
    )

@router.post("/work-logs/timer/operation", response_model=APIResponse[WorkLogResponse], summary="计时器操作")
async def timer_operation(
    operation_data: TimerOperation,
    db: Session = Depends(get_db)
):
    """
    计时器操作
    
    支持的操作类型：
    - **start**: 开始计时
    - **stop**: 停止计时
    - **pause**: 暂停计时
    - **resume**: 恢复计时
    """
    service = WorkLogService(db)
    work_log = service.timer_operation(operation_data)
    
    operation_messages = {
        "start": "计时器启动成功",
        "stop": "计时器停止成功",
        "pause": "计时器暂停成功",
        "resume": "计时器恢复成功"
    }
    
    return create_response(
        success=True,
        data=WorkLogResponse.model_validate(work_log),
        message=operation_messages.get(operation_data.operation, "计时器操作成功")
    )

@router.get("/work-logs/statistics", response_model=APIResponse[WorkLogStatistics], summary="获取工时统计信息")
async def get_work_log_statistics(
    start_date: date = Query(default=None, description="开始日期"),
    end_date: date = Query(default=None, description="结束日期"),
    order_id: str = Query(default=None, description="订单ID筛选"),
    db: Session = Depends(get_db)
):
    """
    获取工时统计信息
    
    包含工时总数、时长分布、收入统计等数据
    """
    service = WorkLogService(db)
    statistics = service.get_work_log_statistics(start_date, end_date, order_id)
    
    return create_response(
        success=True,
        data=statistics,
        message="工时统计信息获取成功"
    )

@router.get("/work-logs/{work_log_id}", response_model=APIResponse[WorkLogResponse], summary="获取工时记录详情")
async def get_work_log(
    work_log_id: str = Path(..., description="工时记录ID"),
    db: Session = Depends(get_db)
):
    """
    根据ID获取工时记录详细信息
    
    返回工时记录的完整信息，包括订单和客户信息
    """
    service = WorkLogService(db)
    work_log = service.get_work_log(work_log_id)
    
    return create_response(
        success=True,
        data=WorkLogResponse.model_validate(work_log),
        message="工时记录信息获取成功"
    )

@router.put("/work-logs/{work_log_id}", response_model=APIResponse[WorkLogResponse], summary="更新工时记录信息")
async def update_work_log(
    work_log_id: str = Path(..., description="工时记录ID"),
    work_log_data: WorkLogUpdate = Body(...),
    db: Session = Depends(get_db)
):
    """
    更新工时记录信息
    
    可以部分更新工时记录的任意字段
    注意：正在计时的记录不能修改时间信息
    """
    service = WorkLogService(db)
    work_log = service.update_work_log(work_log_id, work_log_data)
    
    return create_response(
        success=True,
        data=WorkLogResponse.model_validate(work_log),
        message="工时记录信息更新成功"
    )

@router.delete("/work-logs/{work_log_id}", response_model=SuccessResponse, summary="删除工时记录")
async def delete_work_log(
    work_log_id: str = Path(..., description="工时记录ID"),
    db: Session = Depends(get_db)
):
    """
    删除工时记录
    
    注意：正在计时的记录不能删除，请先停止计时
    """
    service = WorkLogService(db)
    service.delete_work_log(work_log_id)
    
    return SuccessResponse(message="工时记录删除成功")

@router.get("/work-logs", response_model=APIListResponse[WorkLogResponse], summary="获取工时记录列表")
async def list_work_logs(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    keyword: str = Query(default=None, description="搜索关键词"),
    order_id: str = Query(default=None, description="订单ID筛选"),
    work_type: str = Query(default=None, description="工作类型筛选"),
    billable: bool = Query(default=None, description="是否计费筛选"),
    is_running: bool = Query(default=None, description="是否正在计时筛选"),
    start_date: date = Query(default=None, description="开始日期筛选"),
    end_date: date = Query(default=None, description="结束日期筛选"),
    sort_by: str = Query(default=None, description="排序字段"),
    sort_order: str = Query(default="desc", pattern="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db)
):
    """
    获取工时记录列表
    
    支持分页、搜索、筛选和排序功能
    
    **筛选条件:**
    - keyword: 在描述、工作类型、备注中搜索
    - order_id: 按订单筛选
    - work_type: 按工作类型筛选
    - billable: 筛选可计费/不可计费记录
    - is_running: 筛选正在计时的记录
    - start_date/end_date: 按时间范围筛选
    
    **排序字段:**
    - start_time: 按开始时间排序
    - duration: 按工作时长排序
    - created_at: 按创建时间排序
    """
    params = WorkLogListParams(
        page=page,
        page_size=page_size,
        keyword=keyword,
        order_id=order_id,
        work_type=work_type,
        billable=billable,
        is_running=is_running,
        start_date=start_date,
        end_date=end_date,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    service = WorkLogService(db)
    work_logs, total = service.list_work_logs(params)
    
    # 转换为响应模型
    work_log_responses = [WorkLogResponse.model_validate(work_log) for work_log in work_logs]
    
    # 创建分页信息
    pagination = create_pagination_info(page, page_size, total)
    
    return create_list_response(
        data=work_log_responses,
        pagination=pagination,
        message="工时记录列表获取成功"
    )

@router.get("/work-logs/order/{order_id}/list", response_model=APIResponse[List[WorkLogResponse]], summary="获取订单的工时记录列表")
async def get_work_logs_by_order(
    order_id: str = Path(..., description="订单ID"),
    db: Session = Depends(get_db)
):
    """
    获取指定订单的所有工时记录
    
    按开始时间倒序返回
    """
    service = WorkLogService(db)
    work_logs = service.get_work_logs_by_order(order_id)
    
    work_log_responses = [WorkLogResponse.model_validate(work_log) for work_log in work_logs]
    
    return create_response(
        success=True,
        data=work_log_responses,
        message="订单工时记录列表获取成功"
    )

@router.get("/work-logs/running/list", response_model=APIResponse[List[WorkLogResponse]], summary="获取正在运行的计时器列表")
async def get_running_timers(db: Session = Depends(get_db)):
    """
    获取所有正在运行的计时器
    
    返回所有is_running=True的工时记录
    """
    service = WorkLogService(db)
    running_timers = service.get_running_timers()
    
    timer_responses = [WorkLogResponse.model_validate(timer) for timer in running_timers]
    
    return create_response(
        success=True,
        data=timer_responses,
        message="正在运行的计时器列表获取成功"
    )

@router.post("/work-logs/timer/stop-all", response_model=SuccessResponse, summary="停止所有正在运行的计时器")
async def stop_all_running_timers(db: Session = Depends(get_db)):
    """
    停止所有正在运行的计时器
    
    用于批量停止计时，通常在下班或系统维护时使用
    """
    service = WorkLogService(db)
    stopped_count = service.stop_all_running_timers()
    
    return SuccessResponse(message=f"成功停止 {stopped_count} 个正在运行的计时器")

@router.get("/work-logs/summary/daily/{work_date}", response_model=APIResponse[DailyWorkSummary], summary="获取每日工作摘要")
async def get_daily_work_summary(
    work_date: date = Path(..., description="工作日期"),
    db: Session = Depends(get_db)
):
    """
    获取指定日期的工作摘要
    
    包含当日的工时统计、订单分布、工作类型分析等
    """
    service = WorkLogService(db)
    daily_summary = service.get_daily_work_summary(work_date)
    
    return create_response(
        success=True,
        data=daily_summary,
        message="每日工作摘要获取成功"
    )

@router.get("/work-logs/summary/weekly/{week_start}", response_model=APIResponse[WeeklyWorkSummary], summary="获取每周工作摘要")
async def get_weekly_work_summary(
    week_start: date = Path(..., description="周开始日期（周一）"),
    db: Session = Depends(get_db)
):
    """
    获取指定周的工作摘要
    
    包含一周的工时统计、每日分解、效率分析等
    """
    service = WorkLogService(db)
    weekly_summary = service.get_weekly_work_summary(week_start)
    
    return create_response(
        success=True,
        data=weekly_summary,
        message="每周工作摘要获取成功"
    )

@router.get("/work-logs/summary/monthly/{year}/{month}", response_model=APIResponse[MonthlyWorkSummary], summary="获取每月工作摘要")
async def get_monthly_work_summary(
    year: int = Path(..., description="年份"),
    month: int = Path(..., ge=1, le=12, description="月份"),
    db: Session = Depends(get_db)
):
    """
    获取指定月份的工作摘要
    
    包含整月的工时统计、周度分解、效率趋势等
    """
    service = WorkLogService(db)
    monthly_summary = service.get_monthly_work_summary(year, month)
    
    return create_response(
        success=True,
        data=monthly_summary,
        message="每月工作摘要获取成功"
    )

@router.get("/work-logs/summary/list", response_model=APIResponse[List[WorkLogSummary]], summary="获取工时记录摘要列表")
async def get_work_log_summaries(
    limit: int = Query(default=50, ge=1, le=100, description="返回数量限制"),
    order_id: str = Query(default=None, description="订单ID筛选"),
    db: Session = Depends(get_db)
):
    """
    获取工时记录摘要列表
    
    用于下拉选择等场景，返回简化的工时记录信息
    """
    params = WorkLogListParams(page=1, page_size=limit, order_id=order_id, sort_by="start_time", sort_order="desc")
    service = WorkLogService(db)
    work_logs, _ = service.list_work_logs(params)
    
    summaries = []
    for work_log in work_logs:
        summary = WorkLogSummary(
            id=work_log.id,
            order_id=work_log.order_id,
            order_title=work_log.order.title if work_log.order else "未知订单",
            work_date=work_log.work_date,
            duration=work_log.duration,
            duration_display=work_log.duration_display,
            work_type=work_log.work_type,
            billable=work_log.billable,
            earnings=work_log.earnings,
            is_running=work_log.is_running
        )
        summaries.append(summary)
    
    return create_response(
        success=True,
        data=summaries,
        message="工时记录摘要列表获取成功"
    )

@router.post("/work-logs/batch/update", response_model=APIResponse[List[WorkLogResponse]], summary="批量更新工时记录")
async def batch_update_work_logs(
    batch_data: WorkLogBatchUpdate,
    db: Session = Depends(get_db)
):
    """
    批量更新工时记录信息
    
    可以同时更新多个工时记录的相同字段
    """
    service = WorkLogService(db)
    work_logs = service.batch_update_work_logs(batch_data.work_log_ids, batch_data.update_data)
    
    work_log_responses = [WorkLogResponse.model_validate(work_log) for work_log in work_logs]
    
    return create_response(
        success=True,
        data=work_log_responses,
        message=f"成功更新 {len(work_logs)} 个工时记录"
    )

@router.post("/work-logs/batch/delete", response_model=SuccessResponse, summary="批量删除工时记录")
async def batch_delete_work_logs(
    batch_data: WorkLogBatchDelete,
    db: Session = Depends(get_db)
):
    """
    批量删除工时记录
    
    可以同时删除多个工时记录
    注意：正在计时的记录不能删除
    """
    service = WorkLogService(db)
    deleted_count = 0
    
    for work_log_id in batch_data.work_log_ids:
        try:
            service.delete_work_log(work_log_id)
            deleted_count += 1
        except Exception:
            # 跳过删除失败的记录
            continue
    
    return SuccessResponse(message=f"成功删除 {deleted_count} 个工时记录")
