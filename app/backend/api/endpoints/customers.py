"""
客户管理API路由

提供客户相关的RESTful API接口
包含客户CRUD操作、统计查询、批量操作等功能
"""

from typing import List
from fastapi import APIRouter, Depends, Query, Path, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.backend.schemas.customer import (
    CustomerCreate, CustomerUpdate, CustomerResponse, CustomerListParams,
    CustomerSummary, CustomerStatistics, CustomerOrderSummary,
    CustomerBatchUpdate, CustomerBatchDelete
)
from app.backend.schemas.base import (
    APIResponse, APIListResponse, SuccessResponse,
    create_response, create_list_response, create_pagination_info
)
from app.backend.services.customer_service import CustomerService
from app.core.exceptions import CustomerNotFoundException

# 创建路由器
router = APIRouter()

@router.post("/customers", response_model=APIResponse[CustomerResponse], summary="创建客户")
async def create_customer(
    customer_data: CustomerCreate,
    db: Session = Depends(get_db)
):
    """
    创建新客户
    
    - **name**: 客户姓名（必填）
    - **contact_phone**: 联系电话
    - **contact_email**: 邮箱地址
    - **contact_wechat**: 微信号
    - **contact_qq**: QQ号
    - **source**: 客户来源
    - **company**: 所属公司/机构
    - **position**: 职位
    - **notes**: 客户备注
    - **preferences**: 客户偏好设置
    """
    service = CustomerService(db)
    customer = service.create_customer(customer_data)
    
    return create_response(
        success=True,
        data=CustomerResponse.model_validate(customer),
        message="客户创建成功"
    )

@router.get("/customers/{customer_id}", response_model=APIResponse[CustomerResponse], summary="获取客户详情")
async def get_customer(
    customer_id: str = Path(..., description="客户ID"),
    db: Session = Depends(get_db)
):
    """
    根据ID获取客户详细信息
    
    返回客户的完整信息，包括统计数据和偏好设置
    """
    service = CustomerService(db)
    customer = service.get_customer(customer_id)
    
    return create_response(
        success=True,
        data=CustomerResponse.model_validate(customer),
        message="客户信息获取成功"
    )

@router.put("/customers/{customer_id}", response_model=APIResponse[CustomerResponse], summary="更新客户信息")
async def update_customer(
    customer_id: str = Path(..., description="客户ID"),
    customer_data: CustomerUpdate = Body(...),
    db: Session = Depends(get_db)
):
    """
    更新客户信息
    
    可以部分更新客户的任意字段
    """
    service = CustomerService(db)
    customer = service.update_customer(customer_id, customer_data)
    
    return create_response(
        success=True,
        data=CustomerResponse.model_validate(customer),
        message="客户信息更新成功"
    )

@router.delete("/customers/{customer_id}", response_model=SuccessResponse, summary="删除客户")
async def delete_customer(
    customer_id: str = Path(..., description="客户ID"),
    force: bool = Query(default=False, description="是否强制删除（即使有关联订单）"),
    db: Session = Depends(get_db)
):
    """
    删除客户
    
    - **force**: 是否强制删除，如果为false且客户有关联订单则删除失败
    """
    service = CustomerService(db)
    service.delete_customer(customer_id, force=force)
    
    return SuccessResponse(message="客户删除成功")

@router.get("/customers", response_model=APIListResponse[CustomerResponse], summary="获取客户列表")
async def list_customers(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    keyword: str = Query(default=None, description="搜索关键词"),
    source: str = Query(default=None, description="客户来源筛选"),
    is_vip: bool = Query(default=None, description="是否VIP客户筛选"),
    is_active: bool = Query(default=None, description="是否活跃客户筛选"),
    has_orders: bool = Query(default=None, description="是否有订单筛选"),
    sort_by: str = Query(default=None, description="排序字段"),
    sort_order: str = Query(default="desc", pattern="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db)
):
    """
    获取客户列表
    
    支持分页、搜索、筛选和排序功能
    
    **筛选条件:**
    - keyword: 在姓名、公司、邮箱、电话中搜索
    - source: 按客户来源筛选
    - is_vip: 筛选VIP客户
    - is_active: 筛选活跃客户
    - has_orders: 筛选有/无订单的客户
    
    **排序字段:**
    - name: 按姓名排序
    - created_at: 按创建时间排序
    - total_orders: 按订单数排序
    - total_amount: 按交易金额排序
    """
    params = CustomerListParams(
        page=page,
        page_size=page_size,
        keyword=keyword,
        source=source,
        is_vip=is_vip,
        is_active=is_active,
        has_orders=has_orders,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    service = CustomerService(db)
    customers, total = service.list_customers(params)
    
    # 转换为响应模型
    customer_responses = [CustomerResponse.model_validate(customer) for customer in customers]
    
    # 创建分页信息
    pagination = create_pagination_info(page, page_size, total)
    
    return create_list_response(
        data=customer_responses,
        pagination=pagination,
        message="客户列表获取成功"
    )

@router.get("/customers/summary/list", response_model=APIResponse[List[CustomerSummary]], summary="获取客户摘要列表")
async def get_customer_summaries(
    keyword: str = Query(default=None, description="搜索关键词"),
    limit: int = Query(default=50, ge=1, le=100, description="返回数量限制"),
    db: Session = Depends(get_db)
):
    """
    获取客户摘要列表
    
    用于下拉选择等场景，返回简化的客户信息
    """
    service = CustomerService(db)
    
    if keyword:
        customers = service.search_customers(keyword, limit)
    else:
        # 获取最近的客户
        params = CustomerListParams(page=1, page_size=limit, sort_by="created_at", sort_order="desc")
        customers, _ = service.list_customers(params)
    
    summaries = []
    for customer in customers:
        summary = CustomerSummary(
            id=customer.id,
            name=customer.name,
            display_name=customer.display_name,
            primary_contact=customer.primary_contact,
            is_vip=customer.is_vip,
            total_orders=customer.total_orders
        )
        summaries.append(summary)
    
    return create_response(
        success=True,
        data=summaries,
        message="客户摘要列表获取成功"
    )

@router.get("/customers/statistics", response_model=APIResponse[CustomerStatistics], summary="获取客户统计信息")
async def get_customer_statistics(db: Session = Depends(get_db)):
    """
    获取客户统计信息
    
    包含客户总数、来源分布、满意度分布等统计数据
    """
    service = CustomerService(db)
    statistics = service.get_customer_statistics()
    
    return create_response(
        success=True,
        data=statistics,
        message="客户统计信息获取成功"
    )

@router.get("/customers/{customer_id}/orders/summary", response_model=APIResponse[CustomerOrderSummary], summary="获取客户订单摘要")
async def get_customer_order_summary(
    customer_id: str = Path(..., description="客户ID"),
    db: Session = Depends(get_db)
):
    """
    获取客户的订单摘要信息
    
    包含订单数量、金额统计、状态分布等信息
    """
    service = CustomerService(db)
    summary = service.get_customer_order_summary(customer_id)
    
    return create_response(
        success=True,
        data=summary,
        message="客户订单摘要获取成功"
    )

@router.post("/customers/{customer_id}/statistics/update", response_model=APIResponse[CustomerResponse], summary="更新客户统计信息")
async def update_customer_statistics(
    customer_id: str = Path(..., description="客户ID"),
    db: Session = Depends(get_db)
):
    """
    手动更新客户的统计信息
    
    重新计算客户的订单数量、交易金额等统计数据
    """
    service = CustomerService(db)
    customer = service.update_customer_statistics(customer_id)
    
    return create_response(
        success=True,
        data=CustomerResponse.model_validate(customer),
        message="客户统计信息更新成功"
    )

@router.get("/customers/vip/list", response_model=APIResponse[List[CustomerResponse]], summary="获取VIP客户列表")
async def get_vip_customers(db: Session = Depends(get_db)):
    """
    获取VIP客户列表
    
    返回所有VIP客户，按交易金额倒序排列
    """
    service = CustomerService(db)
    vip_customers = service.get_vip_customers()
    
    customer_responses = [CustomerResponse.model_validate(customer) for customer in vip_customers]
    
    return create_response(
        success=True,
        data=customer_responses,
        message="VIP客户列表获取成功"
    )

@router.post("/customers/batch/update", response_model=APIResponse[List[CustomerResponse]], summary="批量更新客户")
async def batch_update_customers(
    batch_data: CustomerBatchUpdate,
    db: Session = Depends(get_db)
):
    """
    批量更新客户信息
    
    可以同时更新多个客户的相同字段
    """
    service = CustomerService(db)
    customers = service.batch_update_customers(batch_data.customer_ids, batch_data.update_data)
    
    customer_responses = [CustomerResponse.model_validate(customer) for customer in customers]
    
    return create_response(
        success=True,
        data=customer_responses,
        message=f"成功更新 {len(customers)} 个客户"
    )

@router.post("/customers/batch/delete", response_model=SuccessResponse, summary="批量删除客户")
async def batch_delete_customers(
    batch_data: CustomerBatchDelete,
    db: Session = Depends(get_db)
):
    """
    批量删除客户
    
    可以同时删除多个客户
    """
    service = CustomerService(db)
    deleted_count = 0
    
    for customer_id in batch_data.customer_ids:
        try:
            service.delete_customer(customer_id, force=batch_data.force_delete)
            deleted_count += 1
        except CustomerNotFoundException:
            # 跳过不存在的客户
            continue
    
    return SuccessResponse(message=f"成功删除 {deleted_count} 个客户")

@router.get("/customers/search", response_model=APIResponse[List[CustomerSummary]], summary="搜索客户")
async def search_customers(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(default=10, ge=1, le=50, description="返回数量限制"),
    db: Session = Depends(get_db)
):
    """
    搜索客户
    
    在客户姓名、公司、邮箱、电话中进行模糊搜索
    """
    service = CustomerService(db)
    customers = service.search_customers(q, limit)
    
    summaries = []
    for customer in customers:
        summary = CustomerSummary(
            id=customer.id,
            name=customer.name,
            display_name=customer.display_name,
            primary_contact=customer.primary_contact,
            is_vip=customer.is_vip,
            total_orders=customer.total_orders
        )
        summaries.append(summary)
    
    return create_response(
        success=True,
        data=summaries,
        message=f"找到 {len(summaries)} 个匹配的客户"
    )
