"""
文件附件管理API路由

提供文件附件相关的RESTful API接口
包含文件上传、下载、预览、版本控制等功能
"""

from typing import List
from fastapi import APIRouter, Depends, Query, Path, Body, UploadFile, File, Form
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.orm import Session
import io

from app.core.database import get_db
from app.core.config import Settings, get_settings
from app.backend.schemas.attachment import (
    AttachmentCreate, AttachmentUpdate, AttachmentResponse, AttachmentListParams,
    AttachmentSummary, AttachmentStatistics, FileUploadRequest, FileUploadResponse,
    FileVersionHistory, FilePreviewInfo, FileStorageInfo
)
from app.backend.schemas.base import (
    APIResponse, APIListResponse, SuccessResponse,
    create_response, create_list_response, create_pagination_info
)
from app.backend.services.attachment_service import AttachmentService

# 创建路由器
router = APIRouter()

@router.post("/attachments/upload", response_model=APIResponse[FileUploadResponse], summary="上传文件")
async def upload_file(
    file: UploadFile = File(..., description="上传的文件"),
    order_id: str = Form(..., description="订单ID"),
    category: str = Form(default="document", description="文件分类"),
    description: str = Form(default=None, description="文件描述"),
    tags: str = Form(default=None, description="文件标签"),
    is_deliverable: bool = Form(default=False, description="是否为交付物"),
    replace_existing: bool = Form(default=False, description="是否替换同名文件"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    上传文件
    
    支持的文件类型：
    - 文档：PDF, DOC, DOCX, TXT, RTF, ODT
    - 图片：JPG, JPEG, PNG, GIF, BMP, WEBP
    - 代码：PY, JS, HTML, CSS, JSON, XML
    - 压缩包：ZIP, RAR, 7Z, TAR, GZ
    
    **参数说明：**
    - file: 要上传的文件
    - order_id: 关联的订单ID
    - category: 文件分类（document/image/code/archive/contract/deliverable/reference/other）
    - description: 文件描述
    - tags: 文件标签（逗号分隔）
    - is_deliverable: 是否为交付物
    - replace_existing: 是否替换同名文件（会创建新版本）
    """
    upload_request = FileUploadRequest(
        order_id=order_id,
        category=category,
        description=description,
        tags=tags,
        is_deliverable=is_deliverable,
        replace_existing=replace_existing
    )
    
    service = AttachmentService(db, settings)
    attachment = service.upload_file(file, upload_request)
    
    # 构建响应
    response_data = FileUploadResponse(
        file_id=attachment.id,
        original_name=attachment.original_name,
        file_name=attachment.file_name,
        file_size=attachment.file_size,
        file_type=attachment.file_type,
        upload_time=attachment.upload_time,
        download_url=f"/api/v1/attachments/{attachment.id}/download",
        preview_url=f"/api/v1/attachments/{attachment.id}/preview" if service.can_preview_file(attachment) else None
    )
    
    return create_response(
        success=True,
        data=response_data,
        message="文件上传成功"
    )

@router.get("/attachments/statistics", response_model=APIResponse[AttachmentStatistics], summary="获取文件统计信息")
async def get_attachment_statistics(
    order_id: str = Query(default=None, description="订单ID筛选"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取文件统计信息

    包含文件总数、大小分布、分类统计等数据
    """
    service = AttachmentService(db, settings)
    statistics = service.get_attachment_statistics(order_id)

    return create_response(
        success=True,
        data=statistics,
        message="文件统计信息获取成功"
    )

@router.get("/attachments/storage/info", response_model=APIResponse[FileStorageInfo], summary="获取存储信息")
async def get_storage_info(
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取文件存储信息

    包含存储使用情况、清理建议、大文件列表等
    """
    service = AttachmentService(db, settings)
    storage_info = service.get_storage_info()

    return create_response(
        success=True,
        data=storage_info,
        message="存储信息获取成功"
    )

@router.get("/attachments/{attachment_id}", response_model=APIResponse[AttachmentResponse], summary="获取文件详情")
async def get_attachment(
    attachment_id: str = Path(..., description="文件ID"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    根据ID获取文件详细信息

    返回文件的完整信息，包括文件属性、关联订单等
    """
    service = AttachmentService(db, settings)
    attachment = service.get_attachment(attachment_id)

    return create_response(
        success=True,
        data=AttachmentResponse.model_validate(attachment),
        message="文件信息获取成功"
    )

@router.put("/attachments/{attachment_id}", response_model=APIResponse[AttachmentResponse], summary="更新文件信息")
async def update_attachment(
    attachment_id: str = Path(..., description="文件ID"),
    attachment_data: AttachmentUpdate = Body(...),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    更新文件信息
    
    可以更新文件的分类、描述、标签等元数据信息
    注意：不能修改文件内容，如需更新文件内容请重新上传
    """
    service = AttachmentService(db, settings)
    attachment = service.update_attachment(attachment_id, attachment_data)
    
    return create_response(
        success=True,
        data=AttachmentResponse.model_validate(attachment),
        message="文件信息更新成功"
    )

@router.delete("/attachments/{attachment_id}", response_model=SuccessResponse, summary="删除文件")
async def delete_attachment(
    attachment_id: str = Path(..., description="文件ID"),
    force: bool = Query(default=False, description="是否强制删除（删除磁盘文件）"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    删除文件
    
    - **force=false**: 软删除，只标记为无效，不删除磁盘文件
    - **force=true**: 硬删除，同时删除数据库记录和磁盘文件
    """
    service = AttachmentService(db, settings)
    service.delete_attachment(attachment_id, force=force)
    
    return SuccessResponse(message="文件删除成功")

@router.get("/attachments", response_model=APIListResponse[AttachmentResponse], summary="获取文件列表")
async def list_attachments(
    page: int = Query(default=1, ge=1, description="页码"),
    page_size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    keyword: str = Query(default=None, description="搜索关键词"),
    order_id: str = Query(default=None, description="订单ID筛选"),
    category: str = Query(default=None, description="文件分类筛选"),
    file_type: str = Query(default=None, description="文件类型筛选"),
    is_deliverable: bool = Query(default=None, description="是否为交付物筛选"),
    is_active: bool = Query(default=None, description="是否有效筛选"),
    include_versions: bool = Query(default=False, description="是否包含历史版本"),
    sort_by: str = Query(default=None, description="排序字段"),
    sort_order: str = Query(default="desc", pattern="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取文件列表
    
    支持分页、搜索、筛选和排序功能
    
    **筛选条件:**
    - keyword: 在文件名、描述、标签中搜索
    - order_id: 按订单筛选
    - category: 按分类筛选
    - file_type: 按文件类型筛选
    - is_deliverable: 筛选交付物文件
    - is_active: 筛选有效文件
    - include_versions: 是否包含历史版本
    
    **排序字段:**
    - original_name: 按文件名排序
    - file_size: 按文件大小排序
    - upload_time: 按上传时间排序
    - category: 按分类排序
    """
    params = AttachmentListParams(
        page=page,
        page_size=page_size,
        keyword=keyword,
        order_id=order_id,
        category=category,
        file_type=file_type,
        is_deliverable=is_deliverable,
        is_active=is_active,
        include_versions=include_versions,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    service = AttachmentService(db, settings)
    attachments, total = service.list_attachments(params)
    
    # 转换为响应模型
    attachment_responses = [AttachmentResponse.model_validate(attachment) for attachment in attachments]
    
    # 创建分页信息
    pagination = create_pagination_info(page, page_size, total)
    
    return create_list_response(
        data=attachment_responses,
        pagination=pagination,
        message="文件列表获取成功"
    )

@router.get("/attachments/{attachment_id}/download", summary="下载文件")
async def download_file(
    attachment_id: str = Path(..., description="文件ID"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    下载文件
    
    返回文件的二进制内容，浏览器会自动下载文件
    """
    service = AttachmentService(db, settings)
    attachment = service.get_attachment(attachment_id)
    file_path = service.get_file_download_path(attachment_id)
    
    return FileResponse(
        path=file_path,
        filename=attachment.original_name,
        media_type=attachment.mime_type or 'application/octet-stream'
    )

@router.get("/attachments/{attachment_id}/preview", response_model=APIResponse[FilePreviewInfo], summary="预览文件")
async def preview_file(
    attachment_id: str = Path(..., description="文件ID"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    预览文件
    
    支持预览的文件类型：
    - 图片：JPG, PNG, GIF等
    - 文本：TXT, MD, JSON, XML, CSV
    - 代码：PY, JS, HTML, CSS等
    
    对于不支持预览的文件类型，返回基本信息
    """
    service = AttachmentService(db, settings)
    attachment = service.get_attachment(attachment_id)
    
    can_preview = service.can_preview_file(attachment)
    preview_info = FilePreviewInfo(
        file_id=attachment.id,
        can_preview=can_preview,
        preview_type="text" if can_preview else None,
        preview_url=f"/api/v1/attachments/{attachment.id}/content" if can_preview else None,
        thumbnail_url=None,  # 缩略图功能可以后续扩展
        content_preview=None
    )
    
    # 如果可以预览且是文本文件，获取内容预览
    if can_preview and attachment.file_extension.lower() in ['.txt', '.md', '.json', '.xml', '.csv']:
        try:
            content = service.get_file_preview_content(attachment_id, max_size=1024)  # 1KB预览
            preview_info.content_preview = content[:500] + "..." if len(content) > 500 else content
        except Exception:
            pass
    
    return create_response(
        success=True,
        data=preview_info,
        message="文件预览信息获取成功"
    )

@router.get("/attachments/{attachment_id}/content", summary="获取文件内容")
async def get_file_content(
    attachment_id: str = Path(..., description="文件ID"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取文件内容
    
    用于在线预览文本文件、代码文件等
    """
    service = AttachmentService(db, settings)
    attachment = service.get_attachment(attachment_id)
    
    if attachment.is_image:
        # 对于图片，返回文件流
        file_path = service.get_file_download_path(attachment_id)
        return FileResponse(
            path=file_path,
            media_type=attachment.mime_type or 'image/jpeg'
        )
    else:
        # 对于文本文件，返回内容
        content = service.get_file_preview_content(attachment_id)
        return StreamingResponse(
            io.StringIO(content),
            media_type="text/plain; charset=utf-8"
        )

@router.get("/attachments/{attachment_id}/versions", response_model=APIResponse[FileVersionHistory], summary="获取文件版本历史")
async def get_file_versions(
    attachment_id: str = Path(..., description="文件ID"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取文件版本历史
    
    返回文件的所有版本信息，包括版本号、大小、上传时间等
    """
    service = AttachmentService(db, settings)
    version_history = service.get_file_version_history(attachment_id)
    
    return create_response(
        success=True,
        data=version_history,
        message="文件版本历史获取成功"
    )

@router.get("/attachments/statistics", response_model=APIResponse[AttachmentStatistics], summary="获取文件统计信息")
async def get_attachment_statistics(
    order_id: str = Query(default=None, description="订单ID筛选"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取文件统计信息
    
    包含文件总数、大小分布、分类统计等数据
    """
    service = AttachmentService(db, settings)
    statistics = service.get_attachment_statistics(order_id)
    
    return create_response(
        success=True,
        data=statistics,
        message="文件统计信息获取成功"
    )

@router.get("/attachments/storage/info", response_model=APIResponse[FileStorageInfo], summary="获取存储信息")
async def get_storage_info(
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取文件存储信息
    
    包含存储使用情况、清理建议、大文件列表等
    """
    service = AttachmentService(db, settings)
    storage_info = service.get_storage_info()
    
    return create_response(
        success=True,
        data=storage_info,
        message="存储信息获取成功"
    )

@router.get("/attachments/order/{order_id}/list", response_model=APIResponse[List[AttachmentSummary]], summary="获取订单的文件列表")
async def get_attachments_by_order(
    order_id: str = Path(..., description="订单ID"),
    category: str = Query(default=None, description="文件分类筛选"),
    is_deliverable: bool = Query(default=None, description="是否为交付物筛选"),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings)
):
    """
    获取指定订单的所有文件
    
    返回简化的文件信息列表
    """
    params = AttachmentListParams(
        page=1,
        page_size=1000,  # 获取所有文件
        order_id=order_id,
        category=category,
        is_deliverable=is_deliverable,
        is_active=True,
        sort_by="upload_time",
        sort_order="desc"
    )
    
    service = AttachmentService(db, settings)
    attachments, _ = service.list_attachments(params)
    
    summaries = []
    for attachment in attachments:
        summary = AttachmentSummary(
            id=attachment.id,
            order_id=attachment.order_id,
            original_name=attachment.original_name,
            file_type=attachment.file_type,
            file_size=attachment.file_size,
            file_size_display=service._format_file_size(attachment.file_size),
            category=attachment.category,
            version=attachment.version,
            is_deliverable=attachment.is_deliverable,
            upload_time=attachment.upload_time,
            can_preview=service.can_preview_file(attachment)
        )
        summaries.append(summary)
    
    return create_response(
        success=True,
        data=summaries,
        message="订单文件列表获取成功"
    )
