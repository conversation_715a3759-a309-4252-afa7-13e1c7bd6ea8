"""
客户相关数据模型

定义客户管理相关的请求和响应数据结构
包含客户创建、更新、查询等操作的数据验证
"""

from decimal import Decimal
from typing import Optional, List, Dict, Any
from datetime import date
from pydantic import Field, EmailStr, validator

from app.backend.schemas.base import (
    BaseSchema, IDSchema, TimestampSchema, 
    CustomerSourceEnum, ListParams
)

class CustomerBase(BaseSchema):
    """
    客户基础信息
    
    包含客户的基本字段，用于创建和更新操作
    """
    
    name: str = Field(..., min_length=1, max_length=100, description="客户姓名")
    contact_phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    contact_email: Optional[EmailStr] = Field(None, description="邮箱地址")
    contact_wechat: Optional[str] = Field(None, max_length=50, description="微信号")
    contact_qq: Optional[str] = Field(None, max_length=20, description="QQ号")
    source: CustomerSourceEnum = Field(default=CustomerSourceEnum.DIRECT, description="客户来源")
    company: Optional[str] = Field(None, max_length=200, description="所属公司/机构")
    position: Optional[str] = Field(None, max_length=100, description="职位")
    notes: Optional[str] = Field(None, description="客户备注")
    
    @validator('contact_phone')
    def validate_phone(cls, v):
        """验证电话号码格式"""
        if v and not v.replace('-', '').replace(' ', '').isdigit():
            raise ValueError('电话号码格式不正确')
        return v
    
    @validator('name')
    def validate_name(cls, v):
        """验证姓名不能为空"""
        if not v or not v.strip():
            raise ValueError('客户姓名不能为空')
        return v.strip()

class CustomerCreate(CustomerBase):
    """
    创建客户请求
    
    用于创建新客户的数据结构
    """
    
    # 创建时可以设置偏好
    preferences: Optional[Dict[str, Any]] = Field(default=None, description="客户偏好设置")
    
    @validator('preferences')
    def validate_preferences(cls, v):
        """验证偏好设置格式"""
        if v is None:
            return None
        
        # 确保偏好设置包含必要字段
        default_prefs = {
            "preferred_categories": [],
            "preferred_billing": "fixed",
            "budget_range": {"min": 0, "max": 0},
            "communication_preference": "wechat",
            "delivery_preference": "email",
            "special_requirements": ""
        }
        
        # 合并默认设置
        if isinstance(v, dict):
            default_prefs.update(v)
            return default_prefs
        
        return default_prefs

class CustomerUpdate(BaseSchema):
    """
    更新客户请求
    
    用于更新客户信息的数据结构，所有字段都是可选的
    """
    
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="客户姓名")
    contact_phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    contact_email: Optional[EmailStr] = Field(None, description="邮箱地址")
    contact_wechat: Optional[str] = Field(None, max_length=50, description="微信号")
    contact_qq: Optional[str] = Field(None, max_length=20, description="QQ号")
    source: Optional[CustomerSourceEnum] = Field(None, description="客户来源")
    company: Optional[str] = Field(None, max_length=200, description="所属公司/机构")
    position: Optional[str] = Field(None, max_length=100, description="职位")
    notes: Optional[str] = Field(None, description="客户备注")
    preferences: Optional[Dict[str, Any]] = Field(None, description="客户偏好设置")
    satisfaction_rating: Optional[int] = Field(None, ge=1, le=5, description="满意度评级")
    is_active: Optional[bool] = Field(None, description="是否活跃客户")
    is_vip: Optional[bool] = Field(None, description="是否VIP客户")

class CustomerResponse(CustomerBase, IDSchema, TimestampSchema):
    """
    客户响应数据
    
    返回客户信息时使用的数据结构
    """
    
    preferences: Optional[Dict[str, Any]] = Field(None, description="客户偏好设置")
    satisfaction_rating: Optional[int] = Field(None, description="满意度评级")
    total_orders: int = Field(default=0, description="总订单数")
    total_amount: Decimal = Field(default=Decimal('0.00'), description="总交易金额")
    last_order_date: Optional[date] = Field(None, description="最后订单日期")
    is_active: bool = Field(default=True, description="是否活跃客户")
    is_vip: bool = Field(default=False, description="是否VIP客户")
    
    # 计算属性
    display_name: Optional[str] = Field(None, description="显示名称")
    primary_contact: Optional[str] = Field(None, description="主要联系方式")
    average_order_amount: Optional[Decimal] = Field(None, description="平均订单金额")

class CustomerListParams(ListParams):
    """
    客户列表查询参数
    
    扩展基础列表参数，添加客户特定的筛选条件
    """
    
    source: Optional[CustomerSourceEnum] = Field(None, description="客户来源筛选")
    is_vip: Optional[bool] = Field(None, description="是否VIP客户筛选")
    is_active: Optional[bool] = Field(None, description="是否活跃客户筛选")
    has_orders: Optional[bool] = Field(None, description="是否有订单筛选")
    min_total_amount: Optional[Decimal] = Field(None, ge=0, description="最小交易金额")
    max_total_amount: Optional[Decimal] = Field(None, ge=0, description="最大交易金额")

class CustomerSummary(BaseSchema):
    """
    客户摘要信息
    
    用于下拉选择等场景的简化客户信息
    """
    
    id: str = Field(..., description="客户ID")
    name: str = Field(..., description="客户姓名")
    display_name: str = Field(..., description="显示名称")
    primary_contact: str = Field(..., description="主要联系方式")
    is_vip: bool = Field(..., description="是否VIP客户")
    total_orders: int = Field(..., description="总订单数")

class CustomerStatistics(BaseSchema):
    """
    客户统计信息
    
    用于客户分析和报表的统计数据
    """
    
    total_customers: int = Field(..., description="客户总数")
    active_customers: int = Field(..., description="活跃客户数")
    vip_customers: int = Field(..., description="VIP客户数")
    new_customers_this_month: int = Field(..., description="本月新增客户数")
    
    # 来源分布
    source_distribution: Dict[str, int] = Field(..., description="客户来源分布")
    
    # 满意度分布
    satisfaction_distribution: Dict[str, int] = Field(..., description="满意度分布")
    
    # 交易金额分布
    amount_distribution: Dict[str, int] = Field(..., description="交易金额分布")

class CustomerPreferences(BaseSchema):
    """
    客户偏好设置
    
    详细的客户偏好配置
    """
    
    preferred_categories: List[str] = Field(default=[], description="偏好的项目类型")
    preferred_billing: str = Field(default="fixed", description="偏好的计费方式")
    budget_range: Dict[str, Decimal] = Field(
        default={"min": Decimal('0'), "max": Decimal('0')}, 
        description="预算范围"
    )
    communication_preference: str = Field(default="wechat", description="沟通偏好")
    delivery_preference: str = Field(default="email", description="交付偏好")
    special_requirements: str = Field(default="", description="特殊要求")
    
    @validator('budget_range')
    def validate_budget_range(cls, v):
        """验证预算范围"""
        if not isinstance(v, dict):
            return {"min": Decimal('0'), "max": Decimal('0')}
        
        min_val = Decimal(str(v.get('min', 0)))
        max_val = Decimal(str(v.get('max', 0)))
        
        if min_val < 0:
            min_val = Decimal('0')
        if max_val < 0:
            max_val = Decimal('0')
        if max_val > 0 and min_val > max_val:
            min_val = max_val
        
        return {"min": min_val, "max": max_val}

class CustomerOrderSummary(BaseSchema):
    """
    客户订单摘要
    
    客户相关的订单统计信息
    """
    
    customer_id: str = Field(..., description="客户ID")
    total_orders: int = Field(..., description="总订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    in_progress_orders: int = Field(..., description="进行中订单数")
    total_amount: Decimal = Field(..., description="总交易金额")
    average_order_amount: Decimal = Field(..., description="平均订单金额")
    last_order_date: Optional[date] = Field(None, description="最后订单日期")
    
    # 按状态分组的订单数
    orders_by_status: Dict[str, int] = Field(..., description="按状态分组的订单数")
    
    # 按分类分组的订单数
    orders_by_category: Dict[str, int] = Field(..., description="按分类分组的订单数")

# 批量操作相关
class CustomerBatchUpdate(BaseSchema):
    """
    客户批量更新
    
    用于批量更新客户信息
    """
    
    customer_ids: List[str] = Field(..., min_items=1, description="客户ID列表")
    update_data: CustomerUpdate = Field(..., description="更新数据")

class CustomerBatchDelete(BaseSchema):
    """
    客户批量删除
    
    用于批量删除客户
    """
    
    customer_ids: List[str] = Field(..., min_items=1, description="客户ID列表")
    force_delete: bool = Field(default=False, description="是否强制删除（即使有关联订单）")

# 导入导出相关
class CustomerExportParams(BaseSchema):
    """
    客户导出参数
    
    用于客户数据导出的参数配置
    """
    
    format: str = Field(default="excel", pattern="^(excel|csv|json)$", description="导出格式")
    include_orders: bool = Field(default=False, description="是否包含订单信息")
    include_statistics: bool = Field(default=False, description="是否包含统计信息")
    filter_params: Optional[CustomerListParams] = Field(None, description="筛选参数")

class CustomerImportData(BaseSchema):
    """
    客户导入数据
    
    用于批量导入客户数据
    """
    
    customers: List[CustomerCreate] = Field(..., min_items=1, description="客户数据列表")
    skip_duplicates: bool = Field(default=True, description="是否跳过重复数据")
    update_existing: bool = Field(default=False, description="是否更新已存在的客户")
