"""
基础数据模型定义

提供通用的Pydantic模型和响应格式
定义API的标准输入输出结构
"""

from datetime import datetime
from typing import Any, Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field, ConfigDict

# 泛型类型变量
T = TypeVar('T')

class BaseSchema(BaseModel):
    """
    基础Schema类
    
    所有Pydantic模型的基类
    提供通用配置和方法
    """
    
    model_config = ConfigDict(
        # 允许从ORM模型创建
        from_attributes=True,
        # 验证赋值
        validate_assignment=True,
        # 使用枚举值
        use_enum_values=True,
        # 允许额外字段（在某些情况下）
        extra='forbid'
    )

class TimestampSchema(BaseSchema):
    """
    时间戳Schema混入
    
    包含创建时间和更新时间字段
    """
    
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class IDSchema(BaseSchema):
    """
    ID Schema混入
    
    包含ID字段
    """
    
    id: str = Field(..., description="唯一标识符")

class PaginationParams(BaseSchema):
    """
    分页参数
    
    用于列表查询的分页参数
    """
    
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.page_size

class SortParams(BaseSchema):
    """
    排序参数
    
    用于列表查询的排序参数
    """
    
    sort_by: Optional[str] = Field(default=None, description="排序字段")
    sort_order: Optional[str] = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")

class SearchParams(BaseSchema):
    """
    搜索参数
    
    用于列表查询的搜索参数
    """
    
    keyword: Optional[str] = Field(default=None, description="搜索关键词")
    
class ListParams(PaginationParams, SortParams, SearchParams):
    """
    列表查询参数
    
    组合分页、排序、搜索参数
    """
    pass

class APIResponse(BaseSchema, Generic[T]):
    """
    标准API响应格式
    
    所有API接口的统一响应格式
    """
    
    success: bool = Field(..., description="请求是否成功")
    data: Optional[T] = Field(default=None, description="响应数据")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")

class APIListResponse(BaseSchema, Generic[T]):
    """
    列表API响应格式
    
    包含分页信息的列表响应格式
    """
    
    success: bool = Field(..., description="请求是否成功")
    data: List[T] = Field(..., description="数据列表")
    pagination: "PaginationInfo" = Field(..., description="分页信息")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")

class PaginationInfo(BaseSchema):
    """
    分页信息
    
    包含分页相关的元数据
    """
    
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total: int = Field(..., description="总记录数")
    total_pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

class ErrorResponse(BaseSchema):
    """
    错误响应格式
    
    API错误时的响应格式
    """
    
    success: bool = Field(default=False, description="请求是否成功")
    data: Optional[Any] = Field(default=None, description="错误数据")
    message: str = Field(..., description="错误消息")
    error_code: str = Field(..., description="错误码")
    details: Optional[List[str]] = Field(default=None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")

class SuccessResponse(BaseSchema):
    """
    成功响应格式
    
    简单成功操作的响应格式
    """
    
    success: bool = Field(default=True, description="请求是否成功")
    message: str = Field(default="操作成功", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")

# 状态枚举
from enum import Enum

class OrderStatusEnum(str, Enum):
    """订单状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    REVISING = "revising"
    REVIEWING = "reviewing"
    COMPLETED = "completed"
    SETTLED = "settled"
    CANCELLED = "cancelled"
    PAUSED = "paused"

class BillingMethodEnum(str, Enum):
    """计费方式枚举"""
    FIXED = "fixed"
    HOURLY = "hourly"
    WORD_COUNT = "word_count"
    PAGE_COUNT = "page_count"

class CustomerSourceEnum(str, Enum):
    """客户来源枚举"""
    FRIEND = "friend"
    PLATFORM = "platform"
    DIRECT = "direct"
    REFERRAL = "referral"

class CategoryTypeEnum(str, Enum):
    """分类类型枚举"""
    DEVELOPMENT = "development"
    WRITING = "writing"
    CONTENT = "content"
    CONSULTING = "consulting"

# 工具函数
def create_response(
    success: bool = True,
    data: Any = None,
    message: str = "操作成功"
) -> dict:
    """
    创建标准响应
    
    Args:
        success: 是否成功
        data: 响应数据
        message: 响应消息
        
    Returns:
        dict: 标准响应格式
    """
    return {
        "success": success,
        "data": data,
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    }

def create_list_response(
    data: List[Any],
    pagination: PaginationInfo,
    message: str = "查询成功"
) -> dict:
    """
    创建列表响应
    
    Args:
        data: 数据列表
        pagination: 分页信息
        message: 响应消息
        
    Returns:
        dict: 列表响应格式
    """
    return {
        "success": True,
        "data": data,
        "pagination": pagination.model_dump(),
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    }

def create_pagination_info(
    page: int,
    page_size: int,
    total: int
) -> PaginationInfo:
    """
    创建分页信息
    
    Args:
        page: 当前页码
        page_size: 每页数量
        total: 总记录数
        
    Returns:
        PaginationInfo: 分页信息
    """
    total_pages = (total + page_size - 1) // page_size
    
    return PaginationInfo(
        page=page,
        page_size=page_size,
        total=total,
        total_pages=total_pages,
        has_next=page < total_pages,
        has_prev=page > 1
    )
