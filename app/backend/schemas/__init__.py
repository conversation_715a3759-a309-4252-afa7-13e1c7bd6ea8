"""
数据模型包初始化

Pydantic数据验证模型定义
"""

from app.backend.schemas.base import (
    BaseSchema, TimestampSchema, IDSchema,
    PaginationParams, SortParams, SearchParams, ListParams,
    APIResponse, APIListResponse, PaginationInfo,
    ErrorResponse, SuccessResponse,
    create_response, create_list_response, create_pagination_info
)

from app.backend.schemas.customer import (
    CustomerCreate, CustomerUpdate, CustomerResponse,
    CustomerListParams, CustomerSummary, CustomerStatistics
)

__all__ = [
    # 基础模型
    "BaseSchema", "TimestampSchema", "IDSchema",
    "PaginationParams", "SortParams", "SearchParams", "ListParams",
    "APIResponse", "APIListResponse", "PaginationInfo",
    "ErrorResponse", "SuccessResponse",
    "create_response", "create_list_response", "create_pagination_info",
    
    # 客户模型
    "CustomerCreate", "CustomerUpdate", "CustomerResponse",
    "CustomerListParams", "CustomerSummary", "CustomerStatistics"
]
