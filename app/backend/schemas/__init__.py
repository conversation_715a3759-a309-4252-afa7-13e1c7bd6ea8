"""
数据模型包初始化

Pydantic数据验证模型定义
"""

from app.backend.schemas.base import (
    BaseSchema, TimestampSchema, IDSchema,
    PaginationParams, SortParams, SearchParams, ListParams,
    APIResponse, APIListResponse, PaginationInfo,
    ErrorResponse, SuccessResponse,
    create_response, create_list_response, create_pagination_info
)

from app.backend.schemas.customer import (
    CustomerCreate, CustomerUpdate, CustomerResponse,
    CustomerListParams, CustomerSummary, CustomerStatistics
)

from app.backend.schemas.order import (
    OrderCreate, OrderUpdate, OrderResponse,
    OrderListParams, OrderSummary, OrderStatistics,
    OrderStatusUpdate, OrderAmountCalculation
)

from app.backend.schemas.work_log import (
    WorkLogCreate, WorkLogUpdate, WorkLogResponse,
    WorkLogListParams, WorkLogSummary, WorkLogStatistics,
    TimerOperation, TimerStatus, DailyWorkSummary
)

from app.backend.schemas.analytics import (
    BusinessOverview, RevenueAnalysis, CustomerAnalysis,
    ProjectAnalysis, WorkEfficiencyAnalysis, PerformanceMetrics,
    AnalyticsQuery, AnalyticsReport
)

__all__ = [
    # 基础模型
    "BaseSchema", "TimestampSchema", "IDSchema",
    "PaginationParams", "SortParams", "SearchParams", "ListParams",
    "APIResponse", "APIListResponse", "PaginationInfo",
    "ErrorResponse", "SuccessResponse",
    "create_response", "create_list_response", "create_pagination_info",
    
    # 客户模型
    "CustomerCreate", "CustomerUpdate", "CustomerResponse",
    "CustomerListParams", "CustomerSummary", "CustomerStatistics",

    # 订单模型
    "OrderCreate", "OrderUpdate", "OrderResponse",
    "OrderListParams", "OrderSummary", "OrderStatistics",
    "OrderStatusUpdate", "OrderAmountCalculation",

    # 工时模型
    "WorkLogCreate", "WorkLogUpdate", "WorkLogResponse",
    "WorkLogListParams", "WorkLogSummary", "WorkLogStatistics",
    "TimerOperation", "TimerStatus", "DailyWorkSummary",

    # 统计分析模型
    "BusinessOverview", "RevenueAnalysis", "CustomerAnalysis",
    "ProjectAnalysis", "WorkEfficiencyAnalysis", "PerformanceMetrics",
    "AnalyticsQuery", "AnalyticsReport"
]
