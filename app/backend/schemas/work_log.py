"""
工时记录相关数据模型

定义工时管理相关的请求和响应数据结构
包含工时记录、计时器操作、统计分析等数据验证
"""

from decimal import Decimal
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from pydantic import Field, validator

from app.backend.schemas.base import (
    BaseSchema, IDSchema, TimestampSchema, ListParams
)

class WorkLogBase(BaseSchema):
    """
    工时记录基础信息
    
    包含工时记录的基本字段，用于创建和更新操作
    """
    
    order_id: str = Field(..., description="订单ID")
    description: Optional[str] = Field(None, description="工作内容描述")
    work_type: Optional[str] = Field(None, max_length=50, description="工作类型")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="时薪")
    billable: bool = Field(default=True, description="是否计费")
    notes: Optional[str] = Field(None, description="备注")
    
    @validator('order_id')
    def validate_order_id(cls, v):
        """验证订单ID格式"""
        if not v or not v.strip():
            raise ValueError('订单ID不能为空')
        return v.strip()

class WorkLogCreate(WorkLogBase):
    """
    创建工时记录请求
    
    用于创建新工时记录的数据结构
    """
    
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: Optional[int] = Field(None, ge=0, description="工作时长(分钟)")
    
    @validator('end_time')
    def validate_end_time(cls, v, values):
        """验证结束时间不能早于开始时间"""
        if v and 'start_time' in values and values['start_time']:
            if v < values['start_time']:
                raise ValueError('结束时间不能早于开始时间')
        return v
    
    @validator('duration')
    def validate_duration(cls, v, values):
        """验证或计算工作时长"""
        start_time = values.get('start_time')
        end_time = values.get('end_time')
        
        # 如果提供了开始和结束时间，自动计算时长
        if start_time and end_time:
            calculated_duration = int((end_time - start_time).total_seconds() / 60)
            if v is not None and abs(v - calculated_duration) > 1:  # 允许1分钟误差
                raise ValueError('手动设置的时长与计算时长不匹配')
            return calculated_duration
        
        # 如果只提供了开始时间，时长可以为空（用于计时器）
        if start_time and not end_time:
            return v or 0
        
        # 如果都没提供时间，必须提供时长
        if not start_time and not end_time and v is None:
            raise ValueError('必须提供时间信息或工作时长')
        
        return v

class WorkLogUpdate(BaseSchema):
    """
    更新工时记录请求
    
    用于更新工时记录信息的数据结构，所有字段都是可选的
    """
    
    description: Optional[str] = Field(None, description="工作内容描述")
    work_type: Optional[str] = Field(None, max_length=50, description="工作类型")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="时薪")
    billable: Optional[bool] = Field(None, description="是否计费")
    notes: Optional[str] = Field(None, description="备注")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: Optional[int] = Field(None, ge=0, description="工作时长(分钟)")

class WorkLogResponse(WorkLogBase, IDSchema, TimestampSchema):
    """
    工时记录响应数据
    
    返回工时记录信息时使用的数据结构
    """
    
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    duration: int = Field(..., description="工作时长(分钟)")
    is_running: bool = Field(..., description="是否正在计时")
    
    # 关联信息
    order_title: Optional[str] = Field(None, description="订单标题")
    customer_name: Optional[str] = Field(None, description="客户姓名")
    
    # 计算属性
    duration_hours: Optional[float] = Field(None, description="工作时长(小时)")
    duration_display: Optional[str] = Field(None, description="工作时长显示格式")
    earnings: Optional[float] = Field(None, description="本次工作收入")
    work_date: Optional[str] = Field(None, description="工作日期")
    efficiency_score: Optional[float] = Field(None, description="工作效率得分")

class WorkLogListParams(ListParams):
    """
    工时记录列表查询参数
    
    扩展基础列表参数，添加工时记录特定的筛选条件
    """
    
    order_id: Optional[str] = Field(None, description="订单ID筛选")
    work_type: Optional[str] = Field(None, description="工作类型筛选")
    billable: Optional[bool] = Field(None, description="是否计费筛选")
    is_running: Optional[bool] = Field(None, description="是否正在计时筛选")
    
    # 时间范围筛选
    start_date: Optional[date] = Field(None, description="开始日期筛选")
    end_date: Optional[date] = Field(None, description="结束日期筛选")
    work_date_from: Optional[date] = Field(None, description="工作日期范围-起始")
    work_date_to: Optional[date] = Field(None, description="工作日期范围-结束")
    
    # 时长筛选
    min_duration: Optional[int] = Field(None, ge=0, description="最小工作时长(分钟)")
    max_duration: Optional[int] = Field(None, ge=0, description="最大工作时长(分钟)")
    
    # 收入筛选
    min_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="最小时薪")
    max_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="最大时薪")

class WorkLogSummary(BaseSchema):
    """
    工时记录摘要信息
    
    用于简化显示的工时记录信息
    """
    
    id: str = Field(..., description="工时记录ID")
    order_id: str = Field(..., description="订单ID")
    order_title: str = Field(..., description="订单标题")
    work_date: str = Field(..., description="工作日期")
    duration: int = Field(..., description="工作时长(分钟)")
    duration_display: str = Field(..., description="工作时长显示格式")
    work_type: Optional[str] = Field(None, description="工作类型")
    billable: bool = Field(..., description="是否计费")
    earnings: float = Field(..., description="本次工作收入")
    is_running: bool = Field(..., description="是否正在计时")

class WorkLogStatistics(BaseSchema):
    """
    工时统计信息
    
    用于工时分析和报表的统计数据
    """
    
    total_logs: int = Field(..., description="工时记录总数")
    total_duration: int = Field(..., description="总工作时长(分钟)")
    total_hours: float = Field(..., description="总工作时长(小时)")
    billable_duration: int = Field(..., description="可计费工作时长(分钟)")
    billable_hours: float = Field(..., description="可计费工作时长(小时)")
    total_earnings: Decimal = Field(..., description="总收入")
    average_hourly_rate: float = Field(..., description="平均时薪")
    average_efficiency_score: float = Field(..., description="平均效率得分")
    
    # 分布统计
    work_type_distribution: Dict[str, int] = Field(..., description="工作类型分布")
    daily_distribution: Dict[str, int] = Field(..., description="每日工时分布")
    hourly_distribution: Dict[str, int] = Field(..., description="每小时工时分布")
    
    # 趋势数据
    daily_hours: List[Dict[str, Any]] = Field(..., description="每日工时趋势")
    weekly_summary: Dict[str, Any] = Field(..., description="周度汇总")
    monthly_summary: Dict[str, Any] = Field(..., description="月度汇总")

class TimerOperation(BaseSchema):
    """
    计时器操作
    
    用于计时器控制的数据结构
    """
    
    operation: str = Field(..., pattern="^(start|stop|pause|resume)$", description="操作类型")
    order_id: str = Field(..., description="订单ID")
    description: Optional[str] = Field(None, description="工作内容描述")
    work_type: Optional[str] = Field(None, description="工作类型")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="时薪")
    billable: bool = Field(default=True, description="是否计费")

class TimerStatus(BaseSchema):
    """
    计时器状态
    
    返回当前计时器状态的数据结构
    """
    
    is_running: bool = Field(..., description="是否正在计时")
    work_log_id: Optional[str] = Field(None, description="工时记录ID")
    order_id: Optional[str] = Field(None, description="订单ID")
    order_title: Optional[str] = Field(None, description="订单标题")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    current_duration: Optional[int] = Field(None, description="当前工作时长(分钟)")
    current_duration_display: Optional[str] = Field(None, description="当前工作时长显示格式")

class DailyWorkSummary(BaseSchema):
    """
    每日工作摘要
    
    用于日报和工作总结的数据结构
    """
    
    work_date: date = Field(..., description="工作日期")
    total_logs: int = Field(..., description="工时记录数")
    total_duration: int = Field(..., description="总工作时长(分钟)")
    total_hours: float = Field(..., description="总工作时长(小时)")
    billable_hours: float = Field(..., description="可计费工作时长(小时)")
    total_earnings: Decimal = Field(..., description="总收入")
    average_hourly_rate: float = Field(..., description="平均时薪")
    
    # 工作分布
    orders_worked: List[Dict[str, Any]] = Field(..., description="工作的订单列表")
    work_types: Dict[str, int] = Field(..., description="工作类型分布")
    hourly_breakdown: List[Dict[str, Any]] = Field(..., description="每小时工作分解")

class WeeklyWorkSummary(BaseSchema):
    """
    每周工作摘要
    
    用于周报和工作总结的数据结构
    """
    
    week_start: date = Field(..., description="周开始日期")
    week_end: date = Field(..., description="周结束日期")
    total_hours: float = Field(..., description="总工作时长(小时)")
    billable_hours: float = Field(..., description="可计费工作时长(小时)")
    total_earnings: Decimal = Field(..., description="总收入")
    average_daily_hours: float = Field(..., description="平均每日工作时长")
    
    # 每日分解
    daily_summaries: List[DailyWorkSummary] = Field(..., description="每日工作摘要")
    
    # 周度统计
    most_productive_day: Optional[str] = Field(None, description="最高效的工作日")
    total_orders_worked: int = Field(..., description="工作的订单总数")
    completion_rate: float = Field(..., description="任务完成率")

class MonthlyWorkSummary(BaseSchema):
    """
    每月工作摘要
    
    用于月报和工作总结的数据结构
    """
    
    year: int = Field(..., description="年份")
    month: int = Field(..., description="月份")
    total_hours: float = Field(..., description="总工作时长(小时)")
    billable_hours: float = Field(..., description="可计费工作时长(小时)")
    total_earnings: Decimal = Field(..., description="总收入")
    average_daily_hours: float = Field(..., description="平均每日工作时长")
    working_days: int = Field(..., description="工作天数")
    
    # 月度趋势
    weekly_summaries: List[WeeklyWorkSummary] = Field(..., description="每周工作摘要")
    top_orders: List[Dict[str, Any]] = Field(..., description="工时最多的订单")
    efficiency_trend: List[Dict[str, Any]] = Field(..., description="效率趋势")

# 批量操作相关
class WorkLogBatchUpdate(BaseSchema):
    """
    工时记录批量更新
    
    用于批量更新工时记录信息
    """
    
    work_log_ids: List[str] = Field(..., min_items=1, description="工时记录ID列表")
    update_data: WorkLogUpdate = Field(..., description="更新数据")

class WorkLogBatchDelete(BaseSchema):
    """
    工时记录批量删除
    
    用于批量删除工时记录
    """
    
    work_log_ids: List[str] = Field(..., min_items=1, description="工时记录ID列表")

# 导入导出相关
class WorkLogExportParams(BaseSchema):
    """
    工时记录导出参数
    
    用于工时数据导出的参数配置
    """
    
    format: str = Field(default="excel", pattern="^(excel|csv|json)$", description="导出格式")
    include_order_info: bool = Field(default=True, description="是否包含订单信息")
    include_customer_info: bool = Field(default=True, description="是否包含客户信息")
    include_statistics: bool = Field(default=False, description="是否包含统计信息")
    filter_params: Optional[WorkLogListParams] = Field(None, description="筛选参数")

class WorkLogImportData(BaseSchema):
    """
    工时记录导入数据
    
    用于批量导入工时数据
    """
    
    work_logs: List[WorkLogCreate] = Field(..., min_items=1, description="工时记录数据列表")
    skip_duplicates: bool = Field(default=True, description="是否跳过重复数据")
    update_existing: bool = Field(default=False, description="是否更新已存在的记录")
