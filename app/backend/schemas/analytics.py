"""
统计分析相关数据模型

定义统计分析相关的请求和响应数据结构
包含综合仪表板、趋势分析、业务洞察等数据验证
"""

from decimal import Decimal
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from pydantic import Field, validator

from app.backend.schemas.base import BaseSchema

class BusinessOverview(BaseSchema):
    """
    业务概览
    
    综合业务仪表板的核心数据
    """
    
    # 基础指标
    total_customers: int = Field(..., description="客户总数")
    active_customers: int = Field(..., description="活跃客户数")
    total_orders: int = Field(..., description="订单总数")
    active_orders: int = Field(..., description="活跃订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    
    # 财务指标
    total_revenue: Decimal = Field(..., description="总收入")
    monthly_revenue: Decimal = Field(..., description="本月收入")
    pending_revenue: Decimal = Field(..., description="待收入金额")
    average_order_value: Decimal = Field(..., description="平均订单价值")
    
    # 工时指标
    total_work_hours: float = Field(..., description="总工作时长(小时)")
    monthly_work_hours: float = Field(..., description="本月工作时长(小时)")
    average_hourly_rate: float = Field(..., description="平均时薪")
    efficiency_score: float = Field(..., description="整体效率得分")
    
    # 增长指标
    customer_growth_rate: float = Field(..., description="客户增长率(%)")
    revenue_growth_rate: float = Field(..., description="收入增长率(%)")
    order_completion_rate: float = Field(..., description="订单完成率(%)")
    
    # 预警指标
    overdue_orders: int = Field(..., description="逾期订单数")
    low_satisfaction_customers: int = Field(..., description="低满意度客户数")
    inactive_customers: int = Field(..., description="不活跃客户数")

class RevenueAnalysis(BaseSchema):
    """
    收入分析
    
    详细的收入统计和趋势分析
    """
    
    # 收入统计
    total_revenue: Decimal = Field(..., description="总收入")
    completed_revenue: Decimal = Field(..., description="已完成收入")
    pending_revenue: Decimal = Field(..., description="待收入金额")
    monthly_revenue: Decimal = Field(..., description="本月收入")
    
    # 收入分布
    revenue_by_customer: List[Dict[str, Any]] = Field(..., description="按客户分组的收入")
    revenue_by_category: List[Dict[str, Any]] = Field(..., description="按分类分组的收入")
    revenue_by_billing_method: List[Dict[str, Any]] = Field(..., description="按计费方式分组的收入")
    
    # 时间趋势
    daily_revenue_trend: List[Dict[str, Any]] = Field(..., description="每日收入趋势")
    monthly_revenue_trend: List[Dict[str, Any]] = Field(..., description="每月收入趋势")
    
    # 预测数据
    revenue_forecast: List[Dict[str, Any]] = Field(..., description="收入预测")
    growth_rate: float = Field(..., description="收入增长率")
    
    # 收入质量
    recurring_revenue_ratio: float = Field(..., description="重复收入比例")
    high_value_customer_ratio: float = Field(..., description="高价值客户比例")

class CustomerAnalysis(BaseSchema):
    """
    客户分析
    
    客户价值、行为和满意度分析
    """
    
    # 客户统计
    total_customers: int = Field(..., description="客户总数")
    new_customers: int = Field(..., description="新客户数")
    active_customers: int = Field(..., description="活跃客户数")
    vip_customers: int = Field(..., description="VIP客户数")
    
    # 客户价值分析
    customer_lifetime_value: List[Dict[str, Any]] = Field(..., description="客户生命周期价值")
    top_customers: List[Dict[str, Any]] = Field(..., description="顶级客户列表")
    customer_segments: List[Dict[str, Any]] = Field(..., description="客户细分")
    
    # 客户行为
    customer_acquisition_trend: List[Dict[str, Any]] = Field(..., description="客户获取趋势")
    customer_retention_rate: float = Field(..., description="客户留存率")
    churn_rate: float = Field(..., description="客户流失率")
    
    # 满意度分析
    average_satisfaction: float = Field(..., description="平均满意度")
    satisfaction_distribution: Dict[str, int] = Field(..., description="满意度分布")
    satisfaction_trend: List[Dict[str, Any]] = Field(..., description="满意度趋势")
    
    # 客户来源
    acquisition_channels: Dict[str, int] = Field(..., description="获客渠道分布")
    channel_effectiveness: List[Dict[str, Any]] = Field(..., description="渠道效果分析")

class ProjectAnalysis(BaseSchema):
    """
    项目分析
    
    订单和项目的执行效率分析
    """
    
    # 项目统计
    total_projects: int = Field(..., description="项目总数")
    completed_projects: int = Field(..., description="已完成项目数")
    in_progress_projects: int = Field(..., description="进行中项目数")
    overdue_projects: int = Field(..., description="逾期项目数")
    
    # 执行效率
    average_completion_time: float = Field(..., description="平均完成时间(天)")
    on_time_delivery_rate: float = Field(..., description="按时交付率")
    project_success_rate: float = Field(..., description="项目成功率")
    
    # 项目分布
    projects_by_category: List[Dict[str, Any]] = Field(..., description="按分类分组的项目")
    projects_by_priority: List[Dict[str, Any]] = Field(..., description="按优先级分组的项目")
    projects_by_billing_method: List[Dict[str, Any]] = Field(..., description="按计费方式分组的项目")
    
    # 工时分析
    estimated_vs_actual_hours: List[Dict[str, Any]] = Field(..., description="预估vs实际工时对比")
    efficiency_by_project_type: List[Dict[str, Any]] = Field(..., description="按项目类型的效率分析")
    
    # 盈利分析
    most_profitable_projects: List[Dict[str, Any]] = Field(..., description="最盈利项目")
    profit_margin_by_category: List[Dict[str, Any]] = Field(..., description="按分类的利润率")

class WorkEfficiencyAnalysis(BaseSchema):
    """
    工作效率分析
    
    工时和生产力的深度分析
    """
    
    # 工时统计
    total_work_hours: float = Field(..., description="总工作时长(小时)")
    billable_hours: float = Field(..., description="可计费工作时长(小时)")
    billable_ratio: float = Field(..., description="可计费工时比例")
    
    # 效率指标
    average_efficiency_score: float = Field(..., description="平均效率得分")
    productivity_trend: List[Dict[str, Any]] = Field(..., description="生产力趋势")
    peak_productivity_hours: List[int] = Field(..., description="高效工作时段")
    
    # 工作模式
    work_pattern_analysis: Dict[str, Any] = Field(..., description="工作模式分析")
    optimal_work_duration: float = Field(..., description="最佳工作时长(小时)")
    break_frequency_recommendation: float = Field(..., description="建议休息频率(小时)")
    
    # 工作类型分析
    efficiency_by_work_type: List[Dict[str, Any]] = Field(..., description="按工作类型的效率")
    time_allocation: Dict[str, float] = Field(..., description="时间分配")
    
    # 改进建议
    efficiency_recommendations: List[str] = Field(..., description="效率改进建议")
    focus_areas: List[str] = Field(..., description="重点改进领域")

class TrendAnalysis(BaseSchema):
    """
    趋势分析
    
    业务趋势和预测分析
    """
    
    # 收入趋势
    revenue_trend: List[Dict[str, Any]] = Field(..., description="收入趋势")
    revenue_forecast: List[Dict[str, Any]] = Field(..., description="收入预测")
    seasonal_patterns: Dict[str, Any] = Field(..., description="季节性模式")
    
    # 客户趋势
    customer_acquisition_trend: List[Dict[str, Any]] = Field(..., description="客户获取趋势")
    customer_retention_trend: List[Dict[str, Any]] = Field(..., description="客户留存趋势")
    
    # 工作量趋势
    workload_trend: List[Dict[str, Any]] = Field(..., description="工作量趋势")
    capacity_utilization: List[Dict[str, Any]] = Field(..., description="产能利用率")
    
    # 市场趋势
    demand_forecast: List[Dict[str, Any]] = Field(..., description="需求预测")
    market_opportunities: List[str] = Field(..., description="市场机会")
    
    # 风险预警
    risk_indicators: List[Dict[str, Any]] = Field(..., description="风险指标")
    early_warnings: List[str] = Field(..., description="早期预警")

class PerformanceMetrics(BaseSchema):
    """
    绩效指标
    
    关键绩效指标(KPI)的综合评估
    """
    
    # 财务KPI
    revenue_growth: float = Field(..., description="收入增长率(%)")
    profit_margin: float = Field(..., description="利润率(%)")
    average_project_value: Decimal = Field(..., description="平均项目价值")
    
    # 客户KPI
    customer_satisfaction: float = Field(..., description="客户满意度")
    customer_retention_rate: float = Field(..., description="客户留存率(%)")
    net_promoter_score: float = Field(..., description="净推荐值")
    
    # 运营KPI
    project_delivery_rate: float = Field(..., description="项目交付率(%)")
    resource_utilization: float = Field(..., description="资源利用率(%)")
    quality_score: float = Field(..., description="质量得分")
    
    # 效率KPI
    productivity_index: float = Field(..., description="生产力指数")
    efficiency_score: float = Field(..., description="效率得分")
    time_to_completion: float = Field(..., description="平均完成时间(天)")
    
    # 综合评分
    overall_performance_score: float = Field(..., description="综合绩效得分")
    performance_grade: str = Field(..., description="绩效等级")
    improvement_areas: List[str] = Field(..., description="改进领域")

class AnalyticsQuery(BaseSchema):
    """
    分析查询参数
    
    用于自定义分析查询的参数
    """
    
    # 时间范围
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    period: Optional[str] = Field(None, pattern="^(daily|weekly|monthly|quarterly|yearly)$", description="时间周期")
    
    # 筛选条件
    customer_ids: Optional[List[str]] = Field(None, description="客户ID列表")
    category_ids: Optional[List[str]] = Field(None, description="分类ID列表")
    order_statuses: Optional[List[str]] = Field(None, description="订单状态列表")
    
    # 分析维度
    group_by: Optional[str] = Field(None, description="分组维度")
    metrics: Optional[List[str]] = Field(None, description="指标列表")
    
    # 比较分析
    compare_period: Optional[bool] = Field(False, description="是否进行同期比较")
    benchmark: Optional[str] = Field(None, description="基准对比")

class AnalyticsReport(BaseSchema):
    """
    分析报告
    
    综合分析报告的数据结构
    """
    
    report_id: str = Field(..., description="报告ID")
    report_type: str = Field(..., description="报告类型")
    generated_at: datetime = Field(..., description="生成时间")
    period: str = Field(..., description="报告周期")
    
    # 报告内容
    executive_summary: str = Field(..., description="执行摘要")
    key_insights: List[str] = Field(..., description="关键洞察")
    recommendations: List[str] = Field(..., description="建议")
    
    # 数据内容
    business_overview: BusinessOverview = Field(..., description="业务概览")
    revenue_analysis: RevenueAnalysis = Field(..., description="收入分析")
    customer_analysis: CustomerAnalysis = Field(..., description="客户分析")
    project_analysis: ProjectAnalysis = Field(..., description="项目分析")
    efficiency_analysis: WorkEfficiencyAnalysis = Field(..., description="效率分析")
    trend_analysis: TrendAnalysis = Field(..., description="趋势分析")
    performance_metrics: PerformanceMetrics = Field(..., description="绩效指标")
    
    # 附加信息
    data_quality_score: float = Field(..., description="数据质量得分")
    confidence_level: float = Field(..., description="置信度")
    next_review_date: date = Field(..., description="下次审查日期")

class DashboardWidget(BaseSchema):
    """
    仪表板组件
    
    仪表板中单个组件的数据结构
    """
    
    widget_id: str = Field(..., description="组件ID")
    widget_type: str = Field(..., description="组件类型")
    title: str = Field(..., description="组件标题")
    description: Optional[str] = Field(None, description="组件描述")
    
    # 数据内容
    data: Dict[str, Any] = Field(..., description="组件数据")
    chart_config: Optional[Dict[str, Any]] = Field(None, description="图表配置")
    
    # 显示配置
    position: Dict[str, int] = Field(..., description="位置信息")
    size: Dict[str, int] = Field(..., description="尺寸信息")
    refresh_interval: Optional[int] = Field(None, description="刷新间隔(秒)")
    
    # 状态信息
    last_updated: datetime = Field(..., description="最后更新时间")
    is_loading: bool = Field(default=False, description="是否正在加载")
    has_error: bool = Field(default=False, description="是否有错误")

class CustomAnalytics(BaseSchema):
    """
    自定义分析
    
    用户自定义分析查询的结果
    """
    
    query_id: str = Field(..., description="查询ID")
    query_name: str = Field(..., description="查询名称")
    created_at: datetime = Field(..., description="创建时间")
    
    # 查询参数
    query_params: AnalyticsQuery = Field(..., description="查询参数")
    
    # 结果数据
    result_data: List[Dict[str, Any]] = Field(..., description="结果数据")
    summary_stats: Dict[str, Any] = Field(..., description="汇总统计")
    
    # 可视化配置
    chart_type: str = Field(..., description="图表类型")
    chart_config: Dict[str, Any] = Field(..., description="图表配置")
    
    # 导出选项
    export_formats: List[str] = Field(default=["json", "csv", "excel"], description="支持的导出格式")
    is_scheduled: bool = Field(default=False, description="是否定时生成")
    schedule_config: Optional[Dict[str, Any]] = Field(None, description="定时配置")
