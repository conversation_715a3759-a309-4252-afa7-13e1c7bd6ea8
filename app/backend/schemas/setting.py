"""
系统配置相关数据模型

定义系统配置管理相关的请求和响应数据结构
包含系统设置、用户偏好、业务规则等数据验证
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from pydantic import Field, validator

from app.backend.schemas.base import (
    BaseSchema, IDSchema, TimestampSchema, ListParams
)

class SettingBase(BaseSchema):
    """
    系统配置基础信息
    
    包含系统配置的基本字段，用于创建和更新操作
    """
    
    config_key: str = Field(..., min_length=1, max_length=100, description="配置键")
    config_value: Optional[str] = Field(None, description="配置值")
    default_value: Optional[str] = Field(None, description="默认值")
    description: Optional[str] = Field(None, description="配置描述")
    category: str = Field(default="general", max_length=50, description="配置分类")
    value_type: str = Field(default="string", max_length=20, description="值类型")
    is_system: bool = Field(default=False, description="是否为系统配置")
    is_readonly: bool = Field(default=False, description="是否只读")
    is_required: bool = Field(default=False, description="是否必需")
    
    @validator('config_key')
    def validate_config_key(cls, v):
        """验证配置键格式"""
        if not v or not v.strip():
            raise ValueError('配置键不能为空')
        
        # 检查配置键格式（只允许字母、数字、点、下划线）
        import re
        if not re.match(r'^[a-zA-Z0-9._]+$', v):
            raise ValueError('配置键只能包含字母、数字、点和下划线')
        
        return v.strip()
    
    @validator('value_type')
    def validate_value_type(cls, v):
        """验证值类型"""
        valid_types = ['string', 'integer', 'float', 'boolean', 'json', 'text']
        if v not in valid_types:
            raise ValueError(f'无效的值类型，支持的类型: {", ".join(valid_types)}')
        return v
    
    @validator('category')
    def validate_category(cls, v):
        """验证配置分类"""
        valid_categories = [
            'general', 'application', 'interface', 'business', 
            'notification', 'data', 'security', 'performance'
        ]
        if v not in valid_categories:
            raise ValueError(f'无效的配置分类，支持的分类: {", ".join(valid_categories)}')
        return v

class SettingCreate(SettingBase):
    """
    创建系统配置请求
    
    用于创建新系统配置的数据结构
    """
    pass

class SettingUpdate(BaseSchema):
    """
    更新系统配置请求
    
    用于更新系统配置信息的数据结构，所有字段都是可选的
    """
    
    config_value: Optional[str] = Field(None, description="配置值")
    description: Optional[str] = Field(None, description="配置描述")
    category: Optional[str] = Field(None, description="配置分类")
    is_readonly: Optional[bool] = Field(None, description="是否只读")
    is_required: Optional[bool] = Field(None, description="是否必需")

class SettingResponse(SettingBase, IDSchema, TimestampSchema):
    """
    系统配置响应数据
    
    返回系统配置信息时使用的数据结构
    """
    
    # 计算属性
    parsed_value: Optional[Union[str, int, float, bool, Dict, List]] = Field(None, description="解析后的值")
    is_default: Optional[bool] = Field(None, description="是否为默认值")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")

class SettingListParams(ListParams):
    """
    系统配置列表查询参数
    
    扩展基础列表参数，添加系统配置特定的筛选条件
    """
    
    category: Optional[str] = Field(None, description="配置分类筛选")
    value_type: Optional[str] = Field(None, description="值类型筛选")
    is_system: Optional[bool] = Field(None, description="是否为系统配置筛选")
    is_readonly: Optional[bool] = Field(None, description="是否只读筛选")
    is_required: Optional[bool] = Field(None, description="是否必需筛选")
    
    # 值筛选
    has_value: Optional[bool] = Field(None, description="是否有配置值")
    is_default: Optional[bool] = Field(None, description="是否为默认值")

class SettingSummary(BaseSchema):
    """
    系统配置摘要信息
    
    用于简化显示的系统配置信息
    """
    
    id: str = Field(..., description="配置ID")
    config_key: str = Field(..., description="配置键")
    config_value: Optional[str] = Field(None, description="配置值")
    description: str = Field(..., description="配置描述")
    category: str = Field(..., description="配置分类")
    value_type: str = Field(..., description="值类型")
    is_system: bool = Field(..., description="是否为系统配置")
    is_readonly: bool = Field(..., description="是否只读")

class SettingCategory(BaseSchema):
    """
    配置分类信息
    
    用于配置分类管理的数据结构
    """
    
    category: str = Field(..., description="分类名称")
    display_name: str = Field(..., description="显示名称")
    description: str = Field(..., description="分类描述")
    icon: Optional[str] = Field(None, description="分类图标")
    order: int = Field(default=0, description="排序顺序")
    settings_count: int = Field(..., description="配置项数量")

class SettingGroup(BaseSchema):
    """
    配置分组
    
    按分类分组的配置信息
    """
    
    category: str = Field(..., description="分类名称")
    display_name: str = Field(..., description="显示名称")
    description: str = Field(..., description="分类描述")
    settings: List[SettingResponse] = Field(..., description="配置列表")

class SystemInfo(BaseSchema):
    """
    系统信息
    
    系统运行状态和基本信息
    """
    
    # 应用信息
    app_name: str = Field(..., description="应用名称")
    app_version: str = Field(..., description="应用版本")
    app_description: str = Field(..., description="应用描述")
    
    # 系统状态
    system_status: str = Field(..., description="系统状态")
    uptime: str = Field(..., description="运行时间")
    last_restart: Optional[datetime] = Field(None, description="最后重启时间")
    
    # 数据库信息
    database_type: str = Field(..., description="数据库类型")
    database_size: str = Field(..., description="数据库大小")
    total_records: int = Field(..., description="总记录数")
    
    # 存储信息
    storage_used: str = Field(..., description="已使用存储")
    storage_available: str = Field(..., description="可用存储")
    storage_usage_percentage: float = Field(..., description="存储使用率")
    
    # 性能指标
    memory_usage: str = Field(..., description="内存使用")
    cpu_usage: float = Field(..., description="CPU使用率")
    response_time: float = Field(..., description="平均响应时间")

class ConfigBackup(BaseSchema):
    """
    配置备份
    
    系统配置的备份和恢复数据结构
    """
    
    backup_id: str = Field(..., description="备份ID")
    backup_name: str = Field(..., description="备份名称")
    backup_time: datetime = Field(..., description="备份时间")
    backup_size: str = Field(..., description="备份大小")
    description: Optional[str] = Field(None, description="备份描述")
    
    # 备份内容
    settings_count: int = Field(..., description="配置项数量")
    categories: List[str] = Field(..., description="包含的分类")
    
    # 备份状态
    is_auto: bool = Field(default=False, description="是否自动备份")
    is_valid: bool = Field(default=True, description="备份是否有效")

class ConfigRestore(BaseSchema):
    """
    配置恢复
    
    从备份恢复配置的参数
    """
    
    backup_id: str = Field(..., description="备份ID")
    restore_categories: Optional[List[str]] = Field(None, description="要恢复的分类")
    overwrite_existing: bool = Field(default=False, description="是否覆盖现有配置")
    create_backup_before_restore: bool = Field(default=True, description="恢复前是否创建备份")

class SettingValidation(BaseSchema):
    """
    配置验证
    
    配置值的验证规则和结果
    """
    
    config_key: str = Field(..., description="配置键")
    config_value: str = Field(..., description="配置值")
    is_valid: bool = Field(..., description="是否有效")
    validation_errors: List[str] = Field(default=[], description="验证错误")
    suggestions: List[str] = Field(default=[], description="建议")

class SettingBatchUpdate(BaseSchema):
    """
    批量更新配置
    
    用于批量更新多个配置项
    """
    
    settings: List[Dict[str, Any]] = Field(..., min_items=1, description="配置列表")
    create_backup: bool = Field(default=True, description="是否创建备份")

class SettingExport(BaseSchema):
    """
    配置导出
    
    配置数据的导出参数
    """
    
    categories: Optional[List[str]] = Field(None, description="要导出的分类")
    include_system: bool = Field(default=False, description="是否包含系统配置")
    include_readonly: bool = Field(default=False, description="是否包含只读配置")
    format: str = Field(default="json", pattern="^(json|yaml|ini)$", description="导出格式")

class SettingImport(BaseSchema):
    """
    配置导入
    
    配置数据的导入参数
    """
    
    data: Dict[str, Any] = Field(..., description="导入数据")
    overwrite_existing: bool = Field(default=False, description="是否覆盖现有配置")
    validate_before_import: bool = Field(default=True, description="导入前是否验证")
    create_backup: bool = Field(default=True, description="导入前是否创建备份")

class UserPreferences(BaseSchema):
    """
    用户偏好设置
    
    用户个人的偏好配置
    """
    
    # 界面偏好
    theme: str = Field(default="light", description="界面主题")
    language: str = Field(default="zh_CN", description="界面语言")
    timezone: str = Field(default="Asia/Shanghai", description="时区")
    date_format: str = Field(default="YYYY-MM-DD", description="日期格式")
    time_format: str = Field(default="HH:mm:ss", description="时间格式")
    
    # 工作偏好
    default_hourly_rate: Optional[float] = Field(None, description="默认时薪")
    preferred_billing_method: str = Field(default="fixed", description="偏好计费方式")
    work_hours_per_day: int = Field(default=8, description="每日工作小时")
    break_reminder_interval: int = Field(default=60, description="休息提醒间隔(分钟)")
    
    # 通知偏好
    email_notifications: bool = Field(default=True, description="邮件通知")
    desktop_notifications: bool = Field(default=True, description="桌面通知")
    sound_notifications: bool = Field(default=False, description="声音通知")
    notification_quiet_hours: Dict[str, str] = Field(
        default={"start": "22:00", "end": "08:00"}, 
        description="免打扰时间"
    )
    
    # 数据偏好
    auto_save_interval: int = Field(default=300, description="自动保存间隔(秒)")
    backup_frequency: str = Field(default="weekly", description="备份频率")
    data_retention_days: int = Field(default=365, description="数据保留天数")

class SystemMaintenance(BaseSchema):
    """
    系统维护
    
    系统维护操作的参数和状态
    """
    
    operation: str = Field(..., description="维护操作")
    description: str = Field(..., description="操作描述")
    scheduled_time: Optional[datetime] = Field(None, description="计划时间")
    estimated_duration: Optional[int] = Field(None, description="预计时长(分钟)")
    
    # 操作状态
    status: str = Field(default="pending", description="操作状态")
    progress: float = Field(default=0.0, description="进度百分比")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    
    # 结果信息
    success: bool = Field(default=False, description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    result_summary: Optional[Dict[str, Any]] = Field(None, description="结果摘要")
