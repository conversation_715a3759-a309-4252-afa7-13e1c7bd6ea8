"""
文件附件相关数据模型

定义文件管理相关的请求和响应数据结构
包含文件上传、下载、版本控制等数据验证
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import Field, validator

from app.backend.schemas.base import (
    BaseSchema, IDSchema, TimestampSchema, ListParams
)

class AttachmentBase(BaseSchema):
    """
    文件附件基础信息
    
    包含文件附件的基本字段，用于创建和更新操作
    """
    
    order_id: str = Field(..., description="订单ID")
    original_name: str = Field(..., min_length=1, max_length=255, description="原始文件名")
    category: str = Field(default="document", max_length=50, description="文件分类")
    description: Optional[str] = Field(None, description="文件描述")
    tags: Optional[str] = Field(None, max_length=500, description="文件标签（逗号分隔）")
    is_deliverable: bool = Field(default=False, description="是否为交付物")
    
    @validator('order_id')
    def validate_order_id(cls, v):
        """验证订单ID格式"""
        if not v or not v.strip():
            raise ValueError('订单ID不能为空')
        return v.strip()
    
    @validator('original_name')
    def validate_original_name(cls, v):
        """验证原始文件名"""
        if not v or not v.strip():
            raise ValueError('文件名不能为空')
        
        # 检查文件名中的非法字符
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in invalid_chars:
            if char in v:
                raise ValueError(f'文件名不能包含字符: {char}')
        
        return v.strip()
    
    @validator('category')
    def validate_category(cls, v):
        """验证文件分类"""
        valid_categories = [
            'document', 'image', 'code', 'archive', 'contract', 
            'deliverable', 'reference', 'other'
        ]
        if v not in valid_categories:
            raise ValueError(f'无效的文件分类，支持的分类: {", ".join(valid_categories)}')
        return v

class AttachmentCreate(AttachmentBase):
    """
    创建文件附件请求
    
    用于创建新文件附件的数据结构
    """
    pass

class AttachmentUpdate(BaseSchema):
    """
    更新文件附件请求
    
    用于更新文件附件信息的数据结构，所有字段都是可选的
    """
    
    category: Optional[str] = Field(None, max_length=50, description="文件分类")
    description: Optional[str] = Field(None, description="文件描述")
    tags: Optional[str] = Field(None, max_length=500, description="文件标签")
    is_deliverable: Optional[bool] = Field(None, description="是否为交付物")
    is_active: Optional[bool] = Field(None, description="是否有效")

class AttachmentResponse(AttachmentBase, IDSchema, TimestampSchema):
    """
    文件附件响应数据
    
    返回文件附件信息时使用的数据结构
    """
    
    file_name: str = Field(..., description="存储文件名")
    file_path: str = Field(..., description="文件路径")
    file_type: str = Field(..., description="文件类型")
    file_size: int = Field(..., description="文件大小(字节)")
    mime_type: Optional[str] = Field(None, description="MIME类型")
    version: int = Field(..., description="文件版本")
    parent_id: Optional[str] = Field(None, description="父文件ID")
    is_active: bool = Field(..., description="是否有效")
    upload_time: datetime = Field(..., description="上传时间")
    
    # 关联信息
    order_title: Optional[str] = Field(None, description="订单标题")
    
    # 计算属性
    file_extension: Optional[str] = Field(None, description="文件扩展名")
    file_size_display: Optional[str] = Field(None, description="文件大小显示格式")
    is_image: Optional[bool] = Field(None, description="是否为图片文件")
    is_document: Optional[bool] = Field(None, description="是否为文档文件")
    is_code: Optional[bool] = Field(None, description="是否为代码文件")
    is_archive: Optional[bool] = Field(None, description="是否为压缩文件")
    can_preview: Optional[bool] = Field(None, description="是否可预览")
    download_url: Optional[str] = Field(None, description="下载链接")
    preview_url: Optional[str] = Field(None, description="预览链接")
    tags_list: Optional[List[str]] = Field(None, description="标签列表")

class AttachmentListParams(ListParams):
    """
    文件附件列表查询参数
    
    扩展基础列表参数，添加文件附件特定的筛选条件
    """
    
    order_id: Optional[str] = Field(None, description="订单ID筛选")
    category: Optional[str] = Field(None, description="文件分类筛选")
    file_type: Optional[str] = Field(None, description="文件类型筛选")
    is_deliverable: Optional[bool] = Field(None, description="是否为交付物筛选")
    is_active: Optional[bool] = Field(None, description="是否有效筛选")
    
    # 文件大小筛选
    min_size: Optional[int] = Field(None, ge=0, description="最小文件大小(字节)")
    max_size: Optional[int] = Field(None, ge=0, description="最大文件大小(字节)")
    
    # 时间范围筛选
    upload_date_from: Optional[datetime] = Field(None, description="上传日期范围-起始")
    upload_date_to: Optional[datetime] = Field(None, description="上传日期范围-结束")
    
    # 文件类型筛选
    file_extensions: Optional[str] = Field(None, description="文件扩展名列表（逗号分隔）")
    include_versions: Optional[bool] = Field(default=False, description="是否包含历史版本")

class AttachmentSummary(BaseSchema):
    """
    文件附件摘要信息
    
    用于简化显示的文件附件信息
    """
    
    id: str = Field(..., description="文件ID")
    order_id: str = Field(..., description="订单ID")
    original_name: str = Field(..., description="原始文件名")
    file_type: str = Field(..., description="文件类型")
    file_size: int = Field(..., description="文件大小(字节)")
    file_size_display: str = Field(..., description="文件大小显示格式")
    category: str = Field(..., description="文件分类")
    version: int = Field(..., description="文件版本")
    is_deliverable: bool = Field(..., description="是否为交付物")
    upload_time: datetime = Field(..., description="上传时间")
    can_preview: bool = Field(..., description="是否可预览")

class AttachmentStatistics(BaseSchema):
    """
    文件附件统计信息
    
    用于文件管理分析和报表的统计数据
    """
    
    total_files: int = Field(..., description="文件总数")
    total_size: int = Field(..., description="总文件大小(字节)")
    total_size_display: str = Field(..., description="总文件大小显示格式")
    
    # 分类统计
    category_distribution: Dict[str, int] = Field(..., description="分类分布")
    file_type_distribution: Dict[str, int] = Field(..., description="文件类型分布")
    
    # 大小统计
    average_file_size: float = Field(..., description="平均文件大小(字节)")
    largest_file_size: int = Field(..., description="最大文件大小(字节)")
    
    # 时间统计
    upload_trend: List[Dict[str, Any]] = Field(..., description="上传趋势")
    recent_uploads: int = Field(..., description="最近上传数量")
    
    # 版本统计
    files_with_versions: int = Field(..., description="有版本的文件数")
    total_versions: int = Field(..., description="总版本数")
    
    # 交付物统计
    deliverable_files: int = Field(..., description="交付物文件数")
    deliverable_size: int = Field(..., description="交付物总大小(字节)")

class FileUploadRequest(BaseSchema):
    """
    文件上传请求
    
    用于文件上传的参数配置
    """
    
    order_id: str = Field(..., description="订单ID")
    category: str = Field(default="document", description="文件分类")
    description: Optional[str] = Field(None, description="文件描述")
    tags: Optional[str] = Field(None, description="文件标签")
    is_deliverable: bool = Field(default=False, description="是否为交付物")
    replace_existing: bool = Field(default=False, description="是否替换同名文件")

class FileUploadResponse(BaseSchema):
    """
    文件上传响应
    
    文件上传成功后的响应数据
    """
    
    file_id: str = Field(..., description="文件ID")
    original_name: str = Field(..., description="原始文件名")
    file_name: str = Field(..., description="存储文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    file_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(..., description="上传时间")
    download_url: str = Field(..., description="下载链接")
    preview_url: Optional[str] = Field(None, description="预览链接")

class FileVersionInfo(BaseSchema):
    """
    文件版本信息
    
    用于文件版本管理的数据结构
    """
    
    version: int = Field(..., description="版本号")
    file_id: str = Field(..., description="文件ID")
    file_size: int = Field(..., description="文件大小(字节)")
    upload_time: datetime = Field(..., description="上传时间")
    description: Optional[str] = Field(None, description="版本描述")
    is_current: bool = Field(..., description="是否为当前版本")

class FileVersionHistory(BaseSchema):
    """
    文件版本历史
    
    文件的完整版本历史记录
    """
    
    file_id: str = Field(..., description="主文件ID")
    original_name: str = Field(..., description="原始文件名")
    current_version: int = Field(..., description="当前版本号")
    total_versions: int = Field(..., description="总版本数")
    versions: List[FileVersionInfo] = Field(..., description="版本列表")

class FilePreviewInfo(BaseSchema):
    """
    文件预览信息
    
    用于文件预览的配置和状态
    """
    
    file_id: str = Field(..., description="文件ID")
    can_preview: bool = Field(..., description="是否可预览")
    preview_type: Optional[str] = Field(None, description="预览类型")
    preview_url: Optional[str] = Field(None, description="预览链接")
    thumbnail_url: Optional[str] = Field(None, description="缩略图链接")
    content_preview: Optional[str] = Field(None, description="内容预览")

class FileBatchOperation(BaseSchema):
    """
    文件批量操作
    
    用于批量处理文件的数据结构
    """
    
    file_ids: List[str] = Field(..., min_items=1, description="文件ID列表")
    operation: str = Field(..., pattern="^(delete|move|copy|archive)$", description="操作类型")
    target_order_id: Optional[str] = Field(None, description="目标订单ID（移动/复制时）")
    target_category: Optional[str] = Field(None, description="目标分类（移动时）")

class FileSearchParams(BaseSchema):
    """
    文件搜索参数
    
    用于高级文件搜索的参数配置
    """
    
    keyword: Optional[str] = Field(None, description="搜索关键词")
    file_types: Optional[List[str]] = Field(None, description="文件类型列表")
    categories: Optional[List[str]] = Field(None, description="分类列表")
    size_range: Optional[Dict[str, int]] = Field(None, description="文件大小范围")
    date_range: Optional[Dict[str, datetime]] = Field(None, description="日期范围")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    is_deliverable: Optional[bool] = Field(None, description="是否为交付物")
    order_ids: Optional[List[str]] = Field(None, description="订单ID列表")

class FileStorageInfo(BaseSchema):
    """
    文件存储信息
    
    用于文件存储状态和配置的数据结构
    """
    
    total_storage_used: int = Field(..., description="已使用存储空间(字节)")
    total_storage_limit: int = Field(..., description="存储空间限制(字节)")
    storage_usage_percentage: float = Field(..., description="存储使用率(%)")
    files_count: int = Field(..., description="文件总数")
    
    # 存储分布
    storage_by_category: Dict[str, int] = Field(..., description="按分类的存储分布")
    storage_by_type: Dict[str, int] = Field(..., description="按类型的存储分布")
    
    # 清理建议
    cleanup_suggestions: List[str] = Field(..., description="清理建议")
    large_files: List[Dict[str, Any]] = Field(..., description="大文件列表")
    old_files: List[Dict[str, Any]] = Field(..., description="旧文件列表")

# 导入导出相关
class FileExportParams(BaseSchema):
    """
    文件导出参数
    
    用于文件数据导出的参数配置
    """
    
    format: str = Field(default="json", pattern="^(json|csv|excel)$", description="导出格式")
    include_metadata: bool = Field(default=True, description="是否包含元数据")
    include_versions: bool = Field(default=False, description="是否包含版本信息")
    filter_params: Optional[AttachmentListParams] = Field(None, description="筛选参数")

class FileImportData(BaseSchema):
    """
    文件导入数据
    
    用于批量导入文件元数据
    """
    
    files: List[AttachmentCreate] = Field(..., min_items=1, description="文件数据列表")
    skip_duplicates: bool = Field(default=True, description="是否跳过重复数据")
    update_existing: bool = Field(default=False, description="是否更新已存在的文件")
