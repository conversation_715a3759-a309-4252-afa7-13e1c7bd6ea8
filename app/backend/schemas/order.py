"""
订单相关数据模型

定义订单管理相关的请求和响应数据结构
包含订单创建、更新、查询等操作的数据验证
"""

from decimal import Decimal
from typing import Optional, List, Dict, Any
from datetime import date, datetime
from pydantic import Field, validator

from app.backend.schemas.base import (
    BaseSchema, IDSchema, TimestampSchema, 
    OrderStatusEnum, BillingMethodEnum, ListParams
)

class OrderBase(BaseSchema):
    """
    订单基础信息
    
    包含订单的基本字段，用于创建和更新操作
    """
    
    title: str = Field(..., min_length=1, max_length=200, description="订单标题")
    description: Optional[str] = Field(None, description="项目描述")
    requirements: Optional[str] = Field(None, description="客户要求")
    customer_id: str = Field(..., description="客户ID")
    category_id: str = Field(..., description="项目分类ID")
    priority: int = Field(default=3, ge=1, le=5, description="优先级(1-5，5最高)")
    
    # 计费信息
    billing_method: BillingMethodEnum = Field(default=BillingMethodEnum.FIXED, description="计费方式")
    unit_price: Optional[Decimal] = Field(None, ge=0, description="单价")
    quantity: Optional[Decimal] = Field(None, ge=0, description="数量（小时数/字数/页数等）")
    total_amount: Decimal = Field(default=Decimal('0.00'), ge=0, description="总金额")
    
    # 财务信息
    deposit_ratio: Optional[Decimal] = Field(None, ge=0, le=1, description="定金比例")
    invoice_required: bool = Field(default=False, description="是否需要发票")
    
    # 时间信息
    start_date: Optional[date] = Field(None, description="开始日期")
    deadline: Optional[datetime] = Field(None, description="截止日期")
    
    # 项目信息
    tech_stack: Optional[str] = Field(None, max_length=500, description="技术栈/专业领域")
    deliverables: Optional[str] = Field(None, description="交付物清单")
    estimated_hours: Optional[Decimal] = Field(None, ge=0, description="预估工时")
    
    # 其他信息
    notes: Optional[str] = Field(None, description="备注信息")
    tags: Optional[str] = Field(None, max_length=500, description="标签（逗号分隔）")
    
    @validator('title')
    def validate_title(cls, v):
        """验证标题不能为空"""
        if not v or not v.strip():
            raise ValueError('订单标题不能为空')
        return v.strip()
    
    @validator('deadline')
    def validate_deadline(cls, v, values):
        """验证截止日期不能早于开始日期"""
        if v and 'start_date' in values and values['start_date']:
            start_datetime = datetime.combine(values['start_date'], datetime.min.time())
            if v < start_datetime:
                raise ValueError('截止日期不能早于开始日期')
        return v
    
    @validator('total_amount')
    def validate_total_amount(cls, v, values):
        """验证总金额的合理性"""
        if v < 0:
            raise ValueError('总金额不能为负数')
        
        # 如果是按量计费，检查单价和数量
        billing_method = values.get('billing_method')
        if billing_method in [BillingMethodEnum.HOURLY, BillingMethodEnum.WORD_COUNT, BillingMethodEnum.PAGE_COUNT]:
            unit_price = values.get('unit_price')
            quantity = values.get('quantity')
            if unit_price and quantity and v != unit_price * quantity:
                # 自动计算总金额
                return unit_price * quantity
        
        return v

class OrderCreate(OrderBase):
    """
    创建订单请求
    
    用于创建新订单的数据结构
    """
    
    status: OrderStatusEnum = Field(default=OrderStatusEnum.PENDING, description="订单状态")
    
    @validator('customer_id', 'category_id')
    def validate_ids(cls, v):
        """验证ID格式"""
        if not v or not v.strip():
            raise ValueError('ID不能为空')
        return v.strip()

class OrderUpdate(BaseSchema):
    """
    更新订单请求
    
    用于更新订单信息的数据结构，所有字段都是可选的
    """
    
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="订单标题")
    description: Optional[str] = Field(None, description="项目描述")
    requirements: Optional[str] = Field(None, description="客户要求")
    customer_id: Optional[str] = Field(None, description="客户ID")
    category_id: Optional[str] = Field(None, description="项目分类ID")
    status: Optional[OrderStatusEnum] = Field(None, description="订单状态")
    priority: Optional[int] = Field(None, ge=1, le=5, description="优先级")
    
    # 计费信息
    billing_method: Optional[BillingMethodEnum] = Field(None, description="计费方式")
    unit_price: Optional[Decimal] = Field(None, ge=0, description="单价")
    quantity: Optional[Decimal] = Field(None, ge=0, description="数量")
    total_amount: Optional[Decimal] = Field(None, ge=0, description="总金额")
    
    # 财务信息
    deposit_ratio: Optional[Decimal] = Field(None, ge=0, le=1, description="定金比例")
    actual_amount: Optional[Decimal] = Field(None, ge=0, description="实际收款金额")
    invoice_required: Optional[bool] = Field(None, description="是否需要发票")
    
    # 时间信息
    start_date: Optional[date] = Field(None, description="开始日期")
    deadline: Optional[datetime] = Field(None, description="截止日期")
    
    # 项目信息
    tech_stack: Optional[str] = Field(None, max_length=500, description="技术栈/专业领域")
    deliverables: Optional[str] = Field(None, description="交付物清单")
    estimated_hours: Optional[Decimal] = Field(None, ge=0, description="预估工时")
    actual_hours: Optional[Decimal] = Field(None, ge=0, description="实际工时")
    
    # 其他信息
    notes: Optional[str] = Field(None, description="备注信息")
    tags: Optional[str] = Field(None, max_length=500, description="标签")

class OrderResponse(OrderBase, IDSchema, TimestampSchema):
    """
    订单响应数据
    
    返回订单信息时使用的数据结构
    """
    
    status: OrderStatusEnum = Field(..., description="订单状态")
    
    # 计算字段
    deposit_amount: Optional[Decimal] = Field(None, description="定金金额")
    final_amount: Optional[Decimal] = Field(None, description="尾款金额")
    actual_amount: Optional[Decimal] = Field(None, description="实际收款金额")
    actual_hours: Optional[Decimal] = Field(None, description="实际工时")
    
    # 时间字段
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    settled_at: Optional[datetime] = Field(None, description="结算时间")
    
    # 关联信息
    customer_name: Optional[str] = Field(None, description="客户姓名")
    category_name: Optional[str] = Field(None, description="分类名称")
    
    # 计算属性
    display_title: Optional[str] = Field(None, description="显示标题")
    is_overdue: Optional[bool] = Field(None, description="是否已逾期")
    days_until_deadline: Optional[int] = Field(None, description="距离截止日期的天数")
    progress_percentage: Optional[float] = Field(None, description="项目进度百分比")
    hourly_rate: Optional[float] = Field(None, description="实际时薪")

class OrderListParams(ListParams):
    """
    订单列表查询参数
    
    扩展基础列表参数，添加订单特定的筛选条件
    """
    
    customer_id: Optional[str] = Field(None, description="客户ID筛选")
    category_id: Optional[str] = Field(None, description="分类ID筛选")
    status: Optional[OrderStatusEnum] = Field(None, description="状态筛选")
    priority: Optional[int] = Field(None, ge=1, le=5, description="优先级筛选")
    billing_method: Optional[BillingMethodEnum] = Field(None, description="计费方式筛选")
    
    # 金额范围筛选
    min_amount: Optional[Decimal] = Field(None, ge=0, description="最小金额")
    max_amount: Optional[Decimal] = Field(None, ge=0, description="最大金额")
    
    # 时间范围筛选
    start_date_from: Optional[date] = Field(None, description="开始日期范围-起始")
    start_date_to: Optional[date] = Field(None, description="开始日期范围-结束")
    deadline_from: Optional[datetime] = Field(None, description="截止日期范围-起始")
    deadline_to: Optional[datetime] = Field(None, description="截止日期范围-结束")
    
    # 状态筛选
    is_overdue: Optional[bool] = Field(None, description="是否逾期筛选")
    is_active: Optional[bool] = Field(None, description="是否活跃订单筛选")
    has_invoice: Optional[bool] = Field(None, description="是否需要发票筛选")

class OrderSummary(BaseSchema):
    """
    订单摘要信息
    
    用于下拉选择等场景的简化订单信息
    """
    
    id: str = Field(..., description="订单ID")
    title: str = Field(..., description="订单标题")
    display_title: str = Field(..., description="显示标题")
    status: OrderStatusEnum = Field(..., description="订单状态")
    customer_name: str = Field(..., description="客户姓名")
    total_amount: Decimal = Field(..., description="总金额")
    deadline: Optional[datetime] = Field(None, description="截止日期")
    is_overdue: bool = Field(..., description="是否已逾期")

class OrderStatistics(BaseSchema):
    """
    订单统计信息
    
    用于订单分析和报表的统计数据
    """
    
    total_orders: int = Field(..., description="订单总数")
    active_orders: int = Field(..., description="活跃订单数")
    completed_orders: int = Field(..., description="已完成订单数")
    overdue_orders: int = Field(..., description="逾期订单数")
    
    # 金额统计
    total_amount: Decimal = Field(..., description="总金额")
    completed_amount: Decimal = Field(..., description="已完成金额")
    pending_amount: Decimal = Field(..., description="待完成金额")
    
    # 状态分布
    status_distribution: Dict[str, int] = Field(..., description="状态分布")
    
    # 计费方式分布
    billing_method_distribution: Dict[str, int] = Field(..., description="计费方式分布")
    
    # 分类分布
    category_distribution: Dict[str, int] = Field(..., description="分类分布")
    
    # 时间统计
    average_completion_days: Optional[float] = Field(None, description="平均完成天数")
    average_hourly_rate: Optional[float] = Field(None, description="平均时薪")

class OrderStatusUpdate(BaseSchema):
    """
    订单状态更新
    
    用于更新订单状态的专用数据结构
    """
    
    status: OrderStatusEnum = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")
    actual_amount: Optional[Decimal] = Field(None, ge=0, description="实际收款金额（结算时）")

class OrderAmountCalculation(BaseSchema):
    """
    订单金额计算
    
    用于金额计算的数据结构
    """
    
    billing_method: BillingMethodEnum = Field(..., description="计费方式")
    unit_price: Optional[Decimal] = Field(None, ge=0, description="单价")
    quantity: Optional[Decimal] = Field(None, ge=0, description="数量")
    total_amount: Optional[Decimal] = Field(None, ge=0, description="总金额")
    deposit_ratio: Optional[Decimal] = Field(None, ge=0, le=1, description="定金比例")
    
    # 计算结果
    calculated_total: Optional[Decimal] = Field(None, description="计算后的总金额")
    deposit_amount: Optional[Decimal] = Field(None, description="定金金额")
    final_amount: Optional[Decimal] = Field(None, description="尾款金额")

# 批量操作相关
class OrderBatchUpdate(BaseSchema):
    """
    订单批量更新
    
    用于批量更新订单信息
    """
    
    order_ids: List[str] = Field(..., min_items=1, description="订单ID列表")
    update_data: OrderUpdate = Field(..., description="更新数据")

class OrderBatchStatusUpdate(BaseSchema):
    """
    订单批量状态更新
    
    用于批量更新订单状态
    """
    
    order_ids: List[str] = Field(..., min_items=1, description="订单ID列表")
    status: OrderStatusEnum = Field(..., description="新状态")
    notes: Optional[str] = Field(None, description="状态变更备注")

class OrderBatchDelete(BaseSchema):
    """
    订单批量删除
    
    用于批量删除订单
    """
    
    order_ids: List[str] = Field(..., min_items=1, description="订单ID列表")
    force_delete: bool = Field(default=False, description="是否强制删除（即使有关联数据）")

# 导入导出相关
class OrderExportParams(BaseSchema):
    """
    订单导出参数
    
    用于订单数据导出的参数配置
    """
    
    format: str = Field(default="excel", pattern="^(excel|csv|json)$", description="导出格式")
    include_customer_info: bool = Field(default=True, description="是否包含客户信息")
    include_work_logs: bool = Field(default=False, description="是否包含工时记录")
    include_attachments: bool = Field(default=False, description="是否包含附件信息")
    filter_params: Optional[OrderListParams] = Field(None, description="筛选参数")

class OrderImportData(BaseSchema):
    """
    订单导入数据
    
    用于批量导入订单数据
    """
    
    orders: List[OrderCreate] = Field(..., min_items=1, description="订单数据列表")
    skip_duplicates: bool = Field(default=True, description="是否跳过重复数据")
    update_existing: bool = Field(default=False, description="是否更新已存在的订单")
