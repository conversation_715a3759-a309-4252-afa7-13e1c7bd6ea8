"""
FastAPI应用主文件

创建和配置FastAPI应用实例
集成中间件、异常处理、路由等组件
"""

import logging
import traceback
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.core.config import settings, create_directories
from app.core.exceptions import BaseAPIException
from app.core.database import create_tables, test_connection
from app.models import *  # 确保所有模型被导入

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    
    在应用启动和关闭时执行必要的初始化和清理工作
    """
    # 启动时执行
    logger.info("🚀 启动兼职接单管理系统后端服务...")
    
    try:
        # 创建必要目录
        create_directories()
        logger.info("✅ 目录创建完成")
        
        # 测试数据库连接
        if test_connection():
            logger.info("✅ 数据库连接成功")
        else:
            logger.error("❌ 数据库连接失败")
            raise Exception("数据库连接失败")
        
        # 创建数据库表（如果不存在）
        create_tables()
        logger.info("✅ 数据库表检查完成")
        
        logger.info("🎉 后端服务启动完成")
        
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("🔄 正在关闭后端服务...")
    logger.info("👋 后端服务已关闭")

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    description=settings.app_description,
    version=settings.app_version,
    docs_url=settings.docs_url,
    redoc_url=settings.redoc_url,
    openapi_url=settings.openapi_url,
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """
    记录HTTP请求日志
    """
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"📥 {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应信息
        logger.info(f"📤 {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
        
        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"💥 {request.method} {request.url} - ERROR - {process_time:.3f}s - {str(e)}")
        raise

# 全局异常处理器
@app.exception_handler(BaseAPIException)
async def api_exception_handler(request: Request, exc: BaseAPIException):
    """
    处理自定义API异常
    """
    logger.warning(f"API异常: {exc.error_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "data": None,
            "message": exc.detail,
            "error_code": exc.error_code,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    处理请求验证异常
    """
    logger.warning(f"请求验证失败: {exc.errors()}")
    
    # 格式化验证错误信息
    error_details = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        error_details.append(f"{field}: {message}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "success": False,
            "data": None,
            "message": "请求参数验证失败",
            "error_code": "VALIDATION_ERROR",
            "details": error_details,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """
    处理HTTP异常
    """
    logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "data": None,
            "message": exc.detail,
            "error_code": "HTTP_ERROR",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """
    处理未捕获的异常
    """
    logger.error(f"未处理异常: {type(exc).__name__} - {str(exc)}")
    logger.error(f"异常堆栈: {traceback.format_exc()}")
    
    # 在开发环境返回详细错误信息
    if settings.debug:
        detail = f"{type(exc).__name__}: {str(exc)}"
    else:
        detail = "服务器内部错误"
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "data": None,
            "message": detail,
            "error_code": "INTERNAL_SERVER_ERROR",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# 健康检查端点
@app.get("/health", tags=["系统"])
async def health_check():
    """
    健康检查接口
    
    返回服务状态和基本信息
    """
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "app_name": settings.app_name,
            "version": settings.app_version,
            "timestamp": datetime.utcnow().isoformat(),
            "database_connected": test_connection()
        },
        "message": "服务运行正常"
    }

# 根路径
@app.get("/", tags=["系统"])
async def root():
    """
    根路径接口
    
    返回API基本信息
    """
    return {
        "success": True,
        "data": {
            "app_name": settings.app_name,
            "version": settings.app_version,
            "description": settings.app_description,
            "docs_url": settings.docs_url,
            "api_prefix": settings.api_prefix
        },
        "message": "欢迎使用兼职接单管理系统API"
    }

# 导入路由模块
from app.backend.api.endpoints import customers, orders

# 注册路由
app.include_router(customers.router, prefix=settings.api_prefix, tags=["客户管理"])
app.include_router(orders.router, prefix=settings.api_prefix, tags=["订单管理"])

# 添加必要的导入
import time
from datetime import datetime

if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"启动开发服务器: {settings.host}:{settings.port}")
    
    uvicorn.run(
        "app.backend.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
