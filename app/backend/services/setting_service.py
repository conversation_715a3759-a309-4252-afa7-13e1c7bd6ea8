"""
系统配置管理业务逻辑服务

提供系统配置相关的业务逻辑处理
包含配置管理、用户偏好、系统维护等功能
"""

import json
import os
import psutil
from typing import List, Optional, Dict, Any, Tuple, Union
from datetime import datetime, timedelta
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc

from app.models.setting import Setting
from app.models.customer import Customer
from app.models.order import Order
from app.models.work_log import WorkLog
from app.models.attachment import Attachment
from app.backend.schemas.setting import (
    SettingCreate, SettingUpdate, SettingListParams,
    SettingGroup, SystemInfo, ConfigBackup, UserPreferences,
    SettingValidation, SystemMaintenance
)
from app.core.config import Settings, get_settings
from app.core.exceptions import (
    NotFoundException, ValidationException, BusinessException
)

class SettingService:
    """
    系统配置管理服务类
    
    提供系统配置相关的所有业务逻辑操作
    """
    
    def __init__(self, db: Session, settings: Settings = None):
        self.db = db
        self.settings = settings or get_settings()
        self.backup_dir = Path("data/backups")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    def create_setting(self, setting_data: SettingCreate) -> Setting:
        """
        创建系统配置
        
        Args:
            setting_data: 配置创建数据
            
        Returns:
            Setting: 创建的配置对象
            
        Raises:
            ValidationException: 配置键已存在
        """
        # 检查配置键是否已存在
        existing = self.db.query(Setting).filter(
            Setting.config_key == setting_data.config_key
        ).first()
        
        if existing:
            raise ValidationException(f"配置键 {setting_data.config_key} 已存在")
        
        # 创建配置对象
        setting = Setting(**setting_data.model_dump())
        
        self.db.add(setting)
        self.db.commit()
        self.db.refresh(setting)
        
        return setting
    
    def get_setting(self, setting_id: str) -> Setting:
        """
        根据ID获取配置
        
        Args:
            setting_id: 配置ID
            
        Returns:
            Setting: 配置对象
            
        Raises:
            NotFoundException: 配置不存在
        """
        setting = self.db.query(Setting).filter(Setting.id == setting_id).first()
        if not setting:
            raise NotFoundException(f"配置 {setting_id} 不存在")
        return setting
    
    def get_setting_by_key(self, config_key: str) -> Optional[Setting]:
        """
        根据配置键获取配置
        
        Args:
            config_key: 配置键
            
        Returns:
            Optional[Setting]: 配置对象或None
        """
        return self.db.query(Setting).filter(Setting.config_key == config_key).first()
    
    def update_setting(self, setting_id: str, setting_data: SettingUpdate) -> Setting:
        """
        更新系统配置
        
        Args:
            setting_id: 配置ID
            setting_data: 更新数据
            
        Returns:
            Setting: 更新后的配置对象
            
        Raises:
            NotFoundException: 配置不存在
            ValidationException: 只读配置不能修改
        """
        setting = self.get_setting(setting_id)
        
        # 检查是否为只读配置
        if setting.is_readonly:
            raise ValidationException("只读配置不能修改")
        
        # 更新字段
        update_data = setting_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(setting, field):
                setattr(setting, field, value)
        
        self.db.commit()
        self.db.refresh(setting)
        
        return setting
    
    def delete_setting(self, setting_id: str) -> bool:
        """
        删除系统配置
        
        Args:
            setting_id: 配置ID
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            NotFoundException: 配置不存在
            ValidationException: 系统配置或必需配置不能删除
        """
        setting = self.get_setting(setting_id)
        
        # 检查是否为系统配置或必需配置
        if setting.is_system:
            raise ValidationException("系统配置不能删除")
        
        if setting.is_required:
            raise ValidationException("必需配置不能删除")
        
        self.db.delete(setting)
        self.db.commit()
        
        return True
    
    def list_settings(self, params: SettingListParams) -> Tuple[List[Setting], int]:
        """
        获取配置列表
        
        Args:
            params: 查询参数
            
        Returns:
            Tuple[List[Setting], int]: 配置列表和总数
        """
        query = self.db.query(Setting)
        
        # 应用筛选条件
        if params.keyword:
            keyword_filter = or_(
                Setting.config_key.contains(params.keyword),
                Setting.description.contains(params.keyword)
            )
            query = query.filter(keyword_filter)
        
        if params.category:
            query = query.filter(Setting.category == params.category)
        
        if params.value_type:
            query = query.filter(Setting.value_type == params.value_type)
        
        if params.is_system is not None:
            query = query.filter(Setting.is_system == params.is_system)
        
        if params.is_readonly is not None:
            query = query.filter(Setting.is_readonly == params.is_readonly)
        
        if params.is_required is not None:
            query = query.filter(Setting.is_required == params.is_required)
        
        if params.has_value is not None:
            if params.has_value:
                query = query.filter(Setting.config_value.isnot(None))
            else:
                query = query.filter(Setting.config_value.is_(None))
        
        if params.is_default is not None:
            if params.is_default:
                query = query.filter(Setting.config_value == Setting.default_value)
            else:
                query = query.filter(Setting.config_value != Setting.default_value)
        
        # 获取总数
        total = query.count()
        
        # 应用排序
        if params.sort_by:
            sort_column = getattr(Setting, params.sort_by, None)
            if sort_column:
                if params.sort_order == "asc":
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
        else:
            # 默认按分类和配置键排序
            query = query.order_by(Setting.category, Setting.config_key)
        
        # 应用分页
        settings = query.offset(params.offset).limit(params.limit).all()
        
        return settings, total
    
    def get_settings_by_category(self, category: str) -> List[Setting]:
        """
        根据分类获取配置列表
        
        Args:
            category: 配置分类
            
        Returns:
            List[Setting]: 配置列表
        """
        return self.db.query(Setting).filter(
            Setting.category == category
        ).order_by(Setting.config_key).all()
    
    def get_settings_grouped(self) -> List[SettingGroup]:
        """
        获取分组的配置列表
        
        Returns:
            List[SettingGroup]: 分组配置列表
        """
        # 获取所有分类
        categories = self.db.query(Setting.category).distinct().all()
        
        groups = []
        category_info = {
            'general': {'display_name': '常规设置', 'description': '基本的系统配置'},
            'application': {'display_name': '应用设置', 'description': '应用程序相关配置'},
            'interface': {'display_name': '界面设置', 'description': '用户界面相关配置'},
            'business': {'display_name': '业务设置', 'description': '业务规则和默认值配置'},
            'notification': {'display_name': '通知设置', 'description': '通知和提醒相关配置'},
            'data': {'display_name': '数据设置', 'description': '数据管理和备份配置'},
            'security': {'display_name': '安全设置', 'description': '安全和权限相关配置'},
            'performance': {'display_name': '性能设置', 'description': '性能优化相关配置'}
        }
        
        for (category,) in categories:
            settings = self.get_settings_by_category(category)
            info = category_info.get(category, {
                'display_name': category.title(),
                'description': f'{category} 相关配置'
            })
            
            group = SettingGroup(
                category=category,
                display_name=info['display_name'],
                description=info['description'],
                settings=settings
            )
            groups.append(group)
        
        return groups
    
    def set_config_value(self, config_key: str, value: Any, 
                        description: str = None, category: str = "general",
                        value_type: str = "string") -> Setting:
        """
        设置配置值
        
        Args:
            config_key: 配置键
            value: 配置值
            description: 配置描述
            category: 配置分类
            value_type: 值类型
            
        Returns:
            Setting: 配置对象
        """
        setting = self.get_setting_by_key(config_key)
        
        if setting:
            # 检查是否为只读配置
            if setting.is_readonly:
                raise ValidationException(f"配置 {config_key} 为只读配置，不能修改")
            
            setting.config_value = str(value)
        else:
            # 创建新配置
            setting_data = SettingCreate(
                config_key=config_key,
                description=description,
                category=category,
                value_type=value_type
            )
            setting = self.create_setting(setting_data)
            setting.config_value = str(value)
        
        self.db.commit()
        self.db.refresh(setting)
        
        return setting
    
    def get_config_value(self, config_key: str, default_value: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            config_key: 配置键
            default_value: 默认值
            
        Returns:
            Any: 配置值
        """
        setting = self.get_setting_by_key(config_key)

        if setting:
            # 根据值类型解析配置值
            if setting.value_type == "boolean":
                return setting.config_value.lower() in ["true", "1"] if setting.config_value else False
            elif setting.value_type == "integer":
                return int(setting.config_value) if setting.config_value else 0
            elif setting.value_type == "float":
                return float(setting.config_value) if setting.config_value else 0.0
            elif setting.value_type == "json":
                import json
                return json.loads(setting.config_value) if setting.config_value else {}
            else:
                return setting.config_value

        return default_value
    
    def validate_setting_value(self, config_key: str, value: str) -> SettingValidation:
        """
        验证配置值
        
        Args:
            config_key: 配置键
            value: 配置值
            
        Returns:
            SettingValidation: 验证结果
        """
        setting = self.get_setting_by_key(config_key)
        
        validation = SettingValidation(
            config_key=config_key,
            config_value=value,
            is_valid=True,
            validation_errors=[],
            suggestions=[]
        )
        
        if not setting:
            validation.is_valid = False
            validation.validation_errors.append("配置项不存在")
            return validation
        
        try:
            # 根据值类型验证
            if setting.value_type == "integer":
                int(value)
            elif setting.value_type == "float":
                float(value)
            elif setting.value_type == "boolean":
                if value.lower() not in ["true", "false", "1", "0"]:
                    raise ValueError("布尔值必须是 true/false 或 1/0")
            elif setting.value_type == "json":
                json.loads(value)
            
            # 特定配置的验证规则
            if config_key == "business.default_deposit_ratio":
                ratio = float(value)
                if not 0 <= ratio <= 1:
                    validation.is_valid = False
                    validation.validation_errors.append("定金比例必须在0-1之间")
            elif config_key == "business.default_hourly_rate":
                rate = float(value)
                if rate < 0:
                    validation.is_valid = False
                    validation.validation_errors.append("时薪不能为负数")
                elif rate > 1000:
                    validation.suggestions.append("时薪较高，请确认是否正确")
            
        except ValueError as e:
            validation.is_valid = False
            validation.validation_errors.append(f"值格式错误: {str(e)}")

        return validation

    def get_system_info(self) -> SystemInfo:
        """
        获取系统信息

        Returns:
            SystemInfo: 系统信息
        """
        # 应用信息
        app_name = self.get_config_value("app.name", "兼职接单管理系统")
        app_version = self.get_config_value("app.version", "1.0.0")
        app_description = self.get_config_value("app.description", "专为程序员兼职接单设计的全流程管理工具")

        # 数据库信息
        database_path = self.settings.database_url.replace("sqlite:///", "")
        database_size = "0 B"
        if os.path.exists(database_path):
            size_bytes = os.path.getsize(database_path)
            database_size = self._format_file_size(size_bytes)

        # 统计总记录数
        total_records = (
            self.db.query(Customer).count() +
            self.db.query(Order).count() +
            self.db.query(WorkLog).count() +
            self.db.query(Attachment).count() +
            self.db.query(Setting).count()
        )

        # 存储信息
        upload_dir = Path(self.settings.upload_dir)
        storage_used = 0
        if upload_dir.exists():
            for file_path in upload_dir.rglob("*"):
                if file_path.is_file():
                    storage_used += file_path.stat().st_size

        # 系统性能信息
        try:
            memory_info = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            disk_usage = psutil.disk_usage('/')

            memory_usage = f"{memory_info.percent:.1f}%"
            storage_available = self._format_file_size(disk_usage.free)
            storage_usage_percentage = (storage_used / disk_usage.total * 100) if disk_usage.total > 0 else 0
        except:
            memory_usage = "N/A"
            cpu_percent = 0.0
            storage_available = "N/A"
            storage_usage_percentage = 0.0

        return SystemInfo(
            app_name=app_name,
            app_version=app_version,
            app_description=app_description,
            system_status="running",
            uptime="N/A",  # 可以通过进程启动时间计算
            last_restart=None,
            database_type="SQLite",
            database_size=database_size,
            total_records=total_records,
            storage_used=self._format_file_size(storage_used),
            storage_available=storage_available,
            storage_usage_percentage=storage_usage_percentage,
            memory_usage=memory_usage,
            cpu_usage=cpu_percent,
            response_time=0.0  # 可以通过监控获取
        )

    def create_config_backup(self, backup_name: str = None,
                           description: str = None) -> ConfigBackup:
        """
        创建配置备份

        Args:
            backup_name: 备份名称
            description: 备份描述

        Returns:
            ConfigBackup: 备份信息
        """
        if not backup_name:
            backup_name = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 获取所有配置
        settings = self.db.query(Setting).all()

        # 构建备份数据
        backup_data = {
            "backup_info": {
                "name": backup_name,
                "description": description,
                "created_at": datetime.now().isoformat(),
                "version": "1.0"
            },
            "settings": []
        }

        categories = set()
        for setting in settings:
            categories.add(setting.category)
            backup_data["settings"].append({
                "config_key": setting.config_key,
                "config_value": setting.config_value,
                "default_value": setting.default_value,
                "description": setting.description,
                "category": setting.category,
                "value_type": setting.value_type,
                "is_system": setting.is_system,
                "is_readonly": setting.is_readonly,
                "is_required": setting.is_required
            })

        # 保存备份文件
        backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_file = self.backup_dir / f"{backup_id}.json"

        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)

        # 计算备份大小
        backup_size = self._format_file_size(backup_file.stat().st_size)

        return ConfigBackup(
            backup_id=backup_id,
            backup_name=backup_name,
            backup_time=datetime.now(),
            backup_size=backup_size,
            description=description,
            settings_count=len(settings),
            categories=list(categories),
            is_auto=False,
            is_valid=True
        )

    def list_config_backups(self) -> List[ConfigBackup]:
        """
        获取配置备份列表

        Returns:
            List[ConfigBackup]: 备份列表
        """
        backups = []

        for backup_file in self.backup_dir.glob("backup_*.json"):
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)

                backup_info = backup_data.get("backup_info", {})
                settings_data = backup_data.get("settings", [])

                categories = set()
                for setting in settings_data:
                    categories.add(setting.get("category", "general"))

                backup = ConfigBackup(
                    backup_id=backup_file.stem,
                    backup_name=backup_info.get("name", backup_file.stem),
                    backup_time=datetime.fromisoformat(backup_info.get("created_at", datetime.now().isoformat())),
                    backup_size=self._format_file_size(backup_file.stat().st_size),
                    description=backup_info.get("description"),
                    settings_count=len(settings_data),
                    categories=list(categories),
                    is_auto=False,
                    is_valid=True
                )
                backups.append(backup)
            except Exception:
                # 备份文件损坏，跳过
                continue

        # 按时间倒序排列
        backups.sort(key=lambda x: x.backup_time, reverse=True)

        return backups

    def restore_config_backup(self, backup_id: str,
                             restore_categories: List[str] = None,
                             overwrite_existing: bool = False) -> bool:
        """
        恢复配置备份

        Args:
            backup_id: 备份ID
            restore_categories: 要恢复的分类
            overwrite_existing: 是否覆盖现有配置

        Returns:
            bool: 是否恢复成功

        Raises:
            NotFoundException: 备份文件不存在
            BusinessException: 恢复失败
        """
        backup_file = self.backup_dir / f"{backup_id}.json"

        if not backup_file.exists():
            raise NotFoundException(f"备份文件 {backup_id} 不存在")

        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            settings_data = backup_data.get("settings", [])

            for setting_data in settings_data:
                # 检查分类筛选
                if restore_categories and setting_data["category"] not in restore_categories:
                    continue

                config_key = setting_data["config_key"]
                existing = self.get_setting_by_key(config_key)

                if existing:
                    if overwrite_existing and not existing.is_readonly:
                        # 更新现有配置
                        for field, value in setting_data.items():
                            if field != "config_key" and hasattr(existing, field):
                                setattr(existing, field, value)
                else:
                    # 创建新配置
                    new_setting = Setting(**setting_data)
                    self.db.add(new_setting)

            self.db.commit()
            return True

        except Exception as e:
            self.db.rollback()
            raise BusinessException(f"恢复配置失败: {str(e)}")

    def reset_to_defaults(self, category: str = None) -> bool:
        """
        重置配置为默认值

        Args:
            category: 要重置的分类（可选）

        Returns:
            bool: 是否重置成功
        """
        query = self.db.query(Setting).filter(Setting.is_readonly == False)

        if category:
            query = query.filter(Setting.category == category)

        settings = query.all()

        for setting in settings:
            if setting.default_value is not None:
                setting.config_value = setting.default_value

        self.db.commit()
        return True

    def initialize_default_settings(self) -> bool:
        """
        初始化默认配置

        Returns:
            bool: 是否初始化成功
        """
        try:
            Setting.initialize_default_settings(self.db)
            return True
        except Exception:
            return False

    def _format_file_size(self, size_bytes: int) -> str:
        """
        格式化文件大小显示

        Args:
            size_bytes: 文件大小（字节）

        Returns:
            str: 格式化后的文件大小
        """
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        return f"{s} {size_names[i]}"
