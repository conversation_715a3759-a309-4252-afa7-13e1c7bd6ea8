"""
订单管理业务逻辑服务

提供订单相关的业务逻辑处理
包含订单CRUD操作、状态管理、统计分析等功能
"""

from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from datetime import date, datetime
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc

from app.models.order import Order
from app.models.customer import Customer
from app.models.category import Category
from app.models.work_log import WorkLog
from app.models.base import OrderStatus, BillingMethod
from app.backend.schemas.order import (
    OrderCreate, OrderUpdate, OrderListParams, OrderStatusUpdate,
    OrderStatistics, OrderAmountCalculation
)
from app.core.exceptions import (
    OrderNotFoundException, CustomerNotFoundException, CategoryNotFoundException,
    InvalidOrderStatusException, BusinessException, ValidationException
)

class OrderService:
    """
    订单管理服务类
    
    提供订单相关的所有业务逻辑操作
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_order(self, order_data: OrderCreate) -> Order:
        """
        创建新订单
        
        Args:
            order_data: 订单创建数据
            
        Returns:
            Order: 创建的订单对象
            
        Raises:
            CustomerNotFoundException: 客户不存在
            CategoryNotFoundException: 分类不存在
            ValidationException: 数据验证失败
        """
        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == order_data.customer_id).first()
        if not customer:
            raise CustomerNotFoundException(order_data.customer_id)
        
        # 验证分类是否存在
        category = self.db.query(Category).filter(Category.id == order_data.category_id).first()
        if not category:
            raise CategoryNotFoundException(order_data.category_id)
        
        # 创建订单对象
        order = Order(**order_data.model_dump(exclude_unset=True))
        
        # 计算金额
        order.calculate_amounts()
        
        # 验证业务规则
        self._validate_order_business_rules(order)
        
        self.db.add(order)
        self.db.commit()
        self.db.refresh(order)
        
        # 更新客户和分类统计
        self._update_customer_statistics(customer.id)
        self._update_category_statistics(category.id)
        
        return order
    
    def get_order(self, order_id: str) -> Order:
        """
        根据ID获取订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            Order: 订单对象
            
        Raises:
            OrderNotFoundException: 订单不存在
        """
        order = self.db.query(Order).options(
            joinedload(Order.customer),
            joinedload(Order.category)
        ).filter(Order.id == order_id).first()
        
        if not order:
            raise OrderNotFoundException(order_id)
        return order
    
    def update_order(self, order_id: str, order_data: OrderUpdate) -> Order:
        """
        更新订单信息
        
        Args:
            order_id: 订单ID
            order_data: 更新数据
            
        Returns:
            Order: 更新后的订单对象
            
        Raises:
            OrderNotFoundException: 订单不存在
            CustomerNotFoundException: 客户不存在
            CategoryNotFoundException: 分类不存在
        """
        order = self.get_order(order_id)
        
        # 验证关联数据
        if order_data.customer_id and order_data.customer_id != order.customer_id:
            customer = self.db.query(Customer).filter(Customer.id == order_data.customer_id).first()
            if not customer:
                raise CustomerNotFoundException(order_data.customer_id)
        
        if order_data.category_id and order_data.category_id != order.category_id:
            category = self.db.query(Category).filter(Category.id == order_data.category_id).first()
            if not category:
                raise CategoryNotFoundException(order_data.category_id)
        
        # 保存旧的客户和分类ID用于统计更新
        old_customer_id = order.customer_id
        old_category_id = order.category_id
        
        # 更新字段
        update_data = order_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(order, field):
                setattr(order, field, value)
        
        # 重新计算金额
        order.calculate_amounts()
        
        # 验证业务规则
        self._validate_order_business_rules(order)
        
        self.db.commit()
        self.db.refresh(order)
        
        # 更新统计信息
        self._update_customer_statistics(old_customer_id)
        self._update_category_statistics(old_category_id)
        if order.customer_id != old_customer_id:
            self._update_customer_statistics(order.customer_id)
        if order.category_id != old_category_id:
            self._update_category_statistics(order.category_id)
        
        return order
    
    def delete_order(self, order_id: str, force: bool = False) -> bool:
        """
        删除订单
        
        Args:
            order_id: 订单ID
            force: 是否强制删除（即使有关联数据）
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            OrderNotFoundException: 订单不存在
            BusinessException: 有关联数据且非强制删除
        """
        order = self.get_order(order_id)
        
        # 检查是否有关联数据
        work_log_count = self.db.query(WorkLog).filter(WorkLog.order_id == order_id).count()
        if work_log_count > 0 and not force:
            raise BusinessException(f"订单有 {work_log_count} 个关联工时记录，无法删除。如需强制删除，请设置 force=True")
        
        # 保存统计更新所需的信息
        customer_id = order.customer_id
        category_id = order.category_id
        
        # 如果强制删除，先删除关联数据
        if force:
            self.db.query(WorkLog).filter(WorkLog.order_id == order_id).delete()
        
        self.db.delete(order)
        self.db.commit()
        
        # 更新统计信息
        self._update_customer_statistics(customer_id)
        self._update_category_statistics(category_id)
        
        return True
    
    def list_orders(self, params: OrderListParams) -> Tuple[List[Order], int]:
        """
        获取订单列表
        
        Args:
            params: 查询参数
            
        Returns:
            Tuple[List[Order], int]: 订单列表和总数
        """
        query = self.db.query(Order).options(
            joinedload(Order.customer),
            joinedload(Order.category)
        )
        
        # 应用筛选条件
        if params.keyword:
            keyword_filter = or_(
                Order.title.contains(params.keyword),
                Order.description.contains(params.keyword),
                Order.tech_stack.contains(params.keyword),
                Order.tags.contains(params.keyword)
            )
            query = query.filter(keyword_filter)
        
        if params.customer_id:
            query = query.filter(Order.customer_id == params.customer_id)
        
        if params.category_id:
            query = query.filter(Order.category_id == params.category_id)
        
        if params.status:
            query = query.filter(Order.status == params.status)
        
        if params.priority:
            query = query.filter(Order.priority == params.priority)
        
        if params.billing_method:
            query = query.filter(Order.billing_method == params.billing_method)
        
        if params.min_amount is not None:
            query = query.filter(Order.total_amount >= params.min_amount)
        
        if params.max_amount is not None:
            query = query.filter(Order.total_amount <= params.max_amount)
        
        # 时间范围筛选
        if params.start_date_from:
            query = query.filter(Order.start_date >= params.start_date_from)
        
        if params.start_date_to:
            query = query.filter(Order.start_date <= params.start_date_to)
        
        if params.deadline_from:
            query = query.filter(Order.deadline >= params.deadline_from)
        
        if params.deadline_to:
            query = query.filter(Order.deadline <= params.deadline_to)
        
        # 状态筛选
        if params.is_overdue is not None:
            current_time = datetime.now()
            if params.is_overdue:
                query = query.filter(
                    and_(
                        Order.deadline < current_time,
                        Order.status.in_(OrderStatus.get_active_statuses())
                    )
                )
            else:
                query = query.filter(
                    or_(
                        Order.deadline >= current_time,
                        Order.deadline.is_(None),
                        Order.status.notin_(OrderStatus.get_active_statuses())
                    )
                )
        
        if params.is_active is not None:
            if params.is_active:
                query = query.filter(Order.status.in_(OrderStatus.get_active_statuses()))
            else:
                query = query.filter(Order.status.notin_(OrderStatus.get_active_statuses()))
        
        if params.has_invoice is not None:
            query = query.filter(Order.invoice_required == params.has_invoice)
        
        # 获取总数
        total = query.count()
        
        # 应用排序
        if params.sort_by:
            sort_column = getattr(Order, params.sort_by, None)
            if sort_column:
                if params.sort_order == "asc":
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
        else:
            # 默认按创建时间倒序
            query = query.order_by(desc(Order.created_at))
        
        # 应用分页
        orders = query.offset(params.offset).limit(params.limit).all()
        
        return orders, total
    
    def update_order_status(self, order_id: str, status_data: OrderStatusUpdate) -> Order:
        """
        更新订单状态
        
        Args:
            order_id: 订单ID
            status_data: 状态更新数据
            
        Returns:
            Order: 更新后的订单对象
            
        Raises:
            OrderNotFoundException: 订单不存在
            InvalidOrderStatusException: 无效的状态转换
        """
        order = self.get_order(order_id)
        
        # 验证状态转换的合法性
        if not self._is_valid_status_transition(order.status, status_data.status):
            raise InvalidOrderStatusException(order.status, status_data.status)
        
        # 更新状态
        success = order.update_status(status_data.status, status_data.notes)
        if not success:
            raise ValidationException("状态更新失败")
        
        # 如果是结算状态，更新实际收款金额
        if status_data.status == OrderStatus.SETTLED and status_data.actual_amount is not None:
            order.actual_amount = status_data.actual_amount
        
        self.db.commit()
        self.db.refresh(order)
        
        # 更新统计信息
        self._update_customer_statistics(order.customer_id)
        self._update_category_statistics(order.category_id)
        
        return order
    
    def calculate_order_amount(self, calculation_data: OrderAmountCalculation) -> OrderAmountCalculation:
        """
        计算订单金额
        
        Args:
            calculation_data: 计算数据
            
        Returns:
            OrderAmountCalculation: 包含计算结果的数据
        """
        result = calculation_data.model_copy()
        
        # 根据计费方式计算总金额
        if calculation_data.billing_method == BillingMethod.FIXED:
            result.calculated_total = calculation_data.total_amount or Decimal('0.00')
        elif calculation_data.unit_price and calculation_data.quantity:
            result.calculated_total = calculation_data.unit_price * calculation_data.quantity
        else:
            result.calculated_total = Decimal('0.00')
        
        # 计算定金和尾款
        if result.calculated_total and calculation_data.deposit_ratio:
            result.deposit_amount = result.calculated_total * calculation_data.deposit_ratio
            result.final_amount = result.calculated_total - result.deposit_amount
        else:
            result.deposit_amount = Decimal('0.00')
            result.final_amount = result.calculated_total
        
        return result
    
    def get_order_statistics(self) -> OrderStatistics:
        """
        获取订单统计信息
        
        Returns:
            OrderStatistics: 订单统计数据
        """
        # 基础统计
        total_orders = self.db.query(Order).count()
        active_orders = self.db.query(Order).filter(
            Order.status.in_(OrderStatus.get_active_statuses())
        ).count()
        completed_orders = self.db.query(Order).filter(
            Order.status == OrderStatus.COMPLETED
        ).count()
        
        # 逾期订单
        current_time = datetime.now()
        overdue_orders = self.db.query(Order).filter(
            and_(
                Order.deadline < current_time,
                Order.status.in_(OrderStatus.get_active_statuses())
            )
        ).count()
        
        # 金额统计
        total_amount = self.db.query(func.sum(Order.total_amount)).scalar() or Decimal('0.00')
        completed_amount = self.db.query(func.sum(Order.actual_amount)).filter(
            Order.status.in_([OrderStatus.COMPLETED, OrderStatus.SETTLED])
        ).scalar() or Decimal('0.00')
        pending_amount = total_amount - completed_amount
        
        # 状态分布
        status_distribution = {}
        status_stats = self.db.query(
            Order.status, func.count(Order.id)
        ).group_by(Order.status).all()
        for status, count in status_stats:
            status_distribution[status] = count
        
        # 计费方式分布
        billing_method_distribution = {}
        billing_stats = self.db.query(
            Order.billing_method, func.count(Order.id)
        ).group_by(Order.billing_method).all()
        for method, count in billing_stats:
            billing_method_distribution[method] = count
        
        # 分类分布
        category_distribution = {}
        category_stats = self.db.query(
            Category.name, func.count(Order.id)
        ).join(Order).group_by(Category.name).all()
        for category_name, count in category_stats:
            category_distribution[category_name] = count
        
        # 时间统计
        completed_orders_with_dates = self.db.query(Order).filter(
            and_(
                Order.status.in_([OrderStatus.COMPLETED, OrderStatus.SETTLED]),
                Order.start_date.isnot(None),
                Order.completed_at.isnot(None)
            )
        ).all()
        
        average_completion_days = None
        if completed_orders_with_dates:
            total_days = sum(
                (order.completed_at.date() - order.start_date).days
                for order in completed_orders_with_dates
            )
            average_completion_days = total_days / len(completed_orders_with_dates)
        
        # 平均时薪
        orders_with_hours = self.db.query(Order).filter(
            and_(
                Order.actual_hours > 0,
                Order.actual_amount > 0
            )
        ).all()
        
        average_hourly_rate = None
        if orders_with_hours:
            total_rate = sum(
                float(order.actual_amount) / float(order.actual_hours)
                for order in orders_with_hours
            )
            average_hourly_rate = total_rate / len(orders_with_hours)
        
        return OrderStatistics(
            total_orders=total_orders,
            active_orders=active_orders,
            completed_orders=completed_orders,
            overdue_orders=overdue_orders,
            total_amount=total_amount,
            completed_amount=completed_amount,
            pending_amount=pending_amount,
            status_distribution=status_distribution,
            billing_method_distribution=billing_method_distribution,
            category_distribution=category_distribution,
            average_completion_days=average_completion_days,
            average_hourly_rate=average_hourly_rate
        )
    
    def _validate_order_business_rules(self, order: Order) -> None:
        """
        验证订单业务规则
        
        Args:
            order: 订单对象
            
        Raises:
            ValidationException: 业务规则验证失败
        """
        # 验证截止日期
        if order.deadline and order.start_date:
            start_datetime = datetime.combine(order.start_date, datetime.min.time())
            if order.deadline < start_datetime:
                raise ValidationException("截止日期不能早于开始日期")
        
        # 验证计费信息
        if order.billing_method in [BillingMethod.HOURLY, BillingMethod.WORD_COUNT, BillingMethod.PAGE_COUNT]:
            if not order.unit_price or not order.quantity:
                raise ValidationException(f"计费方式为{order.billing_method}时，必须设置单价和数量")
        
        # 验证定金比例
        if order.deposit_ratio and (order.deposit_ratio < 0 or order.deposit_ratio > 1):
            raise ValidationException("定金比例必须在0-1之间")
    
    def _is_valid_status_transition(self, current_status: str, new_status: str) -> bool:
        """
        验证状态转换是否合法
        
        Args:
            current_status: 当前状态
            new_status: 新状态
            
        Returns:
            bool: 是否合法
        """
        # 定义允许的状态转换
        valid_transitions = {
            OrderStatus.PENDING: [OrderStatus.IN_PROGRESS, OrderStatus.CANCELLED, OrderStatus.PAUSED],
            OrderStatus.IN_PROGRESS: [OrderStatus.REVISING, OrderStatus.REVIEWING, OrderStatus.COMPLETED, OrderStatus.PAUSED, OrderStatus.CANCELLED],
            OrderStatus.REVISING: [OrderStatus.IN_PROGRESS, OrderStatus.REVIEWING, OrderStatus.CANCELLED],
            OrderStatus.REVIEWING: [OrderStatus.REVISING, OrderStatus.COMPLETED, OrderStatus.CANCELLED],
            OrderStatus.COMPLETED: [OrderStatus.SETTLED, OrderStatus.REVISING],
            OrderStatus.PAUSED: [OrderStatus.IN_PROGRESS, OrderStatus.CANCELLED],
            OrderStatus.CANCELLED: [],  # 已取消的订单不能转换到其他状态
            OrderStatus.SETTLED: []     # 已结算的订单不能转换到其他状态
        }
        
        return new_status in valid_transitions.get(current_status, [])
    
    def _update_customer_statistics(self, customer_id: str) -> None:
        """
        更新客户统计信息
        
        Args:
            customer_id: 客户ID
        """
        try:
            customer = self.db.query(Customer).filter(Customer.id == customer_id).first()
            if customer:
                customer.update_statistics()
                self.db.commit()
        except Exception:
            # 忽略统计更新错误，不影响主业务流程
            self.db.rollback()
    
    def _update_category_statistics(self, category_id: str) -> None:
        """
        更新分类统计信息
        
        Args:
            category_id: 分类ID
        """
        try:
            category = self.db.query(Category).filter(Category.id == category_id).first()
            if category:
                category.update_statistics()
                self.db.commit()
        except Exception:
            # 忽略统计更新错误，不影响主业务流程
            self.db.rollback()

    def get_active_orders(self) -> List[Order]:
        """
        获取活跃订单列表

        Returns:
            List[Order]: 活跃订单列表
        """
        return self.db.query(Order).options(
            joinedload(Order.customer),
            joinedload(Order.category)
        ).filter(
            Order.status.in_(OrderStatus.get_active_statuses())
        ).order_by(Order.deadline.asc()).all()

    def get_overdue_orders(self) -> List[Order]:
        """
        获取逾期订单列表

        Returns:
            List[Order]: 逾期订单列表
        """
        current_time = datetime.now()
        return self.db.query(Order).options(
            joinedload(Order.customer),
            joinedload(Order.category)
        ).filter(
            and_(
                Order.deadline < current_time,
                Order.status.in_(OrderStatus.get_active_statuses())
            )
        ).order_by(Order.deadline.asc()).all()

    def search_orders(self, keyword: str, limit: int = 10) -> List[Order]:
        """
        搜索订单

        Args:
            keyword: 搜索关键词
            limit: 返回数量限制

        Returns:
            List[Order]: 匹配的订单列表
        """
        if not keyword:
            return []

        query = self.db.query(Order).options(
            joinedload(Order.customer),
            joinedload(Order.category)
        ).filter(
            or_(
                Order.title.contains(keyword),
                Order.description.contains(keyword),
                Order.tech_stack.contains(keyword),
                Order.tags.contains(keyword)
            )
        ).order_by(desc(Order.created_at))

        return query.limit(limit).all()

    def batch_update_status(self, order_ids: List[str], status: str, notes: str = None) -> List[Order]:
        """
        批量更新订单状态

        Args:
            order_ids: 订单ID列表
            status: 新状态
            notes: 状态变更备注

        Returns:
            List[Order]: 更新后的订单列表
        """
        orders = []
        status_data = OrderStatusUpdate(status=status, notes=notes)

        for order_id in order_ids:
            try:
                order = self.update_order_status(order_id, status_data)
                orders.append(order)
            except (OrderNotFoundException, InvalidOrderStatusException):
                # 跳过不存在或状态转换无效的订单
                continue

        return orders
