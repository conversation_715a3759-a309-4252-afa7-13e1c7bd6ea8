"""
统计分析业务逻辑服务

提供综合统计分析相关的业务逻辑处理
包含业务概览、趋势分析、绩效评估等功能
"""

from typing import List, Optional, Dict, Any
from decimal import Decimal
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc

from app.models.customer import Customer
from app.models.order import Order
from app.models.work_log import WorkLog
from app.models.category import Category
from app.models.base import OrderStatus, CustomerSource
from app.backend.schemas.analytics import (
    BusinessOverview, RevenueAnalysis, CustomerAnalysis,
    ProjectAnalysis, WorkEfficiencyAnalysis, TrendAnalysis,
    PerformanceMetrics, AnalyticsQuery, AnalyticsReport
)
from app.backend.services.customer_service import CustomerService
from app.backend.services.order_service import OrderService
from app.backend.services.work_log_service import WorkLogService

class AnalyticsService:
    """
    统计分析服务类
    
    提供综合统计分析相关的所有业务逻辑操作
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.customer_service = CustomerService(db)
        self.order_service = OrderService(db)
        self.work_log_service = WorkLogService(db)
    
    def get_business_overview(self, start_date: date = None, end_date: date = None) -> BusinessOverview:
        """
        获取业务概览
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            BusinessOverview: 业务概览数据
        """
        # 设置默认时间范围（当前月）
        if not start_date:
            start_date = datetime.now().replace(day=1).date()
        if not end_date:
            end_date = datetime.now().date()
        
        # 获取基础统计
        customer_stats = self.customer_service.get_customer_statistics()
        order_stats = self.order_service.get_order_statistics()
        work_stats = self.work_log_service.get_work_log_statistics(start_date, end_date)
        
        # 计算月度数据
        month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_orders = self.db.query(Order).filter(Order.created_at >= month_start).all()
        monthly_revenue = sum(order.actual_amount or Decimal('0.00') for order in monthly_orders 
                             if order.status in [OrderStatus.COMPLETED, OrderStatus.SETTLED])
        
        # 计算增长率
        prev_month_start = (month_start - timedelta(days=1)).replace(day=1)
        prev_month_end = month_start - timedelta(days=1)
        
        prev_customers = self.db.query(Customer).filter(
            and_(Customer.created_at >= prev_month_start, Customer.created_at <= prev_month_end)
        ).count()
        current_customers = self.db.query(Customer).filter(Customer.created_at >= month_start).count()
        
        customer_growth_rate = ((current_customers - prev_customers) / prev_customers * 100) if prev_customers > 0 else 0
        
        # 计算平均订单价值
        completed_orders = [o for o in monthly_orders if o.status in [OrderStatus.COMPLETED, OrderStatus.SETTLED]]
        average_order_value = (monthly_revenue / len(completed_orders)) if completed_orders else Decimal('0.00')
        
        # 计算订单完成率
        total_monthly_orders = len(monthly_orders)
        completed_monthly_orders = len(completed_orders)
        order_completion_rate = (completed_monthly_orders / total_monthly_orders * 100) if total_monthly_orders > 0 else 0
        
        # 预警指标
        low_satisfaction_customers = self.db.query(Customer).filter(
            and_(Customer.satisfaction_rating.isnot(None), Customer.satisfaction_rating < 3)
        ).count()
        
        inactive_customers = self.db.query(Customer).filter(Customer.is_active == False).count()
        
        return BusinessOverview(
            total_customers=customer_stats.total_customers,
            active_customers=customer_stats.active_customers,
            total_orders=order_stats.total_orders,
            active_orders=order_stats.active_orders,
            completed_orders=order_stats.completed_orders,
            total_revenue=order_stats.completed_amount,
            monthly_revenue=monthly_revenue,
            pending_revenue=order_stats.pending_amount,
            average_order_value=average_order_value,
            total_work_hours=work_stats.total_hours,
            monthly_work_hours=work_stats.total_hours,  # 简化，实际应该是月度数据
            average_hourly_rate=work_stats.average_hourly_rate,
            efficiency_score=work_stats.average_efficiency_score,
            customer_growth_rate=customer_growth_rate,
            revenue_growth_rate=0.0,  # 需要更复杂的计算
            order_completion_rate=order_completion_rate,
            overdue_orders=order_stats.overdue_orders,
            low_satisfaction_customers=low_satisfaction_customers,
            inactive_customers=inactive_customers
        )
    
    def get_revenue_analysis(self, start_date: date = None, end_date: date = None) -> RevenueAnalysis:
        """
        获取收入分析
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            RevenueAnalysis: 收入分析数据
        """
        # 设置默认时间范围
        if not start_date:
            start_date = datetime.now().replace(day=1).date()
        if not end_date:
            end_date = datetime.now().date()
        
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())
        
        # 获取时间范围内的订单
        orders = self.db.query(Order).filter(
            and_(Order.created_at >= start_datetime, Order.created_at <= end_datetime)
        ).all()
        
        # 基础收入统计
        total_revenue = sum(order.total_amount for order in orders)
        completed_revenue = sum(order.actual_amount or Decimal('0.00') for order in orders 
                               if order.status in [OrderStatus.COMPLETED, OrderStatus.SETTLED])
        pending_revenue = total_revenue - completed_revenue
        
        # 本月收入
        month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_orders = [o for o in orders if o.created_at >= month_start]
        monthly_revenue = sum(order.actual_amount or Decimal('0.00') for order in monthly_orders 
                             if order.status in [OrderStatus.COMPLETED, OrderStatus.SETTLED])
        
        # 按客户分组的收入
        revenue_by_customer = []
        customer_revenue = {}
        for order in orders:
            if order.customer_id not in customer_revenue:
                customer_revenue[order.customer_id] = {
                    'customer_id': order.customer_id,
                    'customer_name': order.customer.name if order.customer else '未知客户',
                    'revenue': Decimal('0.00'),
                    'orders_count': 0
                }
            customer_revenue[order.customer_id]['revenue'] += order.actual_amount or Decimal('0.00')
            customer_revenue[order.customer_id]['orders_count'] += 1
        
        revenue_by_customer = sorted(customer_revenue.values(), 
                                   key=lambda x: x['revenue'], reverse=True)[:10]
        
        # 按分类分组的收入
        revenue_by_category = []
        category_revenue = {}
        for order in orders:
            category_name = order.category.name if order.category else '未分类'
            if category_name not in category_revenue:
                category_revenue[category_name] = {
                    'category_name': category_name,
                    'revenue': Decimal('0.00'),
                    'orders_count': 0
                }
            category_revenue[category_name]['revenue'] += order.actual_amount or Decimal('0.00')
            category_revenue[category_name]['orders_count'] += 1
        
        revenue_by_category = sorted(category_revenue.values(), 
                                   key=lambda x: x['revenue'], reverse=True)
        
        # 按计费方式分组的收入
        revenue_by_billing_method = []
        billing_revenue = {}
        for order in orders:
            method = order.billing_method
            if method not in billing_revenue:
                billing_revenue[method] = {
                    'billing_method': method,
                    'revenue': Decimal('0.00'),
                    'orders_count': 0
                }
            billing_revenue[method]['revenue'] += order.actual_amount or Decimal('0.00')
            billing_revenue[method]['orders_count'] += 1
        
        revenue_by_billing_method = list(billing_revenue.values())
        
        # 每日收入趋势
        daily_revenue_trend = []
        daily_revenue = {}
        for order in orders:
            if order.status in [OrderStatus.COMPLETED, OrderStatus.SETTLED] and order.completed_at:
                date_str = order.completed_at.date().strftime('%Y-%m-%d')
                if date_str not in daily_revenue:
                    daily_revenue[date_str] = Decimal('0.00')
                daily_revenue[date_str] += order.actual_amount or Decimal('0.00')
        
        for date_str in sorted(daily_revenue.keys()):
            daily_revenue_trend.append({
                'date': date_str,
                'revenue': float(daily_revenue[date_str])
            })
        
        # 简化的月度趋势和预测
        monthly_revenue_trend = [{'month': datetime.now().strftime('%Y-%m'), 'revenue': float(monthly_revenue)}]
        revenue_forecast = [{'period': 'next_month', 'predicted_revenue': float(monthly_revenue * 1.1)}]
        
        # 收入质量指标
        repeat_customers = len([c for c in customer_revenue.values() if c['orders_count'] > 1])
        recurring_revenue_ratio = (repeat_customers / len(customer_revenue)) * 100 if customer_revenue else 0
        
        high_value_customers = len([c for c in customer_revenue.values() if c['revenue'] > 5000])
        high_value_customer_ratio = (high_value_customers / len(customer_revenue)) * 100 if customer_revenue else 0
        
        return RevenueAnalysis(
            total_revenue=total_revenue,
            completed_revenue=completed_revenue,
            pending_revenue=pending_revenue,
            monthly_revenue=monthly_revenue,
            revenue_by_customer=revenue_by_customer,
            revenue_by_category=revenue_by_category,
            revenue_by_billing_method=revenue_by_billing_method,
            daily_revenue_trend=daily_revenue_trend,
            monthly_revenue_trend=monthly_revenue_trend,
            revenue_forecast=revenue_forecast,
            growth_rate=0.0,  # 需要更复杂的计算
            recurring_revenue_ratio=recurring_revenue_ratio,
            high_value_customer_ratio=high_value_customer_ratio
        )
    
    def get_customer_analysis(self, start_date: date = None, end_date: date = None) -> CustomerAnalysis:
        """
        获取客户分析
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            CustomerAnalysis: 客户分析数据
        """
        # 获取基础客户统计
        customer_stats = self.customer_service.get_customer_statistics()
        
        # 设置时间范围
        if not start_date:
            start_date = datetime.now().replace(day=1).date()
        if not end_date:
            end_date = datetime.now().date()
        
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())
        
        # 新客户统计
        new_customers = self.db.query(Customer).filter(
            and_(Customer.created_at >= start_datetime, Customer.created_at <= end_datetime)
        ).count()
        
        # 客户生命周期价值分析
        customers_with_orders = self.db.query(Customer).join(Order).all()
        customer_lifetime_value = []
        
        for customer in customers_with_orders[:10]:  # 取前10个客户
            orders = self.db.query(Order).filter(Order.customer_id == customer.id).all()
            total_value = sum(order.actual_amount or Decimal('0.00') for order in orders)
            avg_order_value = total_value / len(orders) if orders else Decimal('0.00')
            
            customer_lifetime_value.append({
                'customer_id': customer.id,
                'customer_name': customer.name,
                'total_value': float(total_value),
                'orders_count': len(orders),
                'avg_order_value': float(avg_order_value),
                'first_order_date': min(order.created_at for order in orders).strftime('%Y-%m-%d') if orders else None
            })
        
        # 按价值排序
        customer_lifetime_value.sort(key=lambda x: x['total_value'], reverse=True)
        top_customers = customer_lifetime_value[:5]
        
        # 客户细分（简化版）
        customer_segments = [
            {'segment': 'high_value', 'count': len([c for c in customer_lifetime_value if c['total_value'] > 10000])},
            {'segment': 'medium_value', 'count': len([c for c in customer_lifetime_value if 1000 <= c['total_value'] <= 10000])},
            {'segment': 'low_value', 'count': len([c for c in customer_lifetime_value if c['total_value'] < 1000])}
        ]
        
        # 客户获取趋势（简化版）
        customer_acquisition_trend = [
            {'period': start_date.strftime('%Y-%m'), 'new_customers': new_customers}
        ]
        
        # 留存率和流失率（简化计算）
        total_customers = customer_stats.total_customers
        active_customers = customer_stats.active_customers
        customer_retention_rate = (active_customers / total_customers * 100) if total_customers > 0 else 0
        churn_rate = 100 - customer_retention_rate
        
        # 满意度分析
        satisfaction_stats = self.db.query(
            Customer.satisfaction_rating, func.count(Customer.id)
        ).filter(Customer.satisfaction_rating.isnot(None)).group_by(Customer.satisfaction_rating).all()
        
        satisfaction_distribution = {}
        total_rated = 0
        total_score = 0
        
        for rating, count in satisfaction_stats:
            satisfaction_distribution[str(rating)] = count
            total_rated += count
            total_score += rating * count
        
        average_satisfaction = total_score / total_rated if total_rated > 0 else 0
        
        # 满意度趋势（简化版）
        satisfaction_trend = [
            {'period': datetime.now().strftime('%Y-%m'), 'average_satisfaction': average_satisfaction}
        ]
        
        # 获客渠道分析
        acquisition_channels = customer_stats.source_distribution
        
        # 渠道效果分析（简化版）
        channel_effectiveness = []
        for source, count in acquisition_channels.items():
            # 计算该渠道客户的平均价值
            source_customers = self.db.query(Customer).filter(Customer.source == source).all()
            total_value = sum(
                sum(order.actual_amount or Decimal('0.00') for order in 
                    self.db.query(Order).filter(Order.customer_id == customer.id).all())
                for customer in source_customers
            )
            avg_value = float(total_value / count) if count > 0 else 0
            
            channel_effectiveness.append({
                'channel': source,
                'customers_count': count,
                'avg_customer_value': avg_value,
                'total_value': float(total_value)
            })
        
        return CustomerAnalysis(
            total_customers=customer_stats.total_customers,
            new_customers=new_customers,
            active_customers=customer_stats.active_customers,
            vip_customers=customer_stats.vip_customers,
            customer_lifetime_value=customer_lifetime_value,
            top_customers=top_customers,
            customer_segments=customer_segments,
            customer_acquisition_trend=customer_acquisition_trend,
            customer_retention_rate=customer_retention_rate,
            churn_rate=churn_rate,
            average_satisfaction=average_satisfaction,
            satisfaction_distribution=satisfaction_distribution,
            satisfaction_trend=satisfaction_trend,
            acquisition_channels=acquisition_channels,
            channel_effectiveness=channel_effectiveness
        )

    def get_project_analysis(self, start_date: date = None, end_date: date = None) -> ProjectAnalysis:
        """
        获取项目分析

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            ProjectAnalysis: 项目分析数据
        """
        # 设置时间范围
        if not start_date:
            start_date = datetime.now().replace(day=1).date()
        if not end_date:
            end_date = datetime.now().date()

        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取时间范围内的订单
        orders = self.db.query(Order).filter(
            and_(Order.created_at >= start_datetime, Order.created_at <= end_datetime)
        ).all()

        # 项目统计
        total_projects = len(orders)
        completed_projects = len([o for o in orders if o.status == OrderStatus.COMPLETED])
        in_progress_projects = len([o for o in orders if o.status in OrderStatus.get_active_statuses()])

        # 逾期项目
        current_time = datetime.now()
        overdue_projects = len([o for o in orders if o.deadline and o.deadline < current_time
                               and o.status in OrderStatus.get_active_statuses()])

        # 执行效率分析
        completed_orders_with_dates = [o for o in orders if o.status == OrderStatus.COMPLETED
                                     and o.start_date and o.completed_at]

        if completed_orders_with_dates:
            completion_times = [(o.completed_at.date() - o.start_date).days for o in completed_orders_with_dates]
            average_completion_time = sum(completion_times) / len(completion_times)

            # 按时交付率
            on_time_orders = [o for o in completed_orders_with_dates
                            if not o.deadline or o.completed_at <= o.deadline]
            on_time_delivery_rate = (len(on_time_orders) / len(completed_orders_with_dates)) * 100
        else:
            average_completion_time = 0
            on_time_delivery_rate = 0

        # 项目成功率（简化为完成率）
        project_success_rate = (completed_projects / total_projects * 100) if total_projects > 0 else 0

        # 按分类分组的项目
        projects_by_category = []
        category_stats = {}
        for order in orders:
            category_name = order.category.name if order.category else '未分类'
            if category_name not in category_stats:
                category_stats[category_name] = {'category': category_name, 'count': 0, 'revenue': Decimal('0.00')}
            category_stats[category_name]['count'] += 1
            category_stats[category_name]['revenue'] += order.actual_amount or Decimal('0.00')

        projects_by_category = list(category_stats.values())

        # 按优先级分组的项目
        projects_by_priority = []
        priority_stats = {}
        for order in orders:
            priority = order.priority
            if priority not in priority_stats:
                priority_stats[priority] = {'priority': priority, 'count': 0}
            priority_stats[priority]['count'] += 1

        projects_by_priority = list(priority_stats.values())

        # 按计费方式分组的项目
        projects_by_billing_method = []
        billing_stats = {}
        for order in orders:
            method = order.billing_method
            if method not in billing_stats:
                billing_stats[method] = {'billing_method': method, 'count': 0, 'avg_amount': Decimal('0.00')}
            billing_stats[method]['count'] += 1

        # 计算平均金额
        for method, stats in billing_stats.items():
            method_orders = [o for o in orders if o.billing_method == method]
            total_amount = sum(o.actual_amount or Decimal('0.00') for o in method_orders)
            stats['avg_amount'] = float(total_amount / len(method_orders)) if method_orders else 0

        projects_by_billing_method = list(billing_stats.values())

        # 预估vs实际工时对比
        estimated_vs_actual_hours = []
        for order in orders:
            if order.estimated_hours and order.actual_hours:
                estimated_vs_actual_hours.append({
                    'order_id': order.id,
                    'order_title': order.title,
                    'estimated_hours': float(order.estimated_hours),
                    'actual_hours': float(order.actual_hours),
                    'variance': float(order.actual_hours - order.estimated_hours),
                    'variance_percentage': float((order.actual_hours - order.estimated_hours) / order.estimated_hours * 100)
                })

        # 按项目类型的效率分析
        efficiency_by_project_type = []
        for category_name, stats in category_stats.items():
            category_orders = [o for o in orders if (o.category.name if o.category else '未分类') == category_name]
            if category_orders:
                avg_completion_time = 0
                if completed_orders_with_dates:
                    category_completed = [o for o in category_orders if o in completed_orders_with_dates]
                    if category_completed:
                        completion_times = [(o.completed_at.date() - o.start_date).days for o in category_completed]
                        avg_completion_time = sum(completion_times) / len(completion_times)

                efficiency_by_project_type.append({
                    'project_type': category_name,
                    'avg_completion_time': avg_completion_time,
                    'total_projects': len(category_orders),
                    'completed_projects': len([o for o in category_orders if o.status == OrderStatus.COMPLETED])
                })

        # 最盈利项目
        profitable_orders = [(o, float(o.actual_amount or Decimal('0.00'))) for o in orders
                           if o.actual_amount and o.actual_amount > 0]
        profitable_orders.sort(key=lambda x: x[1], reverse=True)

        most_profitable_projects = []
        for order, profit in profitable_orders[:10]:
            most_profitable_projects.append({
                'order_id': order.id,
                'order_title': order.title,
                'customer_name': order.customer.name if order.customer else '未知客户',
                'profit': profit,
                'category': order.category.name if order.category else '未分类'
            })

        # 按分类的利润率
        profit_margin_by_category = []
        for category_name, stats in category_stats.items():
            category_orders = [o for o in orders if (o.category.name if o.category else '未分类') == category_name]
            total_revenue = sum(o.actual_amount or Decimal('0.00') for o in category_orders)
            # 简化的利润率计算（假设成本为收入的70%）
            estimated_cost = total_revenue * Decimal('0.7')
            profit = total_revenue - estimated_cost
            profit_margin = float(profit / total_revenue * 100) if total_revenue > 0 else 0

            profit_margin_by_category.append({
                'category': category_name,
                'revenue': float(total_revenue),
                'estimated_profit': float(profit),
                'profit_margin': profit_margin
            })

        return ProjectAnalysis(
            total_projects=total_projects,
            completed_projects=completed_projects,
            in_progress_projects=in_progress_projects,
            overdue_projects=overdue_projects,
            average_completion_time=average_completion_time,
            on_time_delivery_rate=on_time_delivery_rate,
            project_success_rate=project_success_rate,
            projects_by_category=projects_by_category,
            projects_by_priority=projects_by_priority,
            projects_by_billing_method=projects_by_billing_method,
            estimated_vs_actual_hours=estimated_vs_actual_hours,
            efficiency_by_project_type=efficiency_by_project_type,
            most_profitable_projects=most_profitable_projects,
            profit_margin_by_category=profit_margin_by_category
        )

    def get_work_efficiency_analysis(self, start_date: date = None, end_date: date = None) -> WorkEfficiencyAnalysis:
        """
        获取工作效率分析

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            WorkEfficiencyAnalysis: 工作效率分析数据
        """
        # 获取工时统计
        work_stats = self.work_log_service.get_work_log_statistics(start_date, end_date)

        # 可计费工时比例
        billable_ratio = (work_stats.billable_hours / work_stats.total_hours * 100) if work_stats.total_hours > 0 else 0

        # 生产力趋势（简化版）
        productivity_trend = [
            {'period': datetime.now().strftime('%Y-%m'), 'productivity_score': work_stats.average_efficiency_score}
        ]

        # 高效工作时段分析
        hourly_dist = work_stats.hourly_distribution
        peak_hours = sorted(hourly_dist.items(), key=lambda x: x[1], reverse=True)[:3]
        peak_productivity_hours = [int(hour) for hour, _ in peak_hours]

        # 工作模式分析
        work_pattern_analysis = {
            'most_productive_hours': peak_productivity_hours,
            'average_session_length': work_stats.total_duration / work_stats.total_logs if work_stats.total_logs > 0 else 0,
            'work_consistency': 85.0  # 简化的一致性评分
        }

        # 最佳工作时长建议
        optimal_work_duration = 4.0  # 基于效率评分算法的最佳时长
        break_frequency_recommendation = 2.0  # 建议每2小时休息一次

        # 按工作类型的效率
        efficiency_by_work_type = []
        for work_type, duration in work_stats.work_type_distribution.items():
            efficiency_by_work_type.append({
                'work_type': work_type,
                'total_duration': duration,
                'total_hours': duration / 60.0,
                'efficiency_score': 75.0  # 简化的效率评分
            })

        # 时间分配
        total_duration = sum(work_stats.work_type_distribution.values())
        time_allocation = {}
        for work_type, duration in work_stats.work_type_distribution.items():
            time_allocation[work_type] = (duration / total_duration * 100) if total_duration > 0 else 0

        # 改进建议
        efficiency_recommendations = [
            "在高效时段安排重要任务",
            "保持工作会话在2-4小时之间",
            "增加可计费工时比例",
            "定期休息以保持专注度"
        ]

        focus_areas = [
            "时间管理",
            "任务优先级",
            "工作环境优化"
        ]

        return WorkEfficiencyAnalysis(
            total_work_hours=work_stats.total_hours,
            billable_hours=work_stats.billable_hours,
            billable_ratio=billable_ratio,
            average_efficiency_score=work_stats.average_efficiency_score,
            productivity_trend=productivity_trend,
            peak_productivity_hours=peak_productivity_hours,
            work_pattern_analysis=work_pattern_analysis,
            optimal_work_duration=optimal_work_duration,
            break_frequency_recommendation=break_frequency_recommendation,
            efficiency_by_work_type=efficiency_by_work_type,
            time_allocation=time_allocation,
            efficiency_recommendations=efficiency_recommendations,
            focus_areas=focus_areas
        )

    def get_performance_metrics(self, start_date: date = None, end_date: date = None) -> PerformanceMetrics:
        """
        获取绩效指标

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            PerformanceMetrics: 绩效指标数据
        """
        # 获取各模块统计数据
        business_overview = self.get_business_overview(start_date, end_date)
        revenue_analysis = self.get_revenue_analysis(start_date, end_date)
        customer_analysis = self.get_customer_analysis(start_date, end_date)
        project_analysis = self.get_project_analysis(start_date, end_date)
        efficiency_analysis = self.get_work_efficiency_analysis(start_date, end_date)

        # 财务KPI
        revenue_growth = business_overview.revenue_growth_rate
        # 简化的利润率计算
        profit_margin = 30.0  # 假设30%的利润率
        average_project_value = business_overview.average_order_value

        # 客户KPI
        customer_satisfaction = customer_analysis.average_satisfaction
        customer_retention_rate = customer_analysis.customer_retention_rate
        # 简化的净推荐值
        net_promoter_score = (customer_satisfaction - 3) * 25  # 将1-5分转换为NPS

        # 运营KPI
        project_delivery_rate = project_analysis.on_time_delivery_rate
        resource_utilization = efficiency_analysis.billable_ratio
        # 简化的质量得分
        quality_score = (project_analysis.project_success_rate + customer_satisfaction * 20) / 2

        # 效率KPI
        productivity_index = efficiency_analysis.average_efficiency_score
        efficiency_score = efficiency_analysis.average_efficiency_score
        time_to_completion = project_analysis.average_completion_time

        # 综合评分计算
        scores = [
            revenue_growth / 10,  # 收入增长权重
            profit_margin / 10,   # 利润率权重
            customer_satisfaction * 20,  # 客户满意度权重
            project_delivery_rate,  # 交付率权重
            efficiency_score  # 效率权重
        ]
        overall_performance_score = sum(scores) / len(scores)

        # 绩效等级
        if overall_performance_score >= 80:
            performance_grade = "优秀"
        elif overall_performance_score >= 70:
            performance_grade = "良好"
        elif overall_performance_score >= 60:
            performance_grade = "合格"
        else:
            performance_grade = "需改进"

        # 改进领域
        improvement_areas = []
        if revenue_growth < 10:
            improvement_areas.append("收入增长")
        if customer_satisfaction < 4:
            improvement_areas.append("客户满意度")
        if project_delivery_rate < 80:
            improvement_areas.append("项目交付")
        if efficiency_score < 70:
            improvement_areas.append("工作效率")

        return PerformanceMetrics(
            revenue_growth=revenue_growth,
            profit_margin=profit_margin,
            average_project_value=average_project_value,
            customer_satisfaction=customer_satisfaction,
            customer_retention_rate=customer_retention_rate,
            net_promoter_score=net_promoter_score,
            project_delivery_rate=project_delivery_rate,
            resource_utilization=resource_utilization,
            quality_score=quality_score,
            productivity_index=productivity_index,
            efficiency_score=efficiency_score,
            time_to_completion=time_to_completion,
            overall_performance_score=overall_performance_score,
            performance_grade=performance_grade,
            improvement_areas=improvement_areas
        )

    def generate_analytics_report(self, start_date: date = None, end_date: date = None,
                                 report_type: str = "comprehensive") -> AnalyticsReport:
        """
        生成综合分析报告

        Args:
            start_date: 开始日期
            end_date: 结束日期
            report_type: 报告类型

        Returns:
            AnalyticsReport: 综合分析报告
        """
        # 生成报告ID
        report_id = f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 获取所有分析数据
        business_overview = self.get_business_overview(start_date, end_date)
        revenue_analysis = self.get_revenue_analysis(start_date, end_date)
        customer_analysis = self.get_customer_analysis(start_date, end_date)
        project_analysis = self.get_project_analysis(start_date, end_date)
        efficiency_analysis = self.get_work_efficiency_analysis(start_date, end_date)
        performance_metrics = self.get_performance_metrics(start_date, end_date)

        # 生成趋势分析（简化版）
        trend_analysis = TrendAnalysis(
            revenue_trend=[{'period': 'current', 'revenue': float(revenue_analysis.monthly_revenue)}],
            revenue_forecast=revenue_analysis.revenue_forecast,
            seasonal_patterns={'peak_season': 'Q4', 'low_season': 'Q1'},
            customer_acquisition_trend=customer_analysis.customer_acquisition_trend,
            customer_retention_trend=[{'period': 'current', 'retention_rate': customer_analysis.customer_retention_rate}],
            workload_trend=[{'period': 'current', 'hours': efficiency_analysis.total_work_hours}],
            capacity_utilization=[{'period': 'current', 'utilization': efficiency_analysis.billable_ratio}],
            demand_forecast=[{'period': 'next_month', 'predicted_demand': 'stable'}],
            market_opportunities=['数字化转型', '远程办公解决方案', 'AI集成服务'],
            risk_indicators=[{'indicator': 'customer_churn', 'level': 'low'}],
            early_warnings=['注意客户满意度下降趋势'] if customer_analysis.average_satisfaction < 4 else []
        )

        # 生成执行摘要
        executive_summary = f"""
        本期业务表现总体{performance_metrics.performance_grade}。
        总收入{float(revenue_analysis.total_revenue):.2f}元，
        客户满意度{customer_analysis.average_satisfaction:.1f}分，
        项目按时交付率{project_analysis.on_time_delivery_rate:.1f}%。
        """

        # 关键洞察
        key_insights = [
            f"收入增长率为{business_overview.revenue_growth_rate:.1f}%",
            f"客户留存率达到{customer_analysis.customer_retention_rate:.1f}%",
            f"工作效率得分{efficiency_analysis.average_efficiency_score:.1f}分",
            f"项目成功率{project_analysis.project_success_rate:.1f}%"
        ]

        # 建议
        recommendations = [
            "继续保持高质量的客户服务",
            "优化项目管理流程以提高交付效率",
            "加强客户关系维护以提升满意度",
            "探索新的收入增长点"
        ]

        # 数据质量评分（简化）
        data_quality_score = 85.0
        confidence_level = 90.0

        # 下次审查日期
        next_review_date = (datetime.now() + timedelta(days=30)).date()

        return AnalyticsReport(
            report_id=report_id,
            report_type=report_type,
            generated_at=datetime.now(),
            period=f"{start_date} to {end_date}" if start_date and end_date else "current_period",
            executive_summary=executive_summary.strip(),
            key_insights=key_insights,
            recommendations=recommendations,
            business_overview=business_overview,
            revenue_analysis=revenue_analysis,
            customer_analysis=customer_analysis,
            project_analysis=project_analysis,
            efficiency_analysis=efficiency_analysis,
            trend_analysis=trend_analysis,
            performance_metrics=performance_metrics,
            data_quality_score=data_quality_score,
            confidence_level=confidence_level,
            next_review_date=next_review_date
        )

    def get_custom_analytics(self, query: AnalyticsQuery) -> Dict[str, Any]:
        """
        获取自定义分析

        Args:
            query: 分析查询参数

        Returns:
            Dict[str, Any]: 自定义分析结果
        """
        # 根据查询参数构建基础查询
        base_query = self.db.query(Order)

        # 应用时间筛选
        if query.start_date:
            start_datetime = datetime.combine(query.start_date, datetime.min.time())
            base_query = base_query.filter(Order.created_at >= start_datetime)

        if query.end_date:
            end_datetime = datetime.combine(query.end_date, datetime.max.time())
            base_query = base_query.filter(Order.created_at <= end_datetime)

        # 应用其他筛选条件
        if query.customer_ids:
            base_query = base_query.filter(Order.customer_id.in_(query.customer_ids))

        if query.category_ids:
            base_query = base_query.filter(Order.category_id.in_(query.category_ids))

        if query.order_statuses:
            base_query = base_query.filter(Order.status.in_(query.order_statuses))

        orders = base_query.all()

        # 根据分组维度处理数据
        result_data = []
        summary_stats = {}

        if query.group_by == "customer":
            # 按客户分组
            customer_data = {}
            for order in orders:
                customer_id = order.customer_id
                if customer_id not in customer_data:
                    customer_data[customer_id] = {
                        'customer_id': customer_id,
                        'customer_name': order.customer.name if order.customer else '未知客户',
                        'orders_count': 0,
                        'total_amount': Decimal('0.00'),
                        'completed_amount': Decimal('0.00')
                    }
                customer_data[customer_id]['orders_count'] += 1
                customer_data[customer_id]['total_amount'] += order.total_amount
                if order.status in [OrderStatus.COMPLETED, OrderStatus.SETTLED]:
                    customer_data[customer_id]['completed_amount'] += order.actual_amount or Decimal('0.00')

            result_data = list(customer_data.values())
            summary_stats = {
                'total_customers': len(customer_data),
                'total_orders': len(orders),
                'total_amount': float(sum(order.total_amount for order in orders))
            }

        elif query.group_by == "category":
            # 按分类分组
            category_data = {}
            for order in orders:
                category_name = order.category.name if order.category else '未分类'
                if category_name not in category_data:
                    category_data[category_name] = {
                        'category_name': category_name,
                        'orders_count': 0,
                        'total_amount': Decimal('0.00'),
                        'avg_amount': Decimal('0.00')
                    }
                category_data[category_name]['orders_count'] += 1
                category_data[category_name]['total_amount'] += order.total_amount

            # 计算平均值
            for data in category_data.values():
                data['avg_amount'] = float(data['total_amount'] / data['orders_count'])
                data['total_amount'] = float(data['total_amount'])

            result_data = list(category_data.values())
            summary_stats = {
                'total_categories': len(category_data),
                'total_orders': len(orders)
            }

        else:
            # 默认返回订单列表
            result_data = [
                {
                    'order_id': order.id,
                    'title': order.title,
                    'customer_name': order.customer.name if order.customer else '未知客户',
                    'total_amount': float(order.total_amount),
                    'status': order.status,
                    'created_at': order.created_at.isoformat()
                }
                for order in orders
            ]
            summary_stats = {
                'total_orders': len(orders),
                'total_amount': float(sum(order.total_amount for order in orders))
            }

        return {
            'result_data': result_data,
            'summary_stats': summary_stats,
            'query_params': query.model_dump(),
            'generated_at': datetime.now().isoformat()
        }
