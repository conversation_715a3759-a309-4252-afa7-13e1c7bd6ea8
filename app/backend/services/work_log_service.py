"""
工时管理业务逻辑服务

提供工时记录相关的业务逻辑处理
包含工时CRUD操作、计时器管理、统计分析等功能
"""

from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc

from app.models.work_log import WorkLog
from app.models.order import Order
from app.models.customer import Customer
from app.backend.schemas.work_log import (
    WorkLogCreate, WorkLogUpdate, WorkLogListParams,
    TimerOperation, TimerStatus, WorkLogStatistics,
    DailyWorkSummary, WeeklyWorkSummary, MonthlyWorkSummary
)
from app.core.exceptions import (
    WorkLogNotFoundException, OrderNotFoundException,
    BusinessException, ValidationException
)

class WorkLogService:
    """
    工时管理服务类
    
    提供工时记录相关的所有业务逻辑操作
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_work_log(self, work_log_data: WorkLogCreate) -> WorkLog:
        """
        创建新工时记录
        
        Args:
            work_log_data: 工时记录创建数据
            
        Returns:
            WorkLog: 创建的工时记录对象
            
        Raises:
            OrderNotFoundException: 订单不存在
            ValidationException: 数据验证失败
        """
        # 验证订单是否存在
        order = self.db.query(Order).filter(Order.id == work_log_data.order_id).first()
        if not order:
            raise OrderNotFoundException(work_log_data.order_id)
        
        # 检查是否已有正在运行的计时器
        running_timer = self.db.query(WorkLog).filter(
            and_(
                WorkLog.order_id == work_log_data.order_id,
                WorkLog.is_running == True
            )
        ).first()
        
        if running_timer:
            raise BusinessException(f"订单 {work_log_data.order_id} 已有正在运行的计时器")
        
        # 创建工时记录对象
        work_log_dict = work_log_data.model_dump(exclude_unset=True)

        # 如果提供了开始和结束时间，自动计算时长
        if work_log_dict.get('start_time') and work_log_dict.get('end_time'):
            start_time = work_log_dict['start_time']
            end_time = work_log_dict['end_time']
            delta = end_time - start_time
            work_log_dict['duration'] = int(delta.total_seconds() / 60)

        # 如果没有提供结束时间但有时长，计算结束时间
        elif work_log_dict.get('start_time') and work_log_dict.get('duration') and not work_log_dict.get('end_time'):
            start_time = work_log_dict['start_time']
            duration_minutes = work_log_dict['duration']
            work_log_dict['end_time'] = start_time + timedelta(minutes=duration_minutes)

        work_log = WorkLog(**work_log_dict)
        
        # 验证业务规则
        self._validate_work_log_business_rules(work_log)
        
        self.db.add(work_log)
        self.db.commit()
        self.db.refresh(work_log)
        
        # 更新订单的实际工时
        self._update_order_actual_hours(work_log.order_id)
        
        return work_log
    
    def get_work_log(self, work_log_id: str) -> WorkLog:
        """
        根据ID获取工时记录
        
        Args:
            work_log_id: 工时记录ID
            
        Returns:
            WorkLog: 工时记录对象
            
        Raises:
            WorkLogNotFoundException: 工时记录不存在
        """
        work_log = self.db.query(WorkLog).options(
            joinedload(WorkLog.order).joinedload(Order.customer)
        ).filter(WorkLog.id == work_log_id).first()
        
        if not work_log:
            raise WorkLogNotFoundException(work_log_id)
        return work_log
    
    def update_work_log(self, work_log_id: str, work_log_data: WorkLogUpdate) -> WorkLog:
        """
        更新工时记录信息
        
        Args:
            work_log_id: 工时记录ID
            work_log_data: 更新数据
            
        Returns:
            WorkLog: 更新后的工时记录对象
            
        Raises:
            WorkLogNotFoundException: 工时记录不存在
            BusinessException: 正在计时的记录不能修改时间
        """
        work_log = self.get_work_log(work_log_id)
        
        # 正在计时的记录不能修改时间相关字段
        if work_log.is_running:
            time_fields = ['start_time', 'end_time', 'duration']
            update_dict = work_log_data.model_dump(exclude_unset=True)
            if any(field in update_dict for field in time_fields):
                raise BusinessException("正在计时的工时记录不能修改时间信息")
        
        # 更新字段
        update_data = work_log_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(work_log, field):
                setattr(work_log, field, value)
        
        # 如果更新了时间信息，重新计算时长
        if 'start_time' in update_data or 'end_time' in update_data:
            if work_log.start_time and work_log.end_time:
                delta = work_log.end_time - work_log.start_time
                work_log.duration = int(delta.total_seconds() / 60)
        
        # 验证业务规则
        self._validate_work_log_business_rules(work_log)
        
        self.db.commit()
        self.db.refresh(work_log)
        
        # 更新订单的实际工时
        self._update_order_actual_hours(work_log.order_id)
        
        return work_log
    
    def delete_work_log(self, work_log_id: str) -> bool:
        """
        删除工时记录
        
        Args:
            work_log_id: 工时记录ID
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            WorkLogNotFoundException: 工时记录不存在
            BusinessException: 正在计时的记录不能删除
        """
        work_log = self.get_work_log(work_log_id)
        
        # 正在计时的记录不能删除
        if work_log.is_running:
            raise BusinessException("正在计时的工时记录不能删除，请先停止计时")
        
        order_id = work_log.order_id
        
        self.db.delete(work_log)
        self.db.commit()
        
        # 更新订单的实际工时
        self._update_order_actual_hours(order_id)
        
        return True
    
    def list_work_logs(self, params: WorkLogListParams) -> Tuple[List[WorkLog], int]:
        """
        获取工时记录列表
        
        Args:
            params: 查询参数
            
        Returns:
            Tuple[List[WorkLog], int]: 工时记录列表和总数
        """
        query = self.db.query(WorkLog).options(
            joinedload(WorkLog.order).joinedload(Order.customer)
        )
        
        # 应用筛选条件
        if params.keyword:
            keyword_filter = or_(
                WorkLog.description.contains(params.keyword),
                WorkLog.work_type.contains(params.keyword),
                WorkLog.notes.contains(params.keyword)
            )
            query = query.filter(keyword_filter)
        
        if params.order_id:
            query = query.filter(WorkLog.order_id == params.order_id)
        
        if params.work_type:
            query = query.filter(WorkLog.work_type == params.work_type)
        
        if params.billable is not None:
            query = query.filter(WorkLog.billable == params.billable)
        
        if params.is_running is not None:
            query = query.filter(WorkLog.is_running == params.is_running)
        
        # 时间范围筛选
        if params.start_date:
            start_datetime = datetime.combine(params.start_date, datetime.min.time())
            query = query.filter(WorkLog.start_time >= start_datetime)
        
        if params.end_date:
            end_datetime = datetime.combine(params.end_date, datetime.max.time())
            query = query.filter(WorkLog.start_time <= end_datetime)
        
        if params.work_date_from:
            from_datetime = datetime.combine(params.work_date_from, datetime.min.time())
            query = query.filter(WorkLog.start_time >= from_datetime)
        
        if params.work_date_to:
            to_datetime = datetime.combine(params.work_date_to, datetime.max.time())
            query = query.filter(WorkLog.start_time <= to_datetime)
        
        # 时长筛选
        if params.min_duration is not None:
            query = query.filter(WorkLog.duration >= params.min_duration)
        
        if params.max_duration is not None:
            query = query.filter(WorkLog.duration <= params.max_duration)
        
        # 时薪筛选
        if params.min_hourly_rate is not None:
            query = query.filter(WorkLog.hourly_rate >= params.min_hourly_rate)
        
        if params.max_hourly_rate is not None:
            query = query.filter(WorkLog.hourly_rate <= params.max_hourly_rate)
        
        # 获取总数
        total = query.count()
        
        # 应用排序
        if params.sort_by:
            sort_column = getattr(WorkLog, params.sort_by, None)
            if sort_column:
                if params.sort_order == "asc":
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
        else:
            # 默认按开始时间倒序
            query = query.order_by(desc(WorkLog.start_time))
        
        # 应用分页
        work_logs = query.offset(params.offset).limit(params.limit).all()
        
        return work_logs, total
    
    def timer_operation(self, operation_data: TimerOperation) -> WorkLog:
        """
        计时器操作
        
        Args:
            operation_data: 计时器操作数据
            
        Returns:
            WorkLog: 操作后的工时记录对象
            
        Raises:
            OrderNotFoundException: 订单不存在
            BusinessException: 操作不合法
        """
        operation = operation_data.operation
        order_id = operation_data.order_id
        
        # 验证订单是否存在
        order = self.db.query(Order).filter(Order.id == order_id).first()
        if not order:
            raise OrderNotFoundException(order_id)
        
        # 查找当前正在运行的计时器
        running_timer = self.db.query(WorkLog).filter(
            and_(
                WorkLog.order_id == order_id,
                WorkLog.is_running == True
            )
        ).first()
        
        if operation == "start":
            if running_timer:
                raise BusinessException(f"订单 {order_id} 已有正在运行的计时器")
            
            # 创建新的工时记录并开始计时
            work_log = WorkLog(
                order_id=order_id,
                description=operation_data.description,
                work_type=operation_data.work_type,
                hourly_rate=operation_data.hourly_rate,
                billable=operation_data.billable
            )
            work_log.start_timer()
            
            self.db.add(work_log)
            self.db.commit()
            self.db.refresh(work_log)
            
            return work_log
        
        elif operation == "stop":
            if not running_timer:
                raise BusinessException(f"订单 {order_id} 没有正在运行的计时器")
            
            running_timer.stop_timer()
            self.db.commit()
            self.db.refresh(running_timer)
            
            # 更新订单的实际工时
            self._update_order_actual_hours(order_id)
            
            return running_timer
        
        elif operation == "pause":
            if not running_timer:
                raise BusinessException(f"订单 {order_id} 没有正在运行的计时器")
            
            running_timer.pause_timer()
            self.db.commit()
            self.db.refresh(running_timer)
            
            return running_timer
        
        elif operation == "resume":
            if not running_timer:
                raise BusinessException(f"订单 {order_id} 没有暂停的计时器")
            
            if running_timer.is_running:
                raise BusinessException(f"订单 {order_id} 的计时器已在运行中")
            
            running_timer.resume_timer()
            self.db.commit()
            self.db.refresh(running_timer)
            
            return running_timer
        
        else:
            raise ValidationException(f"不支持的计时器操作: {operation}")
    
    def get_timer_status(self, order_id: str = None) -> TimerStatus:
        """
        获取计时器状态
        
        Args:
            order_id: 订单ID（可选，如果不提供则返回所有正在运行的计时器）
            
        Returns:
            TimerStatus: 计时器状态
        """
        query = self.db.query(WorkLog).options(
            joinedload(WorkLog.order)
        ).filter(WorkLog.is_running == True)
        
        if order_id:
            query = query.filter(WorkLog.order_id == order_id)
        
        running_timer = query.first()
        
        if running_timer:
            current_duration = running_timer.get_current_duration()
            hours = current_duration // 60
            minutes = current_duration % 60
            duration_display = f"{hours}小时{minutes}分钟" if hours > 0 else f"{minutes}分钟"
            
            return TimerStatus(
                is_running=True,
                work_log_id=running_timer.id,
                order_id=running_timer.order_id,
                order_title=running_timer.order.title if running_timer.order else None,
                start_time=running_timer.start_time,
                current_duration=current_duration,
                current_duration_display=duration_display
            )
        else:
            return TimerStatus(is_running=False)
    
    def get_work_log_statistics(self, 
                               start_date: date = None, 
                               end_date: date = None,
                               order_id: str = None) -> WorkLogStatistics:
        """
        获取工时统计信息
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            order_id: 订单ID筛选
            
        Returns:
            WorkLogStatistics: 工时统计数据
        """
        query = self.db.query(WorkLog)
        
        # 应用筛选条件
        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())
            query = query.filter(WorkLog.start_time >= start_datetime)
        
        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())
            query = query.filter(WorkLog.start_time <= end_datetime)
        
        if order_id:
            query = query.filter(WorkLog.order_id == order_id)
        
        work_logs = query.all()
        
        # 基础统计
        total_logs = len(work_logs)
        total_duration = sum(log.duration for log in work_logs)
        total_hours = total_duration / 60.0
        
        billable_logs = [log for log in work_logs if log.billable]
        billable_duration = sum(log.duration for log in billable_logs)
        billable_hours = billable_duration / 60.0
        
        # 收入统计
        total_earnings = sum(log.earnings for log in work_logs)
        average_hourly_rate = total_earnings / billable_hours if billable_hours > 0 else 0
        
        # 效率统计
        efficiency_scores = [log.calculate_efficiency_score() for log in work_logs]
        average_efficiency_score = sum(efficiency_scores) / len(efficiency_scores) if efficiency_scores else 0
        
        # 工作类型分布
        work_type_distribution = {}
        for log in work_logs:
            work_type = log.work_type or "未分类"
            work_type_distribution[work_type] = work_type_distribution.get(work_type, 0) + 1
        
        # 每日分布
        daily_distribution = {}
        for log in work_logs:
            work_date = log.work_date
            daily_distribution[work_date] = daily_distribution.get(work_date, 0) + log.duration
        
        # 每小时分布
        hourly_distribution = {}
        for log in work_logs:
            hour = log.start_time.hour
            hourly_distribution[str(hour)] = hourly_distribution.get(str(hour), 0) + log.duration
        
        # 趋势数据
        daily_hours = []
        for date_str, duration in daily_distribution.items():
            daily_hours.append({
                "date": date_str,
                "hours": duration / 60.0,
                "duration": duration
            })
        daily_hours.sort(key=lambda x: x["date"])
        
        # 周度和月度汇总（简化版）
        weekly_summary = {"total_hours": total_hours, "total_earnings": float(total_earnings)}
        monthly_summary = {"total_hours": total_hours, "total_earnings": float(total_earnings)}
        
        return WorkLogStatistics(
            total_logs=total_logs,
            total_duration=total_duration,
            total_hours=total_hours,
            billable_duration=billable_duration,
            billable_hours=billable_hours,
            total_earnings=Decimal(str(total_earnings)),
            average_hourly_rate=average_hourly_rate,
            average_efficiency_score=average_efficiency_score,
            work_type_distribution=work_type_distribution,
            daily_distribution=daily_distribution,
            hourly_distribution=hourly_distribution,
            daily_hours=daily_hours,
            weekly_summary=weekly_summary,
            monthly_summary=monthly_summary
        )
    
    def _validate_work_log_business_rules(self, work_log: WorkLog) -> None:
        """
        验证工时记录业务规则
        
        Args:
            work_log: 工时记录对象
            
        Raises:
            ValidationException: 业务规则验证失败
        """
        # 验证时间逻辑
        if work_log.end_time and work_log.start_time:
            if work_log.end_time < work_log.start_time:
                raise ValidationException("结束时间不能早于开始时间")
        
        # 验证工作时长
        if work_log.duration is not None:
            if work_log.duration < 0:
                raise ValidationException("工作时长不能为负数")

            if work_log.duration > 24 * 60:  # 24小时
                raise ValidationException("单次工作时长不能超过24小时")
        
        # 验证时薪
        if work_log.hourly_rate and work_log.hourly_rate < 0:
            raise ValidationException("时薪不能为负数")
    
    def _update_order_actual_hours(self, order_id: str) -> None:
        """
        更新订单的实际工时
        
        Args:
            order_id: 订单ID
        """
        try:
            order = self.db.query(Order).filter(Order.id == order_id).first()
            if order:
                order.update_actual_hours()
                self.db.commit()
        except Exception:
            # 忽略更新错误，不影响主业务流程
            self.db.rollback()

    def get_work_logs_by_order(self, order_id: str) -> List[WorkLog]:
        """
        根据订单获取工时记录列表

        Args:
            order_id: 订单ID

        Returns:
            List[WorkLog]: 工时记录列表
        """
        return self.db.query(WorkLog).filter(
            WorkLog.order_id == order_id
        ).order_by(desc(WorkLog.start_time)).all()

    def get_running_timers(self) -> List[WorkLog]:
        """
        获取所有正在运行的计时器

        Returns:
            List[WorkLog]: 正在运行的工时记录列表
        """
        return self.db.query(WorkLog).options(
            joinedload(WorkLog.order).joinedload(Order.customer)
        ).filter(WorkLog.is_running == True).all()

    def stop_all_running_timers(self) -> int:
        """
        停止所有正在运行的计时器

        Returns:
            int: 停止的计时器数量
        """
        running_timers = self.get_running_timers()

        for timer in running_timers:
            timer.stop_timer()
            # 更新订单的实际工时
            self._update_order_actual_hours(timer.order_id)

        self.db.commit()
        return len(running_timers)

    def get_daily_work_summary(self, work_date: date) -> DailyWorkSummary:
        """
        获取指定日期的工作摘要

        Args:
            work_date: 工作日期

        Returns:
            DailyWorkSummary: 每日工作摘要
        """
        start_datetime = datetime.combine(work_date, datetime.min.time())
        end_datetime = datetime.combine(work_date, datetime.max.time())

        work_logs = self.db.query(WorkLog).options(
            joinedload(WorkLog.order).joinedload(Order.customer)
        ).filter(
            and_(
                WorkLog.start_time >= start_datetime,
                WorkLog.start_time <= end_datetime
            )
        ).all()

        # 基础统计
        total_logs = len(work_logs)
        total_duration = sum(log.duration for log in work_logs)
        total_hours = total_duration / 60.0

        billable_logs = [log for log in work_logs if log.billable]
        billable_hours = sum(log.duration for log in billable_logs) / 60.0

        total_earnings = sum(log.earnings for log in work_logs)
        average_hourly_rate = total_earnings / billable_hours if billable_hours > 0 else 0

        # 工作的订单列表
        orders_worked = []
        order_durations = {}
        for log in work_logs:
            order_id = log.order_id
            if order_id not in order_durations:
                order_durations[order_id] = {
                    "order_id": order_id,
                    "order_title": log.order.title if log.order else "未知订单",
                    "customer_name": log.order.customer.name if log.order and log.order.customer else "未知客户",
                    "duration": 0,
                    "hours": 0,
                    "earnings": 0
                }
            order_durations[order_id]["duration"] += log.duration
            order_durations[order_id]["hours"] += log.duration / 60.0
            order_durations[order_id]["earnings"] += log.earnings

        orders_worked = list(order_durations.values())
        orders_worked.sort(key=lambda x: x["duration"], reverse=True)

        # 工作类型分布
        work_types = {}
        for log in work_logs:
            work_type = log.work_type or "未分类"
            work_types[work_type] = work_types.get(work_type, 0) + log.duration

        # 每小时工作分解
        hourly_breakdown = []
        for hour in range(24):
            hour_logs = [log for log in work_logs if log.start_time.hour == hour]
            if hour_logs:
                hour_duration = sum(log.duration for log in hour_logs)
                hourly_breakdown.append({
                    "hour": hour,
                    "duration": hour_duration,
                    "hours": hour_duration / 60.0,
                    "logs_count": len(hour_logs)
                })

        return DailyWorkSummary(
            work_date=work_date,
            total_logs=total_logs,
            total_duration=total_duration,
            total_hours=total_hours,
            billable_hours=billable_hours,
            total_earnings=Decimal(str(total_earnings)),
            average_hourly_rate=average_hourly_rate,
            orders_worked=orders_worked,
            work_types=work_types,
            hourly_breakdown=hourly_breakdown
        )

    def get_weekly_work_summary(self, week_start: date) -> WeeklyWorkSummary:
        """
        获取指定周的工作摘要

        Args:
            week_start: 周开始日期（周一）

        Returns:
            WeeklyWorkSummary: 每周工作摘要
        """
        week_end = week_start + timedelta(days=6)

        # 获取每日摘要
        daily_summaries = []
        total_hours = 0
        billable_hours = 0
        total_earnings = Decimal('0.00')

        for i in range(7):
            current_date = week_start + timedelta(days=i)
            daily_summary = self.get_daily_work_summary(current_date)
            daily_summaries.append(daily_summary)

            total_hours += daily_summary.total_hours
            billable_hours += daily_summary.billable_hours
            total_earnings += daily_summary.total_earnings

        # 计算平均每日工作时长
        working_days = len([d for d in daily_summaries if d.total_hours > 0])
        average_daily_hours = total_hours / working_days if working_days > 0 else 0

        # 找出最高效的工作日
        most_productive_day = None
        max_hours = 0
        for daily in daily_summaries:
            if daily.total_hours > max_hours:
                max_hours = daily.total_hours
                most_productive_day = daily.work_date.strftime("%A")  # 星期几

        # 统计工作的订单总数
        all_orders = set()
        for daily in daily_summaries:
            for order in daily.orders_worked:
                all_orders.add(order["order_id"])

        # 简化的完成率计算（基于工作时长）
        completion_rate = min(1.0, total_hours / 40.0) if total_hours > 0 else 0  # 假设40小时为满工作周

        return WeeklyWorkSummary(
            week_start=week_start,
            week_end=week_end,
            total_hours=total_hours,
            billable_hours=billable_hours,
            total_earnings=total_earnings,
            average_daily_hours=average_daily_hours,
            daily_summaries=daily_summaries,
            most_productive_day=most_productive_day,
            total_orders_worked=len(all_orders),
            completion_rate=completion_rate
        )

    def get_monthly_work_summary(self, year: int, month: int) -> MonthlyWorkSummary:
        """
        获取指定月份的工作摘要

        Args:
            year: 年份
            month: 月份

        Returns:
            MonthlyWorkSummary: 每月工作摘要
        """
        # 计算月份的第一天和最后一天
        month_start = date(year, month, 1)
        if month == 12:
            month_end = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            month_end = date(year, month + 1, 1) - timedelta(days=1)

        # 获取月度统计
        statistics = self.get_work_log_statistics(month_start, month_end)

        # 计算工作天数
        working_days = len([d for d in statistics.daily_distribution.keys()
                           if statistics.daily_distribution[d] > 0])

        average_daily_hours = statistics.total_hours / working_days if working_days > 0 else 0

        # 获取周度摘要（简化版）
        weekly_summaries = []
        current_date = month_start
        while current_date <= month_end:
            # 找到当前周的周一
            week_start = current_date - timedelta(days=current_date.weekday())
            if week_start < month_start:
                week_start = month_start

            week_end = min(week_start + timedelta(days=6), month_end)

            if week_start <= month_end:
                weekly_summary = self.get_weekly_work_summary(week_start)
                weekly_summaries.append(weekly_summary)

            current_date = week_end + timedelta(days=1)

        # 获取工时最多的订单
        order_hours = {}
        start_datetime = datetime.combine(month_start, datetime.min.time())
        end_datetime = datetime.combine(month_end, datetime.max.time())

        work_logs = self.db.query(WorkLog).options(
            joinedload(WorkLog.order)
        ).filter(
            and_(
                WorkLog.start_time >= start_datetime,
                WorkLog.start_time <= end_datetime
            )
        ).all()

        for log in work_logs:
            order_id = log.order_id
            if order_id not in order_hours:
                order_hours[order_id] = {
                    "order_id": order_id,
                    "order_title": log.order.title if log.order else "未知订单",
                    "total_hours": 0,
                    "total_earnings": 0
                }
            order_hours[order_id]["total_hours"] += log.duration / 60.0
            order_hours[order_id]["total_earnings"] += log.earnings

        top_orders = sorted(order_hours.values(), key=lambda x: x["total_hours"], reverse=True)[:10]

        # 效率趋势（简化版）
        efficiency_trend = []
        for date_str in sorted(statistics.daily_distribution.keys()):
            daily_logs = [log for log in work_logs if log.work_date == date_str]
            if daily_logs:
                avg_efficiency = sum(log.calculate_efficiency_score() for log in daily_logs) / len(daily_logs)
                efficiency_trend.append({
                    "date": date_str,
                    "efficiency_score": avg_efficiency,
                    "total_hours": sum(log.duration for log in daily_logs) / 60.0
                })

        return MonthlyWorkSummary(
            year=year,
            month=month,
            total_hours=statistics.total_hours,
            billable_hours=statistics.billable_hours,
            total_earnings=statistics.total_earnings,
            average_daily_hours=average_daily_hours,
            working_days=working_days,
            weekly_summaries=weekly_summaries,
            top_orders=top_orders,
            efficiency_trend=efficiency_trend
        )

    def batch_update_work_logs(self, work_log_ids: List[str], update_data: WorkLogUpdate) -> List[WorkLog]:
        """
        批量更新工时记录

        Args:
            work_log_ids: 工时记录ID列表
            update_data: 更新数据

        Returns:
            List[WorkLog]: 更新后的工时记录列表
        """
        work_logs = []
        for work_log_id in work_log_ids:
            try:
                work_log = self.update_work_log(work_log_id, update_data)
                work_logs.append(work_log)
            except WorkLogNotFoundException:
                # 跳过不存在的工时记录
                continue

        return work_logs
