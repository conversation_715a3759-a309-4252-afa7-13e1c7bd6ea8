"""
客户管理业务逻辑服务

提供客户相关的业务逻辑处理
包含客户CRUD操作、统计分析、数据验证等功能
"""

from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from datetime import date, datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc

from app.models.customer import Customer
from app.models.order import Order
from app.backend.schemas.customer import (
    CustomerCreate, CustomerUpdate, CustomerListParams,
    CustomerStatistics, CustomerOrderSummary
)
from app.core.exceptions import (
    CustomerNotFoundException, DuplicateEmailException,
    BusinessException, ValidationException
)

class CustomerService:
    """
    客户管理服务类
    
    提供客户相关的所有业务逻辑操作
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_customer(self, customer_data: CustomerCreate) -> Customer:
        """
        创建新客户
        
        Args:
            customer_data: 客户创建数据
            
        Returns:
            Customer: 创建的客户对象
            
        Raises:
            DuplicateEmailException: 邮箱已存在
            ValidationException: 数据验证失败
        """
        # 检查邮箱是否已存在
        if customer_data.contact_email:
            existing_customer = self.db.query(Customer).filter(
                Customer.contact_email == customer_data.contact_email
            ).first()
            if existing_customer:
                raise DuplicateEmailException(customer_data.contact_email)
        
        # 验证至少有一种联系方式
        if not any([
            customer_data.contact_phone,
            customer_data.contact_email,
            customer_data.contact_wechat,
            customer_data.contact_qq
        ]):
            raise ValidationException("至少需要提供一种联系方式")
        
        # 创建客户对象
        customer = Customer(**customer_data.model_dump(exclude_unset=True))
        
        # 设置默认偏好
        if not customer.preferences:
            customer.preferences = {
                "preferred_categories": [],
                "preferred_billing": "fixed",
                "budget_range": {"min": 0, "max": 0},
                "communication_preference": "wechat",
                "delivery_preference": "email",
                "special_requirements": ""
            }
        
        self.db.add(customer)
        self.db.commit()
        self.db.refresh(customer)
        
        return customer
    
    def get_customer(self, customer_id: str) -> Customer:
        """
        根据ID获取客户
        
        Args:
            customer_id: 客户ID
            
        Returns:
            Customer: 客户对象
            
        Raises:
            CustomerNotFoundException: 客户不存在
        """
        customer = self.db.query(Customer).filter(Customer.id == customer_id).first()
        if not customer:
            raise CustomerNotFoundException(customer_id)
        return customer
    
    def update_customer(self, customer_id: str, customer_data: CustomerUpdate) -> Customer:
        """
        更新客户信息
        
        Args:
            customer_id: 客户ID
            customer_data: 更新数据
            
        Returns:
            Customer: 更新后的客户对象
            
        Raises:
            CustomerNotFoundException: 客户不存在
            DuplicateEmailException: 邮箱已存在
        """
        customer = self.get_customer(customer_id)
        
        # 检查邮箱是否重复（排除当前客户）
        if customer_data.contact_email and customer_data.contact_email != customer.contact_email:
            existing_customer = self.db.query(Customer).filter(
                and_(
                    Customer.contact_email == customer_data.contact_email,
                    Customer.id != customer_id
                )
            ).first()
            if existing_customer:
                raise DuplicateEmailException(customer_data.contact_email)
        
        # 更新字段
        update_data = customer_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(customer, field):
                setattr(customer, field, value)
        
        self.db.commit()
        self.db.refresh(customer)
        
        return customer
    
    def delete_customer(self, customer_id: str, force: bool = False) -> bool:
        """
        删除客户
        
        Args:
            customer_id: 客户ID
            force: 是否强制删除（即使有关联订单）
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            CustomerNotFoundException: 客户不存在
            BusinessException: 有关联订单且非强制删除
        """
        customer = self.get_customer(customer_id)
        
        # 检查是否有关联订单
        order_count = self.db.query(Order).filter(Order.customer_id == customer_id).count()
        if order_count > 0 and not force:
            raise BusinessException(f"客户有 {order_count} 个关联订单，无法删除。如需强制删除，请设置 force=True")
        
        # 如果强制删除，先删除关联订单
        if force and order_count > 0:
            self.db.query(Order).filter(Order.customer_id == customer_id).delete()
        
        self.db.delete(customer)
        self.db.commit()
        
        return True
    
    def list_customers(self, params: CustomerListParams) -> Tuple[List[Customer], int]:
        """
        获取客户列表
        
        Args:
            params: 查询参数
            
        Returns:
            Tuple[List[Customer], int]: 客户列表和总数
        """
        query = self.db.query(Customer)
        
        # 应用筛选条件
        if params.keyword:
            keyword_filter = or_(
                Customer.name.contains(params.keyword),
                Customer.company.contains(params.keyword),
                Customer.contact_email.contains(params.keyword),
                Customer.contact_phone.contains(params.keyword)
            )
            query = query.filter(keyword_filter)
        
        if params.source:
            query = query.filter(Customer.source == params.source)
        
        if params.is_vip is not None:
            query = query.filter(Customer.is_vip == params.is_vip)
        
        if params.is_active is not None:
            query = query.filter(Customer.is_active == params.is_active)
        
        if params.has_orders is not None:
            if params.has_orders:
                query = query.filter(Customer.total_orders > 0)
            else:
                query = query.filter(Customer.total_orders == 0)
        
        if params.min_total_amount is not None:
            query = query.filter(Customer.total_amount >= params.min_total_amount)
        
        if params.max_total_amount is not None:
            query = query.filter(Customer.total_amount <= params.max_total_amount)
        
        # 获取总数
        total = query.count()
        
        # 应用排序
        if params.sort_by:
            sort_column = getattr(Customer, params.sort_by, None)
            if sort_column:
                if params.sort_order == "asc":
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
        else:
            # 默认按创建时间倒序
            query = query.order_by(desc(Customer.created_at))
        
        # 应用分页
        customers = query.offset(params.offset).limit(params.limit).all()
        
        return customers, total
    
    def get_customer_statistics(self) -> CustomerStatistics:
        """
        获取客户统计信息
        
        Returns:
            CustomerStatistics: 客户统计数据
        """
        # 基础统计
        total_customers = self.db.query(Customer).count()
        active_customers = self.db.query(Customer).filter(Customer.is_active == True).count()
        vip_customers = self.db.query(Customer).filter(Customer.is_vip == True).count()
        
        # 本月新增客户
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        new_customers_this_month = self.db.query(Customer).filter(
            Customer.created_at >= current_month_start
        ).count()
        
        # 来源分布
        source_distribution = {}
        source_stats = self.db.query(
            Customer.source, func.count(Customer.id)
        ).group_by(Customer.source).all()
        for source, count in source_stats:
            source_distribution[source] = count
        
        # 满意度分布
        satisfaction_distribution = {}
        satisfaction_stats = self.db.query(
            Customer.satisfaction_rating, func.count(Customer.id)
        ).filter(Customer.satisfaction_rating.isnot(None)).group_by(
            Customer.satisfaction_rating
        ).all()
        for rating, count in satisfaction_stats:
            satisfaction_distribution[str(rating)] = count
        
        # 交易金额分布
        amount_distribution = {
            "0": 0,      # 无交易
            "1-1000": 0,     # 1-1000元
            "1001-5000": 0,  # 1001-5000元
            "5001-10000": 0, # 5001-10000元
            "10000+": 0      # 10000元以上
        }
        
        amount_stats = self.db.query(Customer.total_amount).all()
        for (amount,) in amount_stats:
            amount_val = float(amount)
            if amount_val == 0:
                amount_distribution["0"] += 1
            elif amount_val <= 1000:
                amount_distribution["1-1000"] += 1
            elif amount_val <= 5000:
                amount_distribution["1001-5000"] += 1
            elif amount_val <= 10000:
                amount_distribution["5001-10000"] += 1
            else:
                amount_distribution["10000+"] += 1
        
        return CustomerStatistics(
            total_customers=total_customers,
            active_customers=active_customers,
            vip_customers=vip_customers,
            new_customers_this_month=new_customers_this_month,
            source_distribution=source_distribution,
            satisfaction_distribution=satisfaction_distribution,
            amount_distribution=amount_distribution
        )
    
    def get_customer_order_summary(self, customer_id: str) -> CustomerOrderSummary:
        """
        获取客户订单摘要
        
        Args:
            customer_id: 客户ID
            
        Returns:
            CustomerOrderSummary: 客户订单摘要
            
        Raises:
            CustomerNotFoundException: 客户不存在
        """
        customer = self.get_customer(customer_id)
        
        # 基础统计
        orders = self.db.query(Order).filter(Order.customer_id == customer_id).all()
        total_orders = len(orders)
        
        if total_orders == 0:
            return CustomerOrderSummary(
                customer_id=customer_id,
                total_orders=0,
                completed_orders=0,
                in_progress_orders=0,
                total_amount=Decimal('0.00'),
                average_order_amount=Decimal('0.00'),
                last_order_date=None,
                orders_by_status={},
                orders_by_category={}
            )
        
        # 按状态统计
        orders_by_status = {}
        completed_orders = 0
        in_progress_orders = 0
        
        for order in orders:
            status = order.status
            orders_by_status[status] = orders_by_status.get(status, 0) + 1
            
            if status == "completed":
                completed_orders += 1
            elif status in ["pending", "in_progress", "revising", "reviewing"]:
                in_progress_orders += 1
        
        # 按分类统计
        orders_by_category = {}
        for order in orders:
            if order.category:
                category_name = order.category.name
                orders_by_category[category_name] = orders_by_category.get(category_name, 0) + 1
        
        # 金额统计
        total_amount = sum(order.actual_amount or Decimal('0.00') for order in orders)
        average_order_amount = total_amount / total_orders if total_orders > 0 else Decimal('0.00')
        
        # 最后订单日期
        last_order_date = max(order.created_at.date() for order in orders) if orders else None
        
        return CustomerOrderSummary(
            customer_id=customer_id,
            total_orders=total_orders,
            completed_orders=completed_orders,
            in_progress_orders=in_progress_orders,
            total_amount=total_amount,
            average_order_amount=average_order_amount,
            last_order_date=last_order_date,
            orders_by_status=orders_by_status,
            orders_by_category=orders_by_category
        )
    
    def update_customer_statistics(self, customer_id: str) -> Customer:
        """
        更新客户统计信息
        
        Args:
            customer_id: 客户ID
            
        Returns:
            Customer: 更新后的客户对象
        """
        customer = self.get_customer(customer_id)
        customer.update_statistics()
        self.db.commit()
        self.db.refresh(customer)
        return customer
    
    def search_customers(self, keyword: str, limit: int = 10) -> List[Customer]:
        """
        搜索客户
        
        Args:
            keyword: 搜索关键词
            limit: 返回数量限制
            
        Returns:
            List[Customer]: 匹配的客户列表
        """
        if not keyword:
            return []
        
        query = self.db.query(Customer).filter(
            or_(
                Customer.name.contains(keyword),
                Customer.company.contains(keyword),
                Customer.contact_email.contains(keyword),
                Customer.contact_phone.contains(keyword)
            )
        ).order_by(desc(Customer.total_orders), desc(Customer.created_at))
        
        return query.limit(limit).all()
    
    def get_vip_customers(self) -> List[Customer]:
        """
        获取VIP客户列表
        
        Returns:
            List[Customer]: VIP客户列表
        """
        return self.db.query(Customer).filter(
            Customer.is_vip == True
        ).order_by(desc(Customer.total_amount)).all()
    
    def batch_update_customers(self, customer_ids: List[str], update_data: CustomerUpdate) -> List[Customer]:
        """
        批量更新客户
        
        Args:
            customer_ids: 客户ID列表
            update_data: 更新数据
            
        Returns:
            List[Customer]: 更新后的客户列表
        """
        customers = []
        for customer_id in customer_ids:
            try:
                customer = self.update_customer(customer_id, update_data)
                customers.append(customer)
            except CustomerNotFoundException:
                # 跳过不存在的客户
                continue
        
        return customers
