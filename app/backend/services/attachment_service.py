"""
文件附件管理业务逻辑服务

提供文件附件相关的业务逻辑处理
包含文件上传、下载、版本控制、预览等功能
"""

import os
import shutil
import mimetypes
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from pathlib import Path
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import UploadFile

from app.models.attachment import Attachment
from app.models.order import Order
from app.backend.schemas.attachment import (
    AttachmentCreate, AttachmentUpdate, AttachmentListParams,
    FileUploadRequest, FileVersionHistory, AttachmentStatistics,
    FileStorageInfo
)
from app.core.config import Settings, get_settings
from app.core.exceptions import (
    AttachmentNotFoundException, OrderNotFoundException,
    BusinessException, ValidationException
)

class AttachmentService:
    """
    文件附件管理服务类
    
    提供文件附件相关的所有业务逻辑操作
    """
    
    def __init__(self, db: Session, settings: Settings = None):
        self.db = db
        self.settings = settings or get_settings()
        self.upload_dir = Path(self.settings.upload_dir)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
    
    def upload_file(self, file: UploadFile, upload_request: FileUploadRequest) -> Attachment:
        """
        上传文件
        
        Args:
            file: 上传的文件对象
            upload_request: 上传请求参数
            
        Returns:
            Attachment: 创建的文件附件对象
            
        Raises:
            OrderNotFoundException: 订单不存在
            ValidationException: 文件验证失败
            BusinessException: 文件上传失败
        """
        # 验证订单是否存在
        order = self.db.query(Order).filter(Order.id == upload_request.order_id).first()
        if not order:
            raise OrderNotFoundException(upload_request.order_id)
        
        # 验证文件
        self._validate_file(file)
        
        # 检查是否需要替换同名文件
        if upload_request.replace_existing:
            existing_file = self.db.query(Attachment).filter(
                and_(
                    Attachment.order_id == upload_request.order_id,
                    Attachment.original_name == file.filename,
                    Attachment.is_active == True
                )
            ).first()
            
            if existing_file:
                # 创建新版本
                return self._create_new_version(existing_file, file, upload_request)
        
        # 生成文件存储信息
        file_info = self._generate_file_info(file, upload_request.order_id)
        
        # 保存文件到磁盘
        file_path = self._save_file_to_disk(file, file_info['file_name'])
        
        # 创建数据库记录
        attachment_data = AttachmentCreate(
            order_id=upload_request.order_id,
            original_name=file.filename,
            category=upload_request.category,
            description=upload_request.description,
            tags=upload_request.tags,
            is_deliverable=upload_request.is_deliverable
        )
        
        attachment = Attachment(
            **attachment_data.model_dump(),
            file_name=file_info['file_name'],
            file_path=file_info['relative_path'],
            file_type=file_info['file_type'],
            file_size=file_info['file_size'],
            mime_type=file_info['mime_type']
        )
        
        self.db.add(attachment)
        self.db.commit()
        self.db.refresh(attachment)
        
        return attachment
    
    def get_attachment(self, attachment_id: str) -> Attachment:
        """
        根据ID获取文件附件
        
        Args:
            attachment_id: 文件附件ID
            
        Returns:
            Attachment: 文件附件对象
            
        Raises:
            AttachmentNotFoundException: 文件附件不存在
        """
        attachment = self.db.query(Attachment).options(
            joinedload(Attachment.order)
        ).filter(Attachment.id == attachment_id).first()
        
        if not attachment:
            raise AttachmentNotFoundException(attachment_id)
        return attachment
    
    def update_attachment(self, attachment_id: str, attachment_data: AttachmentUpdate) -> Attachment:
        """
        更新文件附件信息
        
        Args:
            attachment_id: 文件附件ID
            attachment_data: 更新数据
            
        Returns:
            Attachment: 更新后的文件附件对象
            
        Raises:
            AttachmentNotFoundException: 文件附件不存在
        """
        attachment = self.get_attachment(attachment_id)
        
        # 更新字段
        update_data = attachment_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(attachment, field):
                setattr(attachment, field, value)
        
        self.db.commit()
        self.db.refresh(attachment)
        
        return attachment
    
    def delete_attachment(self, attachment_id: str, force: bool = False) -> bool:
        """
        删除文件附件
        
        Args:
            attachment_id: 文件附件ID
            force: 是否强制删除（删除磁盘文件）
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            AttachmentNotFoundException: 文件附件不存在
        """
        attachment = self.get_attachment(attachment_id)
        
        # 如果强制删除，删除磁盘文件
        if force:
            try:
                file_path = Path(attachment.full_path)
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                # 记录错误但不阻止删除数据库记录
                pass
        else:
            # 软删除，只标记为无效
            attachment.is_active = False
            self.db.commit()
            return True
        
        self.db.delete(attachment)
        self.db.commit()
        
        return True
    
    def list_attachments(self, params: AttachmentListParams) -> Tuple[List[Attachment], int]:
        """
        获取文件附件列表
        
        Args:
            params: 查询参数
            
        Returns:
            Tuple[List[Attachment], int]: 文件附件列表和总数
        """
        query = self.db.query(Attachment).options(
            joinedload(Attachment.order)
        )
        
        # 应用筛选条件
        if params.keyword:
            keyword_filter = or_(
                Attachment.original_name.contains(params.keyword),
                Attachment.description.contains(params.keyword),
                Attachment.tags.contains(params.keyword)
            )
            query = query.filter(keyword_filter)
        
        if params.order_id:
            query = query.filter(Attachment.order_id == params.order_id)
        
        if params.category:
            query = query.filter(Attachment.category == params.category)
        
        if params.file_type:
            query = query.filter(Attachment.file_type == params.file_type)
        
        if params.is_deliverable is not None:
            query = query.filter(Attachment.is_deliverable == params.is_deliverable)
        
        if params.is_active is not None:
            query = query.filter(Attachment.is_active == params.is_active)
        
        # 文件大小筛选
        if params.min_size is not None:
            query = query.filter(Attachment.file_size >= params.min_size)
        
        if params.max_size is not None:
            query = query.filter(Attachment.file_size <= params.max_size)
        
        # 时间范围筛选
        if params.upload_date_from:
            query = query.filter(Attachment.upload_time >= params.upload_date_from)
        
        if params.upload_date_to:
            query = query.filter(Attachment.upload_time <= params.upload_date_to)
        
        # 文件扩展名筛选
        if params.file_extensions:
            extensions = [ext.strip() for ext in params.file_extensions.split(',')]
            extension_filters = [Attachment.file_type.endswith(ext) for ext in extensions]
            query = query.filter(or_(*extension_filters))
        
        # 是否包含历史版本
        if not params.include_versions:
            # 只显示最新版本（version=1或parent_id为空的文件）
            query = query.filter(
                or_(
                    Attachment.parent_id.is_(None),
                    and_(
                        Attachment.parent_id.isnot(None),
                        ~self.db.query(Attachment).filter(
                            and_(
                                Attachment.parent_id == Attachment.parent_id,
                                Attachment.version > Attachment.version
                            )
                        ).exists()
                    )
                )
            )
        
        # 获取总数
        total = query.count()
        
        # 应用排序
        if params.sort_by:
            sort_column = getattr(Attachment, params.sort_by, None)
            if sort_column:
                if params.sort_order == "asc":
                    query = query.order_by(asc(sort_column))
                else:
                    query = query.order_by(desc(sort_column))
        else:
            # 默认按上传时间倒序
            query = query.order_by(desc(Attachment.upload_time))
        
        # 应用分页
        attachments = query.offset(params.offset).limit(params.limit).all()
        
        return attachments, total
    
    def get_file_version_history(self, attachment_id: str) -> FileVersionHistory:
        """
        获取文件版本历史
        
        Args:
            attachment_id: 文件附件ID
            
        Returns:
            FileVersionHistory: 文件版本历史
        """
        attachment = self.get_attachment(attachment_id)
        
        # 获取所有版本（包括自己）
        if attachment.parent_id:
            # 如果是子版本，获取主文件的所有版本
            main_file_id = attachment.parent_id
            versions = self.db.query(Attachment).filter(
                or_(
                    Attachment.id == main_file_id,
                    Attachment.parent_id == main_file_id
                )
            ).order_by(Attachment.version).all()
        else:
            # 如果是主文件，获取所有子版本
            versions = self.db.query(Attachment).filter(
                or_(
                    Attachment.id == attachment_id,
                    Attachment.parent_id == attachment_id
                )
            ).order_by(Attachment.version).all()
        
        # 找到当前版本
        current_version = max(v.version for v in versions)
        
        # 构建版本信息
        version_infos = []
        for version in versions:
            version_infos.append({
                'version': version.version,
                'file_id': version.id,
                'file_size': version.file_size,
                'upload_time': version.upload_time,
                'description': version.description,
                'is_current': version.version == current_version
            })
        
        return FileVersionHistory(
            file_id=attachment.id,
            original_name=attachment.original_name,
            current_version=current_version,
            total_versions=len(versions),
            versions=version_infos
        )
    
    def get_attachment_statistics(self, order_id: str = None) -> AttachmentStatistics:
        """
        获取文件附件统计信息
        
        Args:
            order_id: 订单ID筛选（可选）
            
        Returns:
            AttachmentStatistics: 文件附件统计数据
        """
        query = self.db.query(Attachment).filter(Attachment.is_active == True)
        
        if order_id:
            query = query.filter(Attachment.order_id == order_id)
        
        attachments = query.all()
        
        # 基础统计
        total_files = len(attachments)
        total_size = sum(att.file_size for att in attachments)
        
        # 分类统计
        category_distribution = {}
        file_type_distribution = {}
        
        for att in attachments:
            # 分类分布
            category_distribution[att.category] = category_distribution.get(att.category, 0) + 1
            
            # 文件类型分布
            file_type_distribution[att.file_type] = file_type_distribution.get(att.file_type, 0) + 1
        
        # 大小统计
        average_file_size = total_size / total_files if total_files > 0 else 0
        largest_file_size = max(att.file_size for att in attachments) if attachments else 0
        
        # 上传趋势（简化版）
        upload_trend = []
        today = datetime.now().date()
        recent_uploads = len([att for att in attachments if att.upload_time.date() == today])
        
        # 版本统计
        files_with_versions = len([att for att in attachments if att.version > 1 or 
                                  self.db.query(Attachment).filter(Attachment.parent_id == att.id).count() > 0])
        total_versions = sum(att.version for att in attachments)
        
        # 交付物统计
        deliverable_files = len([att for att in attachments if att.is_deliverable])
        deliverable_size = sum(att.file_size for att in attachments if att.is_deliverable)
        
        return AttachmentStatistics(
            total_files=total_files,
            total_size=total_size,
            total_size_display=self._format_file_size(total_size),
            category_distribution=category_distribution,
            file_type_distribution=file_type_distribution,
            average_file_size=average_file_size,
            largest_file_size=largest_file_size,
            upload_trend=upload_trend,
            recent_uploads=recent_uploads,
            files_with_versions=files_with_versions,
            total_versions=total_versions,
            deliverable_files=deliverable_files,
            deliverable_size=deliverable_size
        )

    def get_storage_info(self) -> FileStorageInfo:
        """
        获取文件存储信息

        Returns:
            FileStorageInfo: 文件存储信息
        """
        # 计算已使用存储空间
        total_storage_used = self.db.query(func.sum(Attachment.file_size)).filter(
            Attachment.is_active == True
        ).scalar() or 0

        # 存储限制（从配置获取，这里简化为1GB）
        total_storage_limit = 1024 * 1024 * 1024  # 1GB
        storage_usage_percentage = (total_storage_used / total_storage_limit * 100) if total_storage_limit > 0 else 0

        # 文件总数
        files_count = self.db.query(Attachment).filter(Attachment.is_active == True).count()

        # 按分类的存储分布
        storage_by_category = {}
        category_stats = self.db.query(
            Attachment.category, func.sum(Attachment.file_size)
        ).filter(Attachment.is_active == True).group_by(Attachment.category).all()

        for category, size in category_stats:
            storage_by_category[category] = size or 0

        # 按类型的存储分布
        storage_by_type = {}
        type_stats = self.db.query(
            Attachment.file_type, func.sum(Attachment.file_size)
        ).filter(Attachment.is_active == True).group_by(Attachment.file_type).all()

        for file_type, size in type_stats:
            storage_by_type[file_type] = size or 0

        # 清理建议
        cleanup_suggestions = []
        if storage_usage_percentage > 80:
            cleanup_suggestions.append("存储空间使用率超过80%，建议清理不必要的文件")

        # 大文件列表（超过10MB）
        large_files = []
        large_file_threshold = 10 * 1024 * 1024  # 10MB
        large_attachments = self.db.query(Attachment).filter(
            and_(
                Attachment.is_active == True,
                Attachment.file_size > large_file_threshold
            )
        ).order_by(desc(Attachment.file_size)).limit(10).all()

        for att in large_attachments:
            large_files.append({
                'file_id': att.id,
                'original_name': att.original_name,
                'file_size': att.file_size,
                'file_size_display': self._format_file_size(att.file_size),
                'upload_time': att.upload_time
            })

        # 旧文件列表（超过1年）
        old_files = []
        from datetime import timedelta
        one_year_ago = datetime.now() - timedelta(days=365)
        old_attachments = self.db.query(Attachment).filter(
            and_(
                Attachment.is_active == True,
                Attachment.upload_time < one_year_ago
            )
        ).order_by(Attachment.upload_time).limit(10).all()

        for att in old_attachments:
            old_files.append({
                'file_id': att.id,
                'original_name': att.original_name,
                'file_size': att.file_size,
                'upload_time': att.upload_time
            })

        if len(old_files) > 0:
            cleanup_suggestions.append(f"发现{len(old_attachments)}个超过1年的旧文件，建议检查是否需要归档")

        return FileStorageInfo(
            total_storage_used=total_storage_used,
            total_storage_limit=total_storage_limit,
            storage_usage_percentage=storage_usage_percentage,
            files_count=files_count,
            storage_by_category=storage_by_category,
            storage_by_type=storage_by_type,
            cleanup_suggestions=cleanup_suggestions,
            large_files=large_files,
            old_files=old_files
        )

    def get_file_download_path(self, attachment_id: str) -> str:
        """
        获取文件下载路径

        Args:
            attachment_id: 文件附件ID

        Returns:
            str: 文件完整路径

        Raises:
            AttachmentNotFoundException: 文件附件不存在
            BusinessException: 文件不存在
        """
        attachment = self.get_attachment(attachment_id)

        file_path = Path(attachment.full_path)
        if not file_path.exists():
            raise BusinessException(f"文件不存在: {attachment.original_name}")

        return str(file_path)

    def can_preview_file(self, attachment: Attachment) -> bool:
        """
        检查文件是否可预览

        Args:
            attachment: 文件附件对象

        Returns:
            bool: 是否可预览
        """
        # 可预览的文件类型
        previewable_types = {
            # 图片
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg',
            # 文档
            '.txt', '.md', '.json', '.xml', '.csv',
            # 代码
            '.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.h'
        }

        return attachment.file_extension.lower() in previewable_types

    def get_file_preview_content(self, attachment_id: str, max_size: int = 1024 * 1024) -> str:
        """
        获取文件预览内容

        Args:
            attachment_id: 文件附件ID
            max_size: 最大预览文件大小

        Returns:
            str: 文件预览内容

        Raises:
            AttachmentNotFoundException: 文件附件不存在
            BusinessException: 文件无法预览
        """
        attachment = self.get_attachment(attachment_id)

        if not self.can_preview_file(attachment):
            raise BusinessException("该文件类型不支持预览")

        if attachment.file_size > max_size:
            raise BusinessException("文件过大，无法预览")

        file_path = Path(attachment.full_path)
        if not file_path.exists():
            raise BusinessException("文件不存在")

        try:
            # 对于文本文件，读取内容
            if attachment.file_extension.lower() in ['.txt', '.md', '.json', '.xml', '.csv', '.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.h']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 限制预览长度
                    if len(content) > 10000:
                        content = content[:10000] + "\n\n... (内容已截断)"
                    return content
            else:
                return "该文件类型暂不支持内容预览"
        except Exception as e:
            raise BusinessException(f"读取文件失败: {str(e)}")

    def _validate_file(self, file: UploadFile) -> None:
        """
        验证上传文件

        Args:
            file: 上传的文件对象

        Raises:
            ValidationException: 文件验证失败
        """
        # 检查文件名
        if not file.filename:
            raise ValidationException("文件名不能为空")

        # 检查文件大小
        if hasattr(file, 'size') and file.size > self.settings.max_file_size:
            max_size_mb = self.settings.max_file_size / (1024 * 1024)
            raise ValidationException(f"文件大小超过限制 ({max_size_mb:.1f}MB)")

        # 检查文件类型
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in self.settings.allowed_file_types:
            allowed_types = ", ".join(self.settings.allowed_file_types)
            raise ValidationException(f"不支持的文件类型。支持的类型: {allowed_types}")

    def _generate_file_info(self, file: UploadFile, order_id: str) -> Dict[str, Any]:
        """
        生成文件存储信息

        Args:
            file: 上传的文件对象
            order_id: 订单ID

        Returns:
            Dict[str, Any]: 文件信息
        """
        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_extension = Path(file.filename).suffix.lower()
        file_name = f"{order_id}_{timestamp}_{file.filename}"

        # 计算相对路径
        year_month = datetime.now().strftime("%Y/%m")
        relative_path = f"{year_month}/{file_name}"

        # 获取文件大小
        file_size = 0
        if hasattr(file, 'size'):
            file_size = file.size
        else:
            # 如果没有size属性，读取文件内容获取大小
            content = file.file.read()
            file_size = len(content)
            file.file.seek(0)  # 重置文件指针

        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(file.filename)

        return {
            'file_name': file_name,
            'relative_path': relative_path,
            'file_type': file_extension,
            'file_size': file_size,
            'mime_type': mime_type
        }

    def _save_file_to_disk(self, file: UploadFile, file_name: str) -> str:
        """
        保存文件到磁盘

        Args:
            file: 上传的文件对象
            file_name: 文件名

        Returns:
            str: 文件完整路径

        Raises:
            BusinessException: 文件保存失败
        """
        try:
            # 创建年月目录
            year_month = datetime.now().strftime("%Y/%m")
            target_dir = self.upload_dir / year_month
            target_dir.mkdir(parents=True, exist_ok=True)

            # 完整文件路径
            file_path = target_dir / file_name

            # 保存文件
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            return str(file_path)
        except Exception as e:
            raise BusinessException(f"文件保存失败: {str(e)}")

    def _create_new_version(self, existing_file: Attachment, file: UploadFile,
                           upload_request: FileUploadRequest) -> Attachment:
        """
        创建文件新版本

        Args:
            existing_file: 现有文件
            file: 新文件对象
            upload_request: 上传请求

        Returns:
            Attachment: 新版本文件附件对象
        """
        # 生成新文件信息
        file_info = self._generate_file_info(file, upload_request.order_id)

        # 保存新文件到磁盘
        file_path = self._save_file_to_disk(file, file_info['file_name'])

        # 创建新版本记录
        new_version = existing_file.create_new_version(
            new_file_path=file_info['relative_path'],
            new_file_name=file_info['file_name'],
            new_file_size=file_info['file_size'],
            description=upload_request.description
        )

        # 更新其他属性
        new_version.mime_type = file_info['mime_type']
        new_version.category = upload_request.category
        new_version.tags = upload_request.tags
        new_version.is_deliverable = upload_request.is_deliverable

        self.db.add(new_version)
        self.db.commit()
        self.db.refresh(new_version)

        return new_version

    def _format_file_size(self, size_bytes: int) -> str:
        """
        格式化文件大小显示

        Args:
            size_bytes: 文件大小（字节）

        Returns:
            str: 格式化后的文件大小
        """
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        return f"{s} {size_names[i]}"
