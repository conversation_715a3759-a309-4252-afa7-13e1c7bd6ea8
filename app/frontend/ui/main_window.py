"""
主窗口界面

兼职接单管理系统的主窗口
提供标签页导航和整体界面框架
"""

import sys
from PyQt6.QtWidgets import (
    QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout, QWidget,
    QMenuBar, QStatusBar, QToolBar, QLabel, QPushButton, QMessageBox,
    QSystemTrayIcon, QMenu, QApplication
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QAction, QIcon, QPixmap
import logging

from app.frontend.controllers.api_client import APIClient

class MainWindow(QMainWindow):
    """
    主窗口类
    
    应用程序的主界面，包含菜单栏、工具栏、标签页和状态栏
    """
    
    # 信号定义
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        # 初始化API客户端
        self.api_client = APIClient()
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化界面
        self.init_ui()
        
        # 初始化系统托盘
        self.init_system_tray()
        
        # 初始化定时器
        self.init_timers()
        
        # 连接信号
        self.connect_signals()
        
        # 检查后端连接
        self.check_backend_connection()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("兼职接单管理系统")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建标签页组件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(True)
        main_layout.addWidget(self.tab_widget)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建标签页
        self.create_tabs()
        
        # 应用样式
        self.apply_theme("light")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 新建订单
        new_order_action = QAction('新建订单(&N)', self)
        new_order_action.setShortcut('Ctrl+N')
        new_order_action.triggered.connect(self.new_order)
        file_menu.addAction(new_order_action)
        
        file_menu.addSeparator()
        
        # 导入数据
        import_action = QAction('导入数据(&I)', self)
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        # 导出数据
        export_action = QAction('导出数据(&E)', self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')
        
        # 查找
        find_action = QAction('查找(&F)', self)
        find_action.setShortcut('Ctrl+F')
        find_action.triggered.connect(self.show_search)
        edit_menu.addAction(find_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        # 主题切换
        theme_menu = view_menu.addMenu('主题(&T)')
        
        light_theme_action = QAction('浅色主题', self)
        light_theme_action.triggered.connect(lambda: self.apply_theme("light"))
        theme_menu.addAction(light_theme_action)
        
        dark_theme_action = QAction('深色主题', self)
        dark_theme_action.triggered.connect(lambda: self.apply_theme("dark"))
        theme_menu.addAction(dark_theme_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')
        
        # 系统设置
        settings_action = QAction('系统设置(&S)', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 数据备份
        backup_action = QAction('数据备份(&B)', self)
        backup_action.triggered.connect(self.backup_data)
        tools_menu.addAction(backup_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 新建订单按钮
        new_order_btn = QPushButton("新建订单")
        new_order_btn.clicked.connect(self.new_order)
        toolbar.addWidget(new_order_btn)
        
        toolbar.addSeparator()
        
        # 开始计时按钮
        self.start_timer_btn = QPushButton("开始计时")
        self.start_timer_btn.clicked.connect(self.start_timer)
        toolbar.addWidget(self.start_timer_btn)
        
        # 停止计时按钮
        self.stop_timer_btn = QPushButton("停止计时")
        self.stop_timer_btn.clicked.connect(self.stop_timer)
        self.stop_timer_btn.setEnabled(False)
        toolbar.addWidget(self.stop_timer_btn)
        
        toolbar.addSeparator()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar.addWidget(refresh_btn)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 连接状态标签
        self.connection_label = QLabel("正在连接后端服务...")
        self.status_bar.addWidget(self.connection_label)
        
        # 计时器状态标签
        self.timer_label = QLabel("计时器: 未运行")
        self.status_bar.addPermanentWidget(self.timer_label)
        
        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
    
    def create_tabs(self):
        """创建标签页"""
        # 订单管理标签页
        from app.frontend.ui.order_list import OrderListWidget
        self.order_list_widget = OrderListWidget(self.api_client)
        self.tab_widget.addTab(self.order_list_widget, "订单管理")
        
        # 客户管理标签页
        from app.frontend.ui.customer_manager import CustomerManagerWidget
        self.customer_manager_widget = CustomerManagerWidget(self.api_client)
        self.tab_widget.addTab(self.customer_manager_widget, "客户管理")
        
        # 工时记录标签页
        from app.frontend.ui.work_log_view import WorkLogViewWidget
        self.work_log_widget = WorkLogViewWidget(self.api_client)
        self.tab_widget.addTab(self.work_log_widget, "工时记录")
        
        # 统计分析标签页
        from app.frontend.ui.analytics_view import AnalyticsViewWidget
        self.analytics_widget = AnalyticsViewWidget(self.api_client)
        self.tab_widget.addTab(self.analytics_widget, "统计分析")
        
        # 文件管理标签页
        from app.frontend.ui.file_manager import FileManagerWidget
        self.file_manager_widget = FileManagerWidget(self.api_client)
        self.tab_widget.addTab(self.file_manager_widget, "文件管理")
        
        # 系统设置标签页
        from app.frontend.ui.settings_view import SettingsViewWidget
        self.settings_widget = SettingsViewWidget(self.api_client)
        self.tab_widget.addTab(self.settings_widget, "系统设置")
    
    def init_system_tray(self):
        """初始化系统托盘"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            
            # 创建托盘菜单
            tray_menu = QMenu()
            
            show_action = QAction("显示主窗口", self)
            show_action.triggered.connect(self.show)
            tray_menu.addAction(show_action)
            
            tray_menu.addSeparator()
            
            quit_action = QAction("退出", self)
            quit_action.triggered.connect(QApplication.quit)
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.activated.connect(self.tray_icon_activated)
            
            # 设置托盘图标（这里使用默认图标）
            self.tray_icon.setIcon(self.style().standardIcon(self.style().StandardPixmap.SP_ComputerIcon))
            self.tray_icon.show()
    
    def init_timers(self):
        """初始化定时器"""
        # 时间更新定时器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # 每秒更新
        
        # 计时器状态检查定时器
        self.timer_check_timer = QTimer()
        self.timer_check_timer.timeout.connect(self.check_timer_status)
        self.timer_check_timer.start(5000)  # 每5秒检查
    
    def connect_signals(self):
        """连接信号"""
        self.api_client.error_occurred.connect(self.show_error_message)
        self.theme_changed.connect(self.on_theme_changed)

    def check_backend_connection(self):
        """检查后端连接"""
        try:
            if self.api_client.test_connection():
                self.connection_label.setText("后端服务连接正常")
                self.connection_label.setStyleSheet("color: green;")
            else:
                self.connection_label.setText("后端服务连接失败")
                self.connection_label.setStyleSheet("color: red;")
        except Exception as e:
            self.connection_label.setText(f"连接错误: {str(e)}")
            self.connection_label.setStyleSheet("color: red;")

    def update_time(self):
        """更新时间显示"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def check_timer_status(self):
        """检查计时器状态"""
        try:
            response = self.api_client.get_timer_status()
            if response.get('success'):
                timer_data = response.get('data', {})
                is_running = timer_data.get('is_running', False)

                if is_running:
                    order_title = timer_data.get('current_order_title', '未知订单')
                    elapsed_time = timer_data.get('elapsed_time', '00:00:00')
                    self.timer_label.setText(f"计时中: {order_title} ({elapsed_time})")
                    self.start_timer_btn.setEnabled(False)
                    self.stop_timer_btn.setEnabled(True)
                else:
                    self.timer_label.setText("计时器: 未运行")
                    self.start_timer_btn.setEnabled(True)
                    self.stop_timer_btn.setEnabled(False)
        except Exception as e:
            self.logger.error(f"检查计时器状态失败: {e}")

    def apply_theme(self, theme_name: str):
        """应用主题"""
        try:
            if theme_name == "dark":
                self.setStyleSheet(self.get_dark_theme_style())
            else:
                self.setStyleSheet(self.get_light_theme_style())

            self.theme_changed.emit(theme_name)
        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")

    def get_light_theme_style(self) -> str:
        """获取浅色主题样式"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
            color: #333333;
        }

        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #e0e0e0;
            color: #333333;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #007acc;
        }

        QPushButton {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #005a9e;
        }

        QPushButton:pressed {
            background-color: #004578;
        }

        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }

        QStatusBar {
            background-color: #e0e0e0;
            border-top: 1px solid #cccccc;
        }

        QMenuBar {
            background-color: #f0f0f0;
            border-bottom: 1px solid #cccccc;
        }

        QMenuBar::item:selected {
            background-color: #007acc;
            color: white;
        }

        QToolBar {
            background-color: #f0f0f0;
            border-bottom: 1px solid #cccccc;
            spacing: 4px;
        }
        """

    def get_dark_theme_style(self) -> str:
        """获取深色主题样式"""
        return """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }

        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #3c3c3c;
        }

        QTabBar::tab {
            background-color: #404040;
            color: #ffffff;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: #3c3c3c;
            border-bottom: 2px solid #0078d4;
        }

        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #106ebe;
        }

        QPushButton:pressed {
            background-color: #005a9e;
        }

        QPushButton:disabled {
            background-color: #555555;
            color: #888888;
        }

        QStatusBar {
            background-color: #404040;
            border-top: 1px solid #555555;
            color: #ffffff;
        }

        QMenuBar {
            background-color: #404040;
            border-bottom: 1px solid #555555;
            color: #ffffff;
        }

        QMenuBar::item:selected {
            background-color: #0078d4;
            color: white;
        }

        QToolBar {
            background-color: #404040;
            border-bottom: 1px solid #555555;
            spacing: 4px;
        }
        """

    # 菜单和工具栏事件处理
    def new_order(self):
        """新建订单"""
        self.tab_widget.setCurrentIndex(0)  # 切换到订单管理标签页
        if hasattr(self.order_list_widget, 'show_new_order_dialog'):
            self.order_list_widget.show_new_order_dialog()

    def start_timer(self):
        """开始计时"""
        # 这里可以弹出对话框选择订单，暂时使用默认逻辑
        try:
            response = self.api_client.timer_operation('start')
            if response.get('success'):
                self.show_info_message("计时器已启动")
            else:
                self.show_error_message("启动计时器失败")
        except Exception as e:
            self.show_error_message(f"启动计时器失败: {str(e)}")

    def stop_timer(self):
        """停止计时"""
        try:
            response = self.api_client.timer_operation('stop')
            if response.get('success'):
                self.show_info_message("计时器已停止")
            else:
                self.show_error_message("停止计时器失败")
        except Exception as e:
            self.show_error_message(f"停止计时器失败: {str(e)}")

    def refresh_data(self):
        """刷新数据"""
        # 刷新所有标签页的数据
        for i in range(self.tab_widget.count()):
            widget = self.tab_widget.widget(i)
            if hasattr(widget, 'refresh_data'):
                widget.refresh_data()

        self.show_info_message("数据已刷新")

    def import_data(self):
        """导入数据"""
        self.show_info_message("导入功能开发中...")

    def export_data(self):
        """导出数据"""
        self.show_info_message("导出功能开发中...")

    def show_search(self):
        """显示搜索"""
        self.show_info_message("搜索功能开发中...")

    def show_settings(self):
        """显示设置"""
        self.tab_widget.setCurrentIndex(5)  # 切换到系统设置标签页

    def backup_data(self):
        """备份数据"""
        self.show_info_message("备份功能开发中...")

    def show_about(self):
        """显示关于"""
        QMessageBox.about(self, "关于",
                         "兼职接单管理系统 v1.0.0\n\n"
                         "专为程序员兼职接单设计的全流程管理工具\n"
                         "支持订单管理、客户维护、收入统计、工时跟踪等功能")

    # 事件处理
    def tray_icon_activated(self, reason):
        """系统托盘图标激活"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()

    def on_theme_changed(self, theme_name: str):
        """主题变更事件"""
        # 通知所有子组件主题已变更
        for i in range(self.tab_widget.count()):
            widget = self.tab_widget.widget(i)
            if hasattr(widget, 'on_theme_changed'):
                widget.on_theme_changed(theme_name)

    def show_error_message(self, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, "错误", message)

    def show_info_message(self, message: str):
        """显示信息消息"""
        QMessageBox.information(self, "信息", message)

    def closeEvent(self, event):
        """关闭事件"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            # 最小化到系统托盘
            self.hide()
            self.tray_icon.showMessage(
                "兼职接单管理系统",
                "应用程序已最小化到系统托盘",
                QSystemTrayIcon.MessageIcon.Information,
                2000
            )
            event.ignore()
        else:
            event.accept()
