"""
客户管理界面

显示和管理客户信息
提供客户的增删改查功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QHeaderView, QMessageBox,
    QDialog, QFormLayout, QTextEdit, QComboBox, QDialogButtonBox,
    QAbstractItemView, QMenu
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QAction
import logging

class CustomerManagerWidget(QWidget):
    """
    客户管理组件
    
    显示客户列表并提供管理功能
    """
    
    # 信号定义
    customer_selected = pyqtSignal(str)  # 客户选中信号
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        self.customers_data = []
        
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建工具栏
        toolbar_layout = QHBoxLayout()
        
        # 新建客户按钮
        self.new_customer_btn = QPushButton("新建客户")
        self.new_customer_btn.clicked.connect(self.show_new_customer_dialog)
        toolbar_layout.addWidget(self.new_customer_btn)
        
        # 编辑客户按钮
        self.edit_customer_btn = QPushButton("编辑客户")
        self.edit_customer_btn.clicked.connect(self.edit_selected_customer)
        self.edit_customer_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_customer_btn)
        
        # 删除客户按钮
        self.delete_customer_btn = QPushButton("删除客户")
        self.delete_customer_btn.clicked.connect(self.delete_selected_customer)
        self.delete_customer_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_customer_btn)
        
        toolbar_layout.addStretch()
        
        # 搜索框
        toolbar_layout.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入客户名称、邮箱或电话...")
        self.search_input.textChanged.connect(self.filter_customers)
        toolbar_layout.addWidget(self.search_input)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 创建客户表格
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(6)
        self.customers_table.setHorizontalHeaderLabels([
            "客户名称", "邮箱", "电话", "公司", "订单数量", "创建时间"
        ])
        
        # 设置表格属性
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSortingEnabled(True)
        
        # 设置列宽
        header = self.customers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 客户名称
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 邮箱
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 电话
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 公司
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 订单数量
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 创建时间
        
        # 连接表格信号
        self.customers_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.customers_table.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        # 设置右键菜单
        self.customers_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customers_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.customers_table)
        
        # 创建状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        self.total_label = QLabel("总计: 0 个客户")
        status_layout.addWidget(self.total_label)
        
        layout.addLayout(status_layout)
    
    def load_data(self):
        """加载数据"""
        try:
            self.status_label.setText("正在加载客户数据...")
            
            # 加载客户数据
            response = self.api_client.get_customers(page_size=1000)
            if response.get('success'):
                self.customers_data = response.get('data', [])
                self.populate_table()
                self.status_label.setText("数据加载完成")
            else:
                self.status_label.setText("加载客户数据失败")
                
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            self.status_label.setText(f"加载失败: {str(e)}")
    
    def populate_table(self):
        """填充表格数据"""
        filtered_customers = self.get_filtered_customers()
        
        self.customers_table.setRowCount(len(filtered_customers))
        
        for row, customer in enumerate(filtered_customers):
            # 客户名称
            name_item = QTableWidgetItem(customer.get('name', ''))
            name_item.setData(Qt.ItemDataRole.UserRole, customer.get('id'))  # 存储客户ID
            self.customers_table.setItem(row, 0, name_item)
            
            # 邮箱
            email_item = QTableWidgetItem(customer.get('email', ''))
            self.customers_table.setItem(row, 1, email_item)
            
            # 电话
            phone_item = QTableWidgetItem(customer.get('phone', ''))
            self.customers_table.setItem(row, 2, phone_item)
            
            # 公司
            company_item = QTableWidgetItem(customer.get('company', ''))
            self.customers_table.setItem(row, 3, company_item)
            
            # 订单数量
            order_count = customer.get('order_count', 0)
            count_item = QTableWidgetItem(str(order_count))
            count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.customers_table.setItem(row, 4, count_item)
            
            # 创建时间
            created_at = customer.get('created_at')
            if created_at:
                from datetime import datetime
                created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                created_item = QTableWidgetItem(created_date.strftime('%Y-%m-%d %H:%M'))
            else:
                created_item = QTableWidgetItem("")
            
            self.customers_table.setItem(row, 5, created_item)
        
        # 更新统计信息
        self.total_label.setText(f"总计: {len(filtered_customers)} 个客户")
    
    def get_filtered_customers(self):
        """获取筛选后的客户"""
        filtered = self.customers_data
        
        # 搜索筛选
        search_text = self.search_input.text().lower()
        if search_text:
            filtered = [
                customer for customer in filtered
                if search_text in customer.get('name', '').lower() or
                   search_text in customer.get('email', '').lower() or
                   search_text in customer.get('phone', '').lower() or
                   search_text in customer.get('company', '').lower()
            ]
        
        return filtered
    
    def filter_customers(self):
        """筛选客户"""
        self.populate_table()
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
    
    def on_selection_changed(self):
        """选择变更事件"""
        selected_items = self.customers_table.selectedItems()
        has_selection = len(selected_items) > 0
        
        self.edit_customer_btn.setEnabled(has_selection)
        self.delete_customer_btn.setEnabled(has_selection)
        
        if has_selection:
            row = selected_items[0].row()
            name_item = self.customers_table.item(row, 0)
            if name_item:
                customer_id = name_item.data(Qt.ItemDataRole.UserRole)
                self.customer_selected.emit(customer_id)
    
    def on_item_double_clicked(self, item):
        """双击事件"""
        row = item.row()
        name_item = self.customers_table.item(row, 0)
        if name_item:
            customer_id = name_item.data(Qt.ItemDataRole.UserRole)
            self.view_customer_detail(customer_id)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.customers_table.itemAt(position)
        if item is None:
            return
        
        menu = QMenu(self)
        
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(lambda: self.view_customer_detail(self.get_selected_customer_id()))
        menu.addAction(view_action)
        
        edit_action = QAction("编辑客户", self)
        edit_action.triggered.connect(self.edit_selected_customer)
        menu.addAction(edit_action)
        
        menu.addSeparator()
        
        delete_action = QAction("删除客户", self)
        delete_action.triggered.connect(self.delete_selected_customer)
        menu.addAction(delete_action)
        
        menu.exec(self.customers_table.mapToGlobal(position))
    
    def get_selected_customer_id(self):
        """获取选中的客户ID"""
        selected_items = self.customers_table.selectedItems()
        if selected_items:
            row = selected_items[0].row()
            name_item = self.customers_table.item(row, 0)
            if name_item:
                return name_item.data(Qt.ItemDataRole.UserRole)
        return None
    
    def show_new_customer_dialog(self):
        """显示新建客户对话框"""
        dialog = CustomerEditDialog(self.api_client, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.refresh_data()
    
    def edit_selected_customer(self):
        """编辑选中的客户"""
        customer_id = self.get_selected_customer_id()
        if customer_id:
            try:
                response = self.api_client.get_customer(customer_id)
                if response.get('success'):
                    customer_data = response.get('data')
                    dialog = CustomerEditDialog(self.api_client, customer_data, parent=self)
                    if dialog.exec() == QDialog.DialogCode.Accepted:
                        self.refresh_data()
                else:
                    QMessageBox.warning(self, "警告", "获取客户详情失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"获取客户详情失败: {str(e)}")
    
    def delete_selected_customer(self):
        """删除选中的客户"""
        customer_id = self.get_selected_customer_id()
        if customer_id:
            reply = QMessageBox.question(
                self, "确认删除", 
                "确定要删除选中的客户吗？此操作不可撤销。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                try:
                    response = self.api_client.delete_customer(customer_id)
                    if response.get('success'):
                        QMessageBox.information(self, "成功", "客户删除成功")
                        self.refresh_data()
                    else:
                        QMessageBox.warning(self, "警告", "删除客户失败")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除客户失败: {str(e)}")
    
    def view_customer_detail(self, customer_id):
        """查看客户详情"""
        QMessageBox.information(self, "客户详情", f"查看客户详情: {customer_id}")
    
    def on_theme_changed(self, theme_name: str):
        """主题变更事件"""
        pass


class CustomerEditDialog(QDialog):
    """
    客户编辑对话框
    
    用于新建和编辑客户
    """
    
    def __init__(self, api_client, customer_data=None, parent=None):
        super().__init__(parent)
        self.api_client = api_client
        self.customer_data = customer_data
        self.is_edit_mode = customer_data is not None
        
        self.init_ui()
        if self.is_edit_mode:
            self.load_customer_data()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("编辑客户" if self.is_edit_mode else "新建客户")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 创建表单
        form_layout = QFormLayout()
        
        # 客户名称
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入客户名称")
        form_layout.addRow("客户名称*:", self.name_input)
        
        # 邮箱
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("请输入邮箱地址")
        form_layout.addRow("邮箱:", self.email_input)
        
        # 电话
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入电话号码")
        form_layout.addRow("电话:", self.phone_input)
        
        # 公司
        self.company_input = QLineEdit()
        self.company_input.setPlaceholderText("请输入公司名称")
        form_layout.addRow("公司:", self.company_input)
        
        # 地址
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(60)
        self.address_input.setPlaceholderText("请输入地址")
        form_layout.addRow("地址:", self.address_input)
        
        # 备注
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("请输入备注信息")
        form_layout.addRow("备注:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept_customer)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_customer_data(self):
        """加载客户数据"""
        if not self.customer_data:
            return
        
        self.name_input.setText(self.customer_data.get('name', ''))
        self.email_input.setText(self.customer_data.get('email', ''))
        self.phone_input.setText(self.customer_data.get('phone', ''))
        self.company_input.setText(self.customer_data.get('company', ''))
        self.address_input.setPlainText(self.customer_data.get('address', ''))
        self.notes_input.setPlainText(self.customer_data.get('notes', ''))
    
    def accept_customer(self):
        """确认客户"""
        # 验证必填字段
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "警告", "请输入客户名称")
            return
        
        # 构建客户数据
        customer_data = {
            "name": self.name_input.text().strip(),
            "email": self.email_input.text().strip(),
            "phone": self.phone_input.text().strip(),
            "company": self.company_input.text().strip(),
            "address": self.address_input.toPlainText().strip(),
            "notes": self.notes_input.toPlainText().strip()
        }
        
        try:
            if self.is_edit_mode:
                response = self.api_client.update_customer(self.customer_data['id'], customer_data)
            else:
                response = self.api_client.create_customer(customer_data)
            
            if response.get('success'):
                QMessageBox.information(self, "成功", 
                                      "客户更新成功" if self.is_edit_mode else "客户创建成功")
                self.accept()
            else:
                QMessageBox.warning(self, "警告", 
                                  "客户更新失败" if self.is_edit_mode else "客户创建失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")
