"""
统计分析界面

显示业务数据分析和图表
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QGroupBox,
    QGridLayout, QFrame, QPushButton, QScrollArea
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
import logging

class AnalyticsViewWidget(QWidget):
    """统计分析组件"""

    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        self.analytics_data = {}
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 标题
        title_label = QLabel("业务统计分析")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #333; margin: 20px;")
        scroll_layout.addWidget(title_label)

        # 概览卡片区域
        overview_group = QGroupBox("业务概览")
        overview_layout = QGridLayout(overview_group)

        # 创建统计卡片
        self.total_orders_card = self.create_stat_card("总订单数", "0", "#2196F3")
        self.total_revenue_card = self.create_stat_card("总收入", "¥0", "#4CAF50")
        self.total_hours_card = self.create_stat_card("总工时", "0小时", "#FF9800")
        self.avg_hourly_rate_card = self.create_stat_card("平均时薪", "¥0/小时", "#9C27B0")

        overview_layout.addWidget(self.total_orders_card, 0, 0)
        overview_layout.addWidget(self.total_revenue_card, 0, 1)
        overview_layout.addWidget(self.total_hours_card, 1, 0)
        overview_layout.addWidget(self.avg_hourly_rate_card, 1, 1)

        scroll_layout.addWidget(overview_group)

        # 订单状态分析
        order_status_group = QGroupBox("订单状态分析")
        order_status_layout = QGridLayout(order_status_group)

        self.pending_orders_card = self.create_stat_card("待开始", "0", "#FFC107")
        self.in_progress_orders_card = self.create_stat_card("进行中", "0", "#2196F3")
        self.completed_orders_card = self.create_stat_card("已完成", "0", "#4CAF50")
        self.cancelled_orders_card = self.create_stat_card("已取消", "0", "#F44336")

        order_status_layout.addWidget(self.pending_orders_card, 0, 0)
        order_status_layout.addWidget(self.in_progress_orders_card, 0, 1)
        order_status_layout.addWidget(self.completed_orders_card, 1, 0)
        order_status_layout.addWidget(self.cancelled_orders_card, 1, 1)

        scroll_layout.addWidget(order_status_group)

        # 收入分析
        revenue_group = QGroupBox("收入分析")
        revenue_layout = QGridLayout(revenue_group)

        self.monthly_revenue_card = self.create_stat_card("本月收入", "¥0", "#4CAF50")
        self.pending_revenue_card = self.create_stat_card("待收金额", "¥0", "#FF9800")
        self.avg_order_value_card = self.create_stat_card("平均订单价值", "¥0", "#9C27B0")
        self.highest_order_card = self.create_stat_card("最高订单", "¥0", "#E91E63")

        revenue_layout.addWidget(self.monthly_revenue_card, 0, 0)
        revenue_layout.addWidget(self.pending_revenue_card, 0, 1)
        revenue_layout.addWidget(self.avg_order_value_card, 1, 0)
        revenue_layout.addWidget(self.highest_order_card, 1, 1)

        scroll_layout.addWidget(revenue_group)

        # 客户分析
        customer_group = QGroupBox("客户分析")
        customer_layout = QGridLayout(customer_group)

        self.total_customers_card = self.create_stat_card("总客户数", "0", "#607D8B")
        self.active_customers_card = self.create_stat_card("活跃客户", "0", "#4CAF50")
        self.new_customers_card = self.create_stat_card("新客户", "0", "#2196F3")
        self.repeat_customers_card = self.create_stat_card("回头客", "0", "#FF5722")

        customer_layout.addWidget(self.total_customers_card, 0, 0)
        customer_layout.addWidget(self.active_customers_card, 0, 1)
        customer_layout.addWidget(self.new_customers_card, 1, 0)
        customer_layout.addWidget(self.repeat_customers_card, 1, 1)

        scroll_layout.addWidget(customer_group)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("刷新数据")
        self.refresh_btn.clicked.connect(self.refresh_data)
        self.refresh_btn.setStyleSheet("background-color: #2196F3; color: white; padding: 10px; border-radius: 5px;")
        button_layout.addWidget(self.refresh_btn)

        self.export_btn = QPushButton("导出报表")
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setStyleSheet("background-color: #4CAF50; color: white; padding: 10px; border-radius: 5px;")
        button_layout.addWidget(self.export_btn)

        button_layout.addStretch()

        scroll_layout.addLayout(button_layout)

        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # 状态栏
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)

    def create_stat_card(self, title: str, value: str, color: str) -> QFrame:
        """创建统计卡片"""
        card = QFrame()
        card.setFrameStyle(QFrame.Shape.Box)
        card.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 10px;
                background-color: white;
                margin: 5px;
                padding: 10px;
            }}
            QLabel {{
                border: none;
                background-color: transparent;
            }}
        """)

        layout = QVBoxLayout(card)

        # 标题
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"font-size: 14px; color: {color}; font-weight: bold;")
        layout.addWidget(title_label)

        # 数值
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #333; margin: 10px;")
        layout.addWidget(value_label)

        # 存储value_label引用以便更新
        card.value_label = value_label

        return card

    def load_data(self):
        """加载统计数据"""
        try:
            self.status_label.setText("正在加载统计数据...")

            # 加载业务概览数据
            try:
                overview_response = self.api_client.get_analytics_overview()
                if overview_response.get('success'):
                    overview_data = overview_response.get('data', {})
                    self.update_overview_cards(overview_data)
                    self.logger.info("业务概览数据加载成功")
            except Exception as e:
                self.logger.error(f"加载业务概览数据失败: {e}")

            # 加载收入分析数据
            try:
                revenue_response = self.api_client.get_analytics_revenue()
                if revenue_response.get('success'):
                    revenue_data = revenue_response.get('data', {})
                    self.update_revenue_cards(revenue_data)
                    self.logger.info("收入分析数据加载成功")
            except Exception as e:
                self.logger.error(f"加载收入分析数据失败: {e}")

            # 加载性能分析数据
            try:
                performance_response = self.api_client.get_analytics_performance()
                if performance_response.get('success'):
                    performance_data = performance_response.get('data', {})
                    self.update_performance_cards(performance_data)
                    self.logger.info("性能分析数据加载成功")
            except Exception as e:
                self.logger.error(f"加载性能分析数据失败: {e}")

            self.status_label.setText("统计数据加载完成")

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"统计数据加载过程发生错误: {e}")
            self.status_label.setText(f"数据加载失败: {error_msg}")

    def update_overview_cards(self, data: dict):
        """更新概览卡片"""
        # 总订单数
        total_orders = data.get('total_orders', 0)
        self.total_orders_card.value_label.setText(str(total_orders))

        # 总收入
        total_revenue = data.get('total_revenue', 0)
        try:
            revenue_value = float(total_revenue) if total_revenue else 0.0
            self.total_revenue_card.value_label.setText(f"¥{revenue_value:,.2f}")
        except (ValueError, TypeError):
            self.total_revenue_card.value_label.setText(f"¥{total_revenue}")

        # 总工时
        total_hours = data.get('total_hours', 0)
        try:
            hours_value = float(total_hours) if total_hours else 0.0
            self.total_hours_card.value_label.setText(f"{hours_value:.1f}小时")
        except (ValueError, TypeError):
            self.total_hours_card.value_label.setText(f"{total_hours}小时")

        # 平均时薪
        avg_hourly_rate = data.get('avg_hourly_rate', 0)
        try:
            rate_value = float(avg_hourly_rate) if avg_hourly_rate else 0.0
            self.avg_hourly_rate_card.value_label.setText(f"¥{rate_value:.2f}/小时")
        except (ValueError, TypeError):
            self.avg_hourly_rate_card.value_label.setText(f"¥{avg_hourly_rate}/小时")

        # 更新订单状态统计
        order_stats = data.get('order_status_stats', {})
        self.pending_orders_card.value_label.setText(str(order_stats.get('pending', 0)))
        self.in_progress_orders_card.value_label.setText(str(order_stats.get('in_progress', 0)))
        self.completed_orders_card.value_label.setText(str(order_stats.get('completed', 0)))
        self.cancelled_orders_card.value_label.setText(str(order_stats.get('cancelled', 0)))

        # 更新客户统计
        customer_stats = data.get('customer_stats', {})
        self.total_customers_card.value_label.setText(str(customer_stats.get('total_customers', 0)))
        self.active_customers_card.value_label.setText(str(customer_stats.get('active_customers', 0)))
        self.new_customers_card.value_label.setText(str(customer_stats.get('new_customers', 0)))
        self.repeat_customers_card.value_label.setText(str(customer_stats.get('repeat_customers', 0)))

    def update_revenue_cards(self, data: dict):
        """更新收入卡片"""
        # 本月收入
        monthly_revenue = data.get('monthly_revenue', 0)
        try:
            monthly_value = float(monthly_revenue) if monthly_revenue else 0.0
            self.monthly_revenue_card.value_label.setText(f"¥{monthly_value:,.2f}")
        except (ValueError, TypeError):
            self.monthly_revenue_card.value_label.setText(f"¥{monthly_revenue}")

        # 待收金额
        pending_revenue = data.get('pending_revenue', 0)
        try:
            pending_value = float(pending_revenue) if pending_revenue else 0.0
            self.pending_revenue_card.value_label.setText(f"¥{pending_value:,.2f}")
        except (ValueError, TypeError):
            self.pending_revenue_card.value_label.setText(f"¥{pending_revenue}")

        # 平均订单价值
        avg_order_value = data.get('avg_order_value', 0)
        try:
            avg_value = float(avg_order_value) if avg_order_value else 0.0
            self.avg_order_value_card.value_label.setText(f"¥{avg_value:,.2f}")
        except (ValueError, TypeError):
            self.avg_order_value_card.value_label.setText(f"¥{avg_order_value}")

        # 最高订单
        highest_order = data.get('highest_order_value', 0)
        try:
            highest_value = float(highest_order) if highest_order else 0.0
            self.highest_order_card.value_label.setText(f"¥{highest_value:,.2f}")
        except (ValueError, TypeError):
            self.highest_order_card.value_label.setText(f"¥{highest_order}")

    def update_performance_cards(self, data: dict):
        """更新性能卡片"""
        # 这里可以添加更多性能相关的统计数据更新
        pass

    def export_report(self):
        """导出报表"""
        try:
            # 这里可以实现报表导出功能
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "导出报表", "报表导出功能正在开发中...")
        except Exception as e:
            self.logger.error(f"导出报表失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_data()

    def on_theme_changed(self, theme_name: str):
        """主题变更事件"""
        # 根据主题调整卡片样式
        if theme_name == "dark":
            # 深色主题样式
            card_style = """
                QFrame {
                    border: 2px solid {color};
                    border-radius: 10px;
                    background-color: #2b2b2b;
                    margin: 5px;
                    padding: 10px;
                }
                QLabel {
                    border: none;
                    background-color: transparent;
                    color: #fff;
                }
            """
        else:
            # 浅色主题样式
            card_style = """
                QFrame {
                    border: 2px solid {color};
                    border-radius: 10px;
                    background-color: white;
                    margin: 5px;
                    padding: 10px;
                }
                QLabel {
                    border: none;
                    background-color: transparent;
                }
            """

        # 更新所有卡片的样式
        cards = [
            (self.total_orders_card, "#2196F3"),
            (self.total_revenue_card, "#4CAF50"),
            (self.total_hours_card, "#FF9800"),
            (self.avg_hourly_rate_card, "#9C27B0"),
            (self.pending_orders_card, "#FFC107"),
            (self.in_progress_orders_card, "#2196F3"),
            (self.completed_orders_card, "#4CAF50"),
            (self.cancelled_orders_card, "#F44336"),
            (self.monthly_revenue_card, "#4CAF50"),
            (self.pending_revenue_card, "#FF9800"),
            (self.avg_order_value_card, "#9C27B0"),
            (self.highest_order_card, "#E91E63"),
            (self.total_customers_card, "#607D8B"),
            (self.active_customers_card, "#4CAF50"),
            (self.new_customers_card, "#2196F3"),
            (self.repeat_customers_card, "#FF5722")
        ]

        for card, color in cards:
            card.setStyleSheet(card_style.format(color=color))
