"""
订单列表界面

显示和管理订单列表
提供订单的增删改查功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QComboBox, QLineEdit, QLabel, QHeaderView, QMessageBox,
    QDialog, QFormLayout, QDateEdit, QTextEdit, QSpinBox, QDoubleSpinBox,
    QCheckBox, QDialogButtonBox, QMenu, QAbstractItemView
)
from PyQt6.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QFont
from datetime import datetime, date
import logging

class OrderListWidget(QWidget):
    """
    订单列表组件
    
    显示订单列表并提供管理功能
    """
    
    # 信号定义
    order_selected = pyqtSignal(str)  # 订单选中信号
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        self.orders_data = []
        self.customers_data = {}
        
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建工具栏
        toolbar_layout = QHBoxLayout()
        
        # 新建订单按钮
        self.new_order_btn = QPushButton("新建订单")
        self.new_order_btn.clicked.connect(self.show_new_order_dialog)
        toolbar_layout.addWidget(self.new_order_btn)
        
        # 编辑订单按钮
        self.edit_order_btn = QPushButton("编辑订单")
        self.edit_order_btn.clicked.connect(self.edit_selected_order)
        self.edit_order_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_order_btn)
        
        # 删除订单按钮
        self.delete_order_btn = QPushButton("删除订单")
        self.delete_order_btn.clicked.connect(self.delete_selected_order)
        self.delete_order_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_order_btn)
        
        toolbar_layout.addStretch()
        
        # 状态筛选
        toolbar_layout.addWidget(QLabel("状态:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "待开始", "进行中", "已完成", "已取消", "已暂停"])
        self.status_filter.currentTextChanged.connect(self.filter_orders)
        toolbar_layout.addWidget(self.status_filter)
        
        # 搜索框
        toolbar_layout.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入订单标题或客户名称...")
        self.search_input.textChanged.connect(self.filter_orders)
        toolbar_layout.addWidget(self.search_input)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 创建订单表格
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(8)
        self.orders_table.setHorizontalHeaderLabels([
            "订单标题", "客户", "项目类型", "状态", "总金额", "截止日期", "创建时间", "操作"
        ])
        
        # 设置表格属性
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSortingEnabled(True)
        
        # 设置列宽
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 订单标题
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 客户
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 项目类型
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 总金额
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 截止日期
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 创建时间
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # 操作
        
        # 连接表格信号
        self.orders_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.orders_table.itemDoubleClicked.connect(self.on_item_double_clicked)
        
        # 设置右键菜单
        self.orders_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.orders_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.orders_table)
        
        # 创建状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        self.total_label = QLabel("总计: 0 个订单")
        status_layout.addWidget(self.total_label)
        
        layout.addLayout(status_layout)
    
    def load_data(self):
        """加载数据"""
        try:
            self.status_label.setText("正在加载订单数据...")
            
            # 加载客户数据
            customers_response = self.api_client.get_customers(page_size=1000)
            if customers_response.get('success'):
                customers_list = customers_response.get('data', [])
                self.customers_data = {customer['id']: customer for customer in customers_list}
            
            # 加载订单数据
            orders_response = self.api_client.get_orders(page_size=1000)
            if orders_response.get('success'):
                self.orders_data = orders_response.get('data', [])
                self.populate_table()
                self.status_label.setText("数据加载完成")
            else:
                self.status_label.setText("加载订单数据失败")
                
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            self.status_label.setText(f"加载失败: {str(e)}")
    
    def populate_table(self):
        """填充表格数据"""
        filtered_orders = self.get_filtered_orders()
        
        self.orders_table.setRowCount(len(filtered_orders))
        
        for row, order in enumerate(filtered_orders):
            # 订单标题
            title_item = QTableWidgetItem(order.get('title', ''))
            self.orders_table.setItem(row, 0, title_item)
            
            # 客户名称
            customer_id = order.get('customer_id')
            customer_name = self.customers_data.get(customer_id, {}).get('name', '未知客户')
            customer_item = QTableWidgetItem(customer_name)
            self.orders_table.setItem(row, 1, customer_item)
            
            # 项目类型
            project_type = order.get('project_type', '')
            type_item = QTableWidgetItem(project_type)
            self.orders_table.setItem(row, 2, type_item)
            
            # 状态
            status = order.get('status', '')
            status_item = QTableWidgetItem(self.get_status_display(status))
            status_item.setData(Qt.ItemDataRole.UserRole, order.get('id'))  # 存储订单ID
            self.orders_table.setItem(row, 3, status_item)
            
            # 总金额
            total_amount = order.get('total_amount', 0)
            amount_item = QTableWidgetItem(f"¥{total_amount:.2f}")
            amount_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.orders_table.setItem(row, 4, amount_item)
            
            # 截止日期
            deadline = order.get('deadline')
            if deadline:
                deadline_date = datetime.fromisoformat(deadline.replace('Z', '+00:00')).date()
                deadline_item = QTableWidgetItem(deadline_date.strftime('%Y-%m-%d'))
                
                # 根据截止日期设置颜色
                today = date.today()
                if deadline_date < today:
                    deadline_item.setBackground(Qt.GlobalColor.red)
                elif (deadline_date - today).days <= 3:
                    deadline_item.setBackground(Qt.GlobalColor.yellow)
            else:
                deadline_item = QTableWidgetItem("未设置")
            
            self.orders_table.setItem(row, 5, deadline_item)
            
            # 创建时间
            created_at = order.get('created_at')
            if created_at:
                created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                created_item = QTableWidgetItem(created_date.strftime('%Y-%m-%d %H:%M'))
            else:
                created_item = QTableWidgetItem("")
            
            self.orders_table.setItem(row, 6, created_item)
            
            # 操作按钮
            action_widget = self.create_action_widget(order)
            self.orders_table.setCellWidget(row, 7, action_widget)
        
        # 更新统计信息
        self.total_label.setText(f"总计: {len(filtered_orders)} 个订单")
    
    def create_action_widget(self, order):
        """创建操作按钮组件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(4, 2, 4, 2)
        
        # 查看详情按钮
        view_btn = QPushButton("详情")
        view_btn.setMaximumWidth(50)
        view_btn.clicked.connect(lambda: self.view_order_detail(order['id']))
        layout.addWidget(view_btn)
        
        # 开始计时按钮
        timer_btn = QPushButton("计时")
        timer_btn.setMaximumWidth(50)
        timer_btn.clicked.connect(lambda: self.start_order_timer(order['id']))
        layout.addWidget(timer_btn)
        
        return widget
    
    def get_filtered_orders(self):
        """获取筛选后的订单"""
        filtered = self.orders_data
        
        # 状态筛选
        status_filter = self.status_filter.currentText()
        if status_filter != "全部":
            status_map = {
                "待开始": "pending",
                "进行中": "in_progress", 
                "已完成": "completed",
                "已取消": "cancelled",
                "已暂停": "paused"
            }
            target_status = status_map.get(status_filter)
            if target_status:
                filtered = [order for order in filtered if order.get('status') == target_status]
        
        # 搜索筛选
        search_text = self.search_input.text().lower()
        if search_text:
            filtered = [
                order for order in filtered
                if search_text in order.get('title', '').lower() or
                   search_text in self.customers_data.get(order.get('customer_id'), {}).get('name', '').lower()
            ]
        
        return filtered
    
    def get_status_display(self, status):
        """获取状态显示文本"""
        status_map = {
            "pending": "待开始",
            "in_progress": "进行中",
            "completed": "已完成", 
            "cancelled": "已取消",
            "paused": "已暂停"
        }
        return status_map.get(status, status)

    def filter_orders(self):
        """筛选订单"""
        self.populate_table()

    def refresh_data(self):
        """刷新数据"""
        self.load_data()

    def on_selection_changed(self):
        """选择变更事件"""
        selected_items = self.orders_table.selectedItems()
        has_selection = len(selected_items) > 0

        self.edit_order_btn.setEnabled(has_selection)
        self.delete_order_btn.setEnabled(has_selection)

        if has_selection:
            row = selected_items[0].row()
            status_item = self.orders_table.item(row, 3)
            if status_item:
                order_id = status_item.data(Qt.ItemDataRole.UserRole)
                self.order_selected.emit(order_id)

    def on_item_double_clicked(self, item):
        """双击事件"""
        row = item.row()
        status_item = self.orders_table.item(row, 3)
        if status_item:
            order_id = status_item.data(Qt.ItemDataRole.UserRole)
            self.view_order_detail(order_id)

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.orders_table.itemAt(position)
        if item is None:
            return

        menu = QMenu(self)

        view_action = QAction("查看详情", self)
        view_action.triggered.connect(lambda: self.view_order_detail(self.get_selected_order_id()))
        menu.addAction(view_action)

        edit_action = QAction("编辑订单", self)
        edit_action.triggered.connect(self.edit_selected_order)
        menu.addAction(edit_action)

        menu.addSeparator()

        timer_action = QAction("开始计时", self)
        timer_action.triggered.connect(lambda: self.start_order_timer(self.get_selected_order_id()))
        menu.addAction(timer_action)

        menu.addSeparator()

        delete_action = QAction("删除订单", self)
        delete_action.triggered.connect(self.delete_selected_order)
        menu.addAction(delete_action)

        menu.exec(self.orders_table.mapToGlobal(position))

    def get_selected_order_id(self):
        """获取选中的订单ID"""
        selected_items = self.orders_table.selectedItems()
        if selected_items:
            row = selected_items[0].row()
            status_item = self.orders_table.item(row, 3)
            if status_item:
                return status_item.data(Qt.ItemDataRole.UserRole)
        return None

    def show_new_order_dialog(self):
        """显示新建订单对话框"""
        dialog = OrderEditDialog(self.api_client, self.customers_data, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.refresh_data()

    def edit_selected_order(self):
        """编辑选中的订单"""
        order_id = self.get_selected_order_id()
        if order_id:
            try:
                response = self.api_client.get_order(order_id)
                if response.get('success'):
                    order_data = response.get('data')
                    dialog = OrderEditDialog(self.api_client, self.customers_data, order_data, parent=self)
                    if dialog.exec() == QDialog.DialogCode.Accepted:
                        self.refresh_data()
                else:
                    QMessageBox.warning(self, "警告", "获取订单详情失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"获取订单详情失败: {str(e)}")

    def delete_selected_order(self):
        """删除选中的订单"""
        order_id = self.get_selected_order_id()
        if order_id:
            reply = QMessageBox.question(
                self, "确认删除",
                "确定要删除选中的订单吗？此操作不可撤销。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    response = self.api_client.delete_order(order_id)
                    if response.get('success'):
                        QMessageBox.information(self, "成功", "订单删除成功")
                        self.refresh_data()
                    else:
                        QMessageBox.warning(self, "警告", "删除订单失败")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"删除订单失败: {str(e)}")

    def view_order_detail(self, order_id):
        """查看订单详情"""
        # 这里可以打开订单详情对话框或切换到详情标签页
        QMessageBox.information(self, "订单详情", f"查看订单详情: {order_id}")

    def start_order_timer(self, order_id):
        """开始订单计时"""
        try:
            response = self.api_client.timer_operation('start', order_id)
            if response.get('success'):
                QMessageBox.information(self, "成功", "计时器已启动")
            else:
                QMessageBox.warning(self, "警告", "启动计时器失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动计时器失败: {str(e)}")

    def on_theme_changed(self, theme_name: str):
        """主题变更事件"""
        # 可以在这里更新组件的主题样式
        pass


class OrderEditDialog(QDialog):
    """
    订单编辑对话框

    用于新建和编辑订单
    """

    def __init__(self, api_client, customers_data, order_data=None, parent=None):
        super().__init__(parent)
        self.api_client = api_client
        self.customers_data = customers_data
        self.order_data = order_data
        self.is_edit_mode = order_data is not None

        self.init_ui()
        if self.is_edit_mode:
            self.load_order_data()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("编辑订单" if self.is_edit_mode else "新建订单")
        self.setModal(True)
        self.resize(500, 600)

        layout = QVBoxLayout(self)

        # 创建表单
        form_layout = QFormLayout()

        # 订单标题
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("请输入订单标题")
        form_layout.addRow("订单标题*:", self.title_input)

        # 客户选择
        self.customer_combo = QComboBox()
        self.customer_combo.addItem("请选择客户", "")
        for customer_id, customer in self.customers_data.items():
            self.customer_combo.addItem(customer['name'], customer_id)
        form_layout.addRow("客户*:", self.customer_combo)

        # 项目类型
        self.project_type_combo = QComboBox()
        self.project_type_combo.addItems([
            "网站开发", "移动应用", "桌面软件", "数据分析",
            "论文写作", "内容创作", "技术咨询", "其他"
        ])
        form_layout.addRow("项目类型*:", self.project_type_combo)

        # 项目描述
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(100)
        self.description_input.setPlaceholderText("请输入项目描述")
        form_layout.addRow("项目描述:", self.description_input)

        # 总金额
        self.total_amount_input = QDoubleSpinBox()
        self.total_amount_input.setRange(0, 999999.99)
        self.total_amount_input.setDecimals(2)
        self.total_amount_input.setSuffix(" 元")
        form_layout.addRow("总金额*:", self.total_amount_input)

        # 定金金额
        self.deposit_amount_input = QDoubleSpinBox()
        self.deposit_amount_input.setRange(0, 999999.99)
        self.deposit_amount_input.setDecimals(2)
        self.deposit_amount_input.setSuffix(" 元")
        form_layout.addRow("定金金额:", self.deposit_amount_input)

        # 截止日期
        self.deadline_input = QDateEdit()
        self.deadline_input.setDate(QDate.currentDate().addDays(7))
        self.deadline_input.setCalendarPopup(True)
        form_layout.addRow("截止日期:", self.deadline_input)

        # 状态
        self.status_combo = QComboBox()
        self.status_combo.addItems(["待开始", "进行中", "已完成", "已取消", "已暂停"])
        form_layout.addRow("状态:", self.status_combo)

        # 是否紧急
        self.is_urgent_checkbox = QCheckBox("紧急订单")
        form_layout.addRow("", self.is_urgent_checkbox)

        layout.addLayout(form_layout)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept_order)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def load_order_data(self):
        """加载订单数据"""
        if not self.order_data:
            return

        self.title_input.setText(self.order_data.get('title', ''))

        # 设置客户
        customer_id = self.order_data.get('customer_id')
        if customer_id:
            index = self.customer_combo.findData(customer_id)
            if index >= 0:
                self.customer_combo.setCurrentIndex(index)

        self.project_type_combo.setCurrentText(self.order_data.get('project_type', ''))
        self.description_input.setPlainText(self.order_data.get('description', ''))
        self.total_amount_input.setValue(self.order_data.get('total_amount', 0))
        self.deposit_amount_input.setValue(self.order_data.get('deposit_amount', 0))

        # 设置截止日期
        deadline = self.order_data.get('deadline')
        if deadline:
            deadline_date = datetime.fromisoformat(deadline.replace('Z', '+00:00')).date()
            self.deadline_input.setDate(QDate(deadline_date))

        # 设置状态
        status = self.order_data.get('status', 'pending')
        status_map = {
            "pending": "待开始",
            "in_progress": "进行中",
            "completed": "已完成",
            "cancelled": "已取消",
            "paused": "已暂停"
        }
        status_text = status_map.get(status, "待开始")
        self.status_combo.setCurrentText(status_text)

        self.is_urgent_checkbox.setChecked(self.order_data.get('is_urgent', False))

    def accept_order(self):
        """确认订单"""
        # 验证必填字段
        if not self.title_input.text().strip():
            QMessageBox.warning(self, "警告", "请输入订单标题")
            return

        if not self.customer_combo.currentData():
            QMessageBox.warning(self, "警告", "请选择客户")
            return

        if self.total_amount_input.value() <= 0:
            QMessageBox.warning(self, "警告", "请输入有效的总金额")
            return

        # 构建订单数据
        status_map = {
            "待开始": "pending",
            "进行中": "in_progress",
            "已完成": "completed",
            "已取消": "cancelled",
            "已暂停": "paused"
        }

        order_data = {
            "title": self.title_input.text().strip(),
            "customer_id": self.customer_combo.currentData(),
            "project_type": self.project_type_combo.currentText(),
            "description": self.description_input.toPlainText().strip(),
            "total_amount": self.total_amount_input.value(),
            "deposit_amount": self.deposit_amount_input.value(),
            "deadline": self.deadline_input.date().toString(Qt.DateFormat.ISODate),
            "status": status_map.get(self.status_combo.currentText(), "pending"),
            "is_urgent": self.is_urgent_checkbox.isChecked()
        }

        try:
            if self.is_edit_mode:
                response = self.api_client.update_order(self.order_data['id'], order_data)
            else:
                response = self.api_client.create_order(order_data)

            if response.get('success'):
                QMessageBox.information(self, "成功",
                                      "订单更新成功" if self.is_edit_mode else "订单创建成功")
                self.accept()
            else:
                QMessageBox.warning(self, "警告",
                                  "订单更新失败" if self.is_edit_mode else "订单创建失败")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")
