"""
工时记录界面

显示和管理工时记录
提供计时器功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QHeaderView, QMessageBox, QComboBox, QLineEdit,
    QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox, QTextEdit,
    QAbstractItemView
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont
from datetime import datetime
import logging

class WorkLogViewWidget(QWidget):
    """工时记录组件"""

    # 信号定义
    timer_started = pyqtSignal(str)  # 计时器启动信号
    timer_stopped = pyqtSignal(str)  # 计时器停止信号

    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        self.work_logs_data = []
        self.timer_status = {}
        self.current_timer = None

        # 创建定时器用于更新计时器显示
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_timer_display)
        self.update_timer.start(1000)  # 每秒更新一次

        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建计时器控制区域
        timer_group = QGroupBox("计时器控制")
        timer_layout = QVBoxLayout(timer_group)

        # 计时器状态显示
        status_layout = QHBoxLayout()

        self.timer_status_label = QLabel("计时器状态: 未启动")
        self.timer_status_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        status_layout.addWidget(self.timer_status_label)

        status_layout.addStretch()

        self.current_time_label = QLabel("00:00:00")
        self.current_time_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #2196F3;")
        status_layout.addWidget(self.current_time_label)

        timer_layout.addLayout(status_layout)

        # 计时器控制按钮
        control_layout = QHBoxLayout()

        self.start_timer_btn = QPushButton("开始计时")
        self.start_timer_btn.clicked.connect(self.start_timer)
        self.start_timer_btn.setStyleSheet("background-color: #4CAF50; color: white; padding: 10px;")
        control_layout.addWidget(self.start_timer_btn)

        self.stop_timer_btn = QPushButton("停止计时")
        self.stop_timer_btn.clicked.connect(self.stop_timer)
        self.stop_timer_btn.setEnabled(False)
        self.stop_timer_btn.setStyleSheet("background-color: #f44336; color: white; padding: 10px;")
        control_layout.addWidget(self.stop_timer_btn)

        self.pause_timer_btn = QPushButton("暂停计时")
        self.pause_timer_btn.clicked.connect(self.pause_timer)
        self.pause_timer_btn.setEnabled(False)
        self.pause_timer_btn.setStyleSheet("background-color: #FF9800; color: white; padding: 10px;")
        control_layout.addWidget(self.pause_timer_btn)

        control_layout.addStretch()

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        control_layout.addWidget(self.refresh_btn)

        timer_layout.addLayout(control_layout)

        layout.addWidget(timer_group)

        # 工时记录表格
        records_group = QGroupBox("工时记录")
        records_layout = QVBoxLayout(records_group)

        # 创建工时记录表格
        self.work_logs_table = QTableWidget()
        self.work_logs_table.setColumnCount(7)
        self.work_logs_table.setHorizontalHeaderLabels([
            "订单标题", "工作内容", "开始时间", "结束时间", "工作时长", "时薪", "收入"
        ])

        # 设置表格属性
        self.work_logs_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.work_logs_table.setAlternatingRowColors(True)
        self.work_logs_table.setSortingEnabled(True)

        # 设置列宽
        header = self.work_logs_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 订单标题
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 工作内容
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 开始时间
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 结束时间
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 工作时长
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 时薪
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 收入

        records_layout.addWidget(self.work_logs_table)

        layout.addWidget(records_group)

        # 创建状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        self.total_label = QLabel("总计: 0 条记录")
        status_layout.addWidget(self.total_label)

        layout.addLayout(status_layout)

    def load_data(self):
        """加载数据"""
        try:
            self.status_label.setText("正在加载工时数据...")

            # 加载计时器状态
            try:
                timer_response = self.api_client.get_timer_status()
                if timer_response.get('success'):
                    self.timer_status = timer_response.get('data', {})
                    self.update_timer_status_display()
                    self.logger.info("计时器状态加载成功")
            except Exception as e:
                self.logger.error(f"加载计时器状态失败: {e}")

            # 加载工时记录
            try:
                work_logs_response = self.api_client.get_work_logs(page_size=100)
                if work_logs_response.get('success'):
                    self.work_logs_data = work_logs_response.get('data', [])
                    self.populate_table()
                    self.status_label.setText(f"数据加载完成 - {len(self.work_logs_data)} 条记录")
                    self.logger.info(f"成功加载 {len(self.work_logs_data)} 条工时记录")
                else:
                    error_msg = work_logs_response.get('message', '未知错误')
                    self.status_label.setText(f"工时数据加载失败: {error_msg}")
                    self.logger.error(f"工时数据加载失败: {error_msg}")
            except Exception as e:
                error_msg = str(e)
                self.status_label.setText(f"工时数据加载失败: {error_msg}")
                self.logger.error(f"工时数据加载异常: {e}")

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"数据加载过程发生未知错误: {e}")
            self.status_label.setText(f"数据加载失败: {error_msg}")

    def populate_table(self):
        """填充表格数据"""
        self.work_logs_table.setRowCount(len(self.work_logs_data))

        for row, work_log in enumerate(self.work_logs_data):
            # 订单标题
            order_title = work_log.get('order_title', '未知订单')
            self.work_logs_table.setItem(row, 0, QTableWidgetItem(order_title))

            # 工作内容
            description = work_log.get('description', '')
            self.work_logs_table.setItem(row, 1, QTableWidgetItem(description))

            # 开始时间
            start_time = work_log.get('start_time', '')
            if start_time:
                start_time = start_time.replace('T', ' ').split('.')[0]
            self.work_logs_table.setItem(row, 2, QTableWidgetItem(start_time))

            # 结束时间
            end_time = work_log.get('end_time', '')
            if end_time:
                end_time = end_time.replace('T', ' ').split('.')[0]
            self.work_logs_table.setItem(row, 3, QTableWidgetItem(end_time))

            # 工作时长
            duration = work_log.get('duration_display', '0分钟')
            self.work_logs_table.setItem(row, 4, QTableWidgetItem(duration))

            # 时薪
            hourly_rate = work_log.get('hourly_rate', 0)
            try:
                rate_value = float(hourly_rate) if hourly_rate else 0.0
                rate_item = QTableWidgetItem(f"¥{rate_value:.2f}")
            except (ValueError, TypeError):
                rate_item = QTableWidgetItem(f"¥{hourly_rate}")
            rate_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.work_logs_table.setItem(row, 5, rate_item)

            # 收入
            earnings = work_log.get('earnings', 0)
            try:
                earnings_value = float(earnings) if earnings else 0.0
                earnings_item = QTableWidgetItem(f"¥{earnings_value:.2f}")
            except (ValueError, TypeError):
                earnings_item = QTableWidgetItem(f"¥{earnings}")
            earnings_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.work_logs_table.setItem(row, 6, earnings_item)

        # 更新总计标签
        self.total_label.setText(f"总计: {len(self.work_logs_data)} 条记录")

    def update_timer_status_display(self):
        """更新计时器状态显示"""
        is_running = self.timer_status.get('is_running', False)

        if is_running:
            order_title = self.timer_status.get('order_title', '未知订单')
            self.timer_status_label.setText(f"正在计时: {order_title}")
            self.timer_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #4CAF50;")

            # 更新按钮状态
            self.start_timer_btn.setEnabled(False)
            self.stop_timer_btn.setEnabled(True)
            self.pause_timer_btn.setEnabled(True)
        else:
            self.timer_status_label.setText("计时器状态: 未启动")
            self.timer_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #666;")

            # 更新按钮状态
            self.start_timer_btn.setEnabled(True)
            self.stop_timer_btn.setEnabled(False)
            self.pause_timer_btn.setEnabled(False)

    def update_timer_display(self):
        """更新计时器时间显示"""
        if self.timer_status.get('is_running', False):
            duration_display = self.timer_status.get('current_duration_display', '00:00:00')
            self.current_time_label.setText(duration_display)
            self.current_time_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #4CAF50;")
        else:
            self.current_time_label.setText("00:00:00")
            self.current_time_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #666;")

    def start_timer(self):
        """开始计时"""
        # 这里应该弹出对话框选择订单，暂时使用第一个订单
        try:
            # 获取订单列表来选择订单
            orders_response = self.api_client.get_orders(page_size=10)
            if orders_response.get('success'):
                orders = orders_response.get('data', [])
                if orders:
                    order_id = orders[0]['id']  # 使用第一个订单
                    response = self.api_client.timer_operation('start', order_id)
                    if response.get('success'):
                        QMessageBox.information(self, "成功", "计时器已启动")
                        self.refresh_timer_status()
                        self.timer_started.emit(order_id)
                    else:
                        error_msg = response.get('message', '未知错误')
                        QMessageBox.warning(self, "警告", f"启动计时器失败: {error_msg}")
                else:
                    QMessageBox.warning(self, "警告", "没有可用的订单")
            else:
                QMessageBox.warning(self, "警告", "无法获取订单列表")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动计时器时发生错误: {str(e)}")

    def stop_timer(self):
        """停止计时"""
        try:
            order_id = self.timer_status.get('order_id')
            if order_id:
                response = self.api_client.timer_operation('stop', order_id)
                if response.get('success'):
                    QMessageBox.information(self, "成功", "计时器已停止")
                    self.refresh_timer_status()
                    self.timer_stopped.emit(order_id)
                    self.refresh_data()  # 刷新工时记录
                else:
                    error_msg = response.get('message', '未知错误')
                    QMessageBox.warning(self, "警告", f"停止计时器失败: {error_msg}")
            else:
                QMessageBox.warning(self, "警告", "没有正在运行的计时器")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止计时器时发生错误: {str(e)}")

    def pause_timer(self):
        """暂停计时"""
        try:
            order_id = self.timer_status.get('order_id')
            if order_id:
                response = self.api_client.timer_operation('pause', order_id)
                if response.get('success'):
                    QMessageBox.information(self, "成功", "计时器已暂停")
                    self.refresh_timer_status()
                else:
                    error_msg = response.get('message', '未知错误')
                    QMessageBox.warning(self, "警告", f"暂停计时器失败: {error_msg}")
            else:
                QMessageBox.warning(self, "警告", "没有正在运行的计时器")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"暂停计时器时发生错误: {str(e)}")

    def refresh_timer_status(self):
        """刷新计时器状态"""
        try:
            timer_response = self.api_client.get_timer_status()
            if timer_response.get('success'):
                self.timer_status = timer_response.get('data', {})
                self.update_timer_status_display()
        except Exception as e:
            self.logger.error(f"刷新计时器状态失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_data()

    def on_theme_changed(self, theme_name: str):
        """主题变更事件"""
        # 根据主题调整样式
        if theme_name == "dark":
            self.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 5px;
                    margin-top: 1ex;
                    color: #fff;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }
                QTableWidget {
                    background-color: #2b2b2b;
                    color: #fff;
                    gridline-color: #555;
                }
                QTableWidget::item {
                    border-bottom: 1px solid #555;
                }
                QTableWidget::item:selected {
                    background-color: #3d5afe;
                }
            """)
        else:
            self.setStyleSheet("")
