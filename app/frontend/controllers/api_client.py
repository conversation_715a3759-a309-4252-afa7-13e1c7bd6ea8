"""
API客户端

负责与后端FastAPI服务通信
提供统一的HTTP请求接口
"""

import requests
import json
from typing import Dict, Any, Optional, List
from PyQt6.QtCore import QObject, pyqtSignal
import logging

class APIClient(QObject):
    """
    API客户端类
    
    提供与后端API通信的统一接口
    """
    
    # 信号定义
    request_started = pyqtSignal()
    request_finished = pyqtSignal()
    error_occurred = pyqtSignal(str)
    
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        super().__init__()
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, 
                     data: Optional[Dict] = None, 
                     params: Optional[Dict] = None,
                     files: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            params: 查询参数
            files: 文件数据
            
        Returns:
            Dict: 响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            self.request_started.emit()
            
            # 准备请求参数
            kwargs = {
                'params': params,
                'timeout': 30
            }
            
            if files:
                # 文件上传请求
                kwargs['files'] = files
                if data:
                    kwargs['data'] = data
                # 移除Content-Type头，让requests自动设置
                headers = self.session.headers.copy()
                if 'Content-Type' in headers:
                    del headers['Content-Type']
                kwargs['headers'] = headers
            else:
                # 普通JSON请求
                if data:
                    kwargs['json'] = data
            
            # 发送请求
            response = self.session.request(method, url, **kwargs)
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            try:
                result = response.json()
            except json.JSONDecodeError:
                result = {'message': response.text}
            
            self.request_finished.emit()
            return result
            
        except requests.exceptions.RequestException as e:
            error_msg = f"API请求失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"请求处理失败: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            raise Exception(error_msg)
    
    # 客户管理API
    def get_customers(self, page: int = 1, page_size: int = 20, **kwargs) -> Dict[str, Any]:
        """获取客户列表"""
        params = {'page': page, 'page_size': page_size, **kwargs}
        return self._make_request('GET', '/api/v1/customers', params=params)
    
    def get_customer(self, customer_id: str) -> Dict[str, Any]:
        """获取客户详情"""
        return self._make_request('GET', f'/api/v1/customers/{customer_id}')
    
    def create_customer(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建客户"""
        return self._make_request('POST', '/api/v1/customers', data=customer_data)
    
    def update_customer(self, customer_id: str, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新客户"""
        return self._make_request('PUT', f'/api/v1/customers/{customer_id}', data=customer_data)
    
    def delete_customer(self, customer_id: str) -> Dict[str, Any]:
        """删除客户"""
        return self._make_request('DELETE', f'/api/v1/customers/{customer_id}')
    
    # 订单管理API
    def get_orders(self, page: int = 1, page_size: int = 20, **kwargs) -> Dict[str, Any]:
        """获取订单列表"""
        params = {'page': page, 'page_size': page_size, **kwargs}
        return self._make_request('GET', '/api/v1/orders', params=params)
    
    def get_order(self, order_id: str) -> Dict[str, Any]:
        """获取订单详情"""
        return self._make_request('GET', f'/api/v1/orders/{order_id}')
    
    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建订单"""
        return self._make_request('POST', '/api/v1/orders', data=order_data)
    
    def update_order(self, order_id: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新订单"""
        return self._make_request('PUT', f'/api/v1/orders/{order_id}', data=order_data)
    
    def delete_order(self, order_id: str) -> Dict[str, Any]:
        """删除订单"""
        return self._make_request('DELETE', f'/api/v1/orders/{order_id}')
    
    # 工时管理API
    def get_work_logs(self, page: int = 1, page_size: int = 20, **kwargs) -> Dict[str, Any]:
        """获取工时记录列表"""
        params = {'page': page, 'page_size': page_size, **kwargs}
        return self._make_request('GET', '/api/v1/work-logs', params=params)
    
    def create_work_log(self, work_log_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建工时记录"""
        return self._make_request('POST', '/api/v1/work-logs', data=work_log_data)
    
    def get_timer_status(self) -> Dict[str, Any]:
        """获取计时器状态"""
        return self._make_request('GET', '/api/v1/work-logs/timer/status')
    
    def timer_operation(self, operation: str, order_id: str = None) -> Dict[str, Any]:
        """计时器操作"""
        data = {'operation': operation}
        if order_id:
            data['order_id'] = order_id
        return self._make_request('POST', '/api/v1/work-logs/timer/operation', data=data)
    
    # 统计分析API
    def get_analytics_overview(self) -> Dict[str, Any]:
        """获取业务概览"""
        return self._make_request('GET', '/api/v1/analytics/overview')
    
    def get_revenue_analysis(self, **kwargs) -> Dict[str, Any]:
        """获取收入分析"""
        return self._make_request('GET', '/api/v1/analytics/revenue', params=kwargs)
    
    def get_dashboard_widgets(self) -> Dict[str, Any]:
        """获取仪表板组件数据"""
        return self._make_request('GET', '/api/v1/analytics/dashboard/widgets')
    
    # 文件管理API
    def upload_file(self, file_path: str, order_id: str, **kwargs) -> Dict[str, Any]:
        """上传文件"""
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {'order_id': order_id, **kwargs}
            return self._make_request('POST', '/api/v1/attachments/upload', data=data, files=files)
    
    def get_attachments(self, page: int = 1, page_size: int = 20, **kwargs) -> Dict[str, Any]:
        """获取文件列表"""
        params = {'page': page, 'page_size': page_size, **kwargs}
        return self._make_request('GET', '/api/v1/attachments', params=params)
    
    # 系统配置API
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return self._make_request('GET', '/api/v1/settings/system/info')
    
    def get_settings_groups(self) -> Dict[str, Any]:
        """获取分组配置"""
        return self._make_request('GET', '/api/v1/settings/groups')
    
    def get_config_value(self, config_key: str) -> Dict[str, Any]:
        """获取配置值"""
        return self._make_request('GET', f'/api/v1/settings/key/{config_key}/value')
    
    def set_config_value(self, config_key: str, config_value: str) -> Dict[str, Any]:
        """设置配置值"""
        return self._make_request('PUT', f'/api/v1/settings/key/{config_key}/value', data=config_value)
    
    # 健康检查
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self._make_request('GET', '/health')
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            self.health_check()
            return True
        except:
            return False
