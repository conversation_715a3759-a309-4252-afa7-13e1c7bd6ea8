"""
数据模型包初始化

导入所有数据模型，确保SQLAlchemy能够正确识别和创建表
"""

# 导入基础模型
from app.models.base import (
    Base, BaseModel, TimestampMixin, UUIDMixin, SoftDeleteMixin, OrderableMixin,
    OrderStatus, BillingMethod, CustomerSource, CategoryType
)

# 导入业务模型
from app.models.customer import Customer
from app.models.category import Category
from app.models.order import Order
from app.models.work_log import WorkLog
from app.models.attachment import Attachment
from app.models.communication import Communication
from app.models.template import Template
from app.models.setting import Setting

# 导入枚举类
from app.models.communication import (
    CommunicationType, CommunicationDirection, CommunicationCategory
)
from app.models.setting import SettingCategory, ValueType

# 确保所有模型都被导入，这样SQLAlchemy才能正确创建表
__all__ = [
    # 基础类
    "Base",
    "BaseModel", 
    "TimestampMixin",
    "UUIDMixin",
    "SoftDeleteMixin",
    "OrderableMixin",
    
    # 业务模型
    "Customer",
    "Category", 
    "Order",
    "WorkLog",
    "Attachment",
    "Communication",
    "Template",
    "Setting",
    
    # 枚举类
    "OrderStatus",
    "BillingMethod", 
    "CustomerSource",
    "CategoryType",
    "CommunicationType",
    "CommunicationDirection",
    "CommunicationCategory",
    "SettingCategory",
    "ValueType"
]
