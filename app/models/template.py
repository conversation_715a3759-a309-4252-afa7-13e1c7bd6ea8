"""
订单模板数据模型

定义订单模板的数据结构和业务逻辑
支持快速创建常用类型的订单
"""

from sqlalchemy import Column, String, Text, JSON, Boolean, Integer, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, OrderableMixin

class Template(BaseModel, OrderableMixin):
    """
    订单模板模型
    
    存储常用订单类型的模板信息，支持快速创建订单
    包含预设的字段值、计费方式、交付清单等
    """
    
    __tablename__ = "templates"
    __table_args__ = {'comment': '订单模板表'}
    
    # 基本信息
    name = Column(
        String(100),
        nullable=False,
        index=True,
        comment="模板名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="模板描述"
    )
    
    # 关联信息
    category_id = Column(
        String(36),
        ForeignKey("categories.id"),
        nullable=False,
        index=True,
        comment="项目分类ID"
    )
    
    # 模板数据（JSON格式存储）
    template_data = Column(
        JSON,
        nullable=False,
        comment="模板数据"
    )
    
    # 状态信息
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否启用"
    )
    
    is_default = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为默认模板"
    )
    
    # 使用统计
    usage_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    # 其他信息
    tags = Column(
        String(500),
        nullable=True,
        comment="标签"
    )
    
    # 关联关系
    category = relationship(
        "Category",
        back_populates="templates"
    )
    
    def __repr__(self) -> str:
        return f"<Template(id={self.id}, name={self.name}, category_id={self.category_id})>"
    
    @property
    def display_name(self) -> str:
        """
        显示名称
        
        Returns:
            str: 模板显示名称
        """
        if self.category:
            return f"{self.name} ({self.category.name})"
        return self.name
    
    def get_template_data(self) -> dict:
        """
        获取模板数据
        
        Returns:
            dict: 模板数据字典
        """
        if self.template_data:
            return self.template_data
        return self._get_default_template_data()
    
    def _get_default_template_data(self) -> dict:
        """
        获取默认模板数据结构
        
        Returns:
            dict: 默认模板数据
        """
        return {
            "title": "",
            "description": "",
            "requirements": "",
            "billing_method": "fixed",
            "unit_price": 0.0,
            "total_amount": 0.0,
            "deposit_ratio": 0.3,
            "estimated_hours": 0.0,
            "tech_stack": "",
            "deliverables": "",
            "tags": "",
            "priority": 3,
            "custom_fields": {}
        }
    
    def update_template_data(self, data: dict) -> None:
        """
        更新模板数据
        
        Args:
            data: 新的模板数据
        """
        current_data = self.get_template_data()
        current_data.update(data)
        self.template_data = current_data
    
    def create_order_from_template(self, session, customer_id: str, 
                                  overrides: dict = None) -> "Order":
        """
        从模板创建订单
        
        Args:
            session: 数据库会话
            customer_id: 客户ID
            overrides: 覆盖的字段值
            
        Returns:
            Order: 创建的订单对象
        """
        from app.models.order import Order
        
        # 获取模板数据
        template_data = self.get_template_data()
        
        # 应用覆盖值
        if overrides:
            template_data.update(overrides)
        
        # 创建订单
        order_data = {
            "customer_id": customer_id,
            "category_id": self.category_id,
            "title": template_data.get("title", ""),
            "description": template_data.get("description", ""),
            "requirements": template_data.get("requirements", ""),
            "billing_method": template_data.get("billing_method", "fixed"),
            "unit_price": template_data.get("unit_price", 0.0),
            "total_amount": template_data.get("total_amount", 0.0),
            "deposit_ratio": template_data.get("deposit_ratio", 0.3),
            "estimated_hours": template_data.get("estimated_hours", 0.0),
            "tech_stack": template_data.get("tech_stack", ""),
            "deliverables": template_data.get("deliverables", ""),
            "tags": template_data.get("tags", ""),
            "priority": template_data.get("priority", 3)
        }
        
        # 过滤掉None值
        order_data = {k: v for k, v in order_data.items() if v is not None}
        
        order = Order(**order_data)
        
        # 增加使用次数
        self.usage_count += 1
        
        return order
    
    def get_tags_list(self) -> list[str]:
        """
        获取标签列表
        
        Returns:
            list: 标签列表
        """
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []
    
    def add_tag(self, tag: str) -> None:
        """
        添加标签
        
        Args:
            tag: 要添加的标签
        """
        tags = self.get_tags_list()
        if tag not in tags:
            tags.append(tag)
            self.tags = ', '.join(tags)
    
    def remove_tag(self, tag: str) -> None:
        """
        移除标签
        
        Args:
            tag: 要移除的标签
        """
        tags = self.get_tags_list()
        if tag in tags:
            tags.remove(tag)
            self.tags = ', '.join(tags)
    
    def clone(self, new_name: str) -> "Template":
        """
        克隆模板
        
        Args:
            new_name: 新模板名称
            
        Returns:
            Template: 克隆的模板对象
        """
        return Template(
            name=new_name,
            description=f"克隆自: {self.name}",
            category_id=self.category_id,
            template_data=self.template_data.copy() if self.template_data else None,
            tags=self.tags,
            sort_order=self.sort_order
        )
    
    @classmethod
    def get_by_category(cls, session, category_id: str, active_only: bool = True):
        """
        根据分类获取模板列表
        
        Args:
            session: 数据库会话
            category_id: 分类ID
            active_only: 是否只获取启用的模板
            
        Returns:
            list: 模板列表
        """
        query = session.query(cls).filter(cls.category_id == category_id)
        
        if active_only:
            query = query.filter(cls.is_active == True)
        
        return query.order_by(cls.sort_order, cls.name).all()
    
    @classmethod
    def get_default_templates(cls, session):
        """
        获取默认模板列表
        
        Args:
            session: 数据库会话
            
        Returns:
            list: 默认模板列表
        """
        return session.query(cls).filter(
            cls.is_default == True,
            cls.is_active == True
        ).order_by(cls.sort_order, cls.name).all()
    
    @classmethod
    def get_popular_templates(cls, session, limit: int = 10):
        """
        获取热门模板列表
        
        Args:
            session: 数据库会话
            limit: 返回数量限制
            
        Returns:
            list: 热门模板列表
        """
        return session.query(cls).filter(
            cls.is_active == True
        ).order_by(cls.usage_count.desc()).limit(limit).all()
    
    @classmethod
    def search_templates(cls, session, keyword: str):
        """
        搜索模板
        
        Args:
            session: 数据库会话
            keyword: 搜索关键词
            
        Returns:
            list: 匹配的模板列表
        """
        return session.query(cls).filter(
            (cls.name.contains(keyword) |
             cls.description.contains(keyword) |
             cls.tags.contains(keyword)),
            cls.is_active == True
        ).order_by(cls.usage_count.desc()).all()
    
    @classmethod
    def create_default_templates(cls, session):
        """
        创建默认模板

        Args:
            session: 数据库会话
        """
        from app.models.category import Category

        # 获取分类
        web_dev_category = session.query(Category).filter(Category.name == "Web开发").first()
        academic_category = session.query(Category).filter(Category.name == "学术论文").first()

        # 只有当分类存在时才创建模板
        default_templates = []

        if web_dev_category:
            default_templates.append({
                "name": "Web开发项目",
                "description": "标准Web开发项目模板",
                "category_id": web_dev_category.id,
                "template_data": {
                    "title": "Web开发项目",
                    "description": "开发响应式Web应用",
                    "billing_method": "fixed",
                    "unit_price": 5000.0,
                    "total_amount": 5000.0,
                    "deposit_ratio": 0.3,
                    "estimated_hours": 40.0,
                    "tech_stack": "HTML, CSS, JavaScript, Python, Django",
                    "deliverables": "源代码, 部署文档, 用户手册",
                    "priority": 3
                },
                "is_default": True,
                "sort_order": 1
            })

        if academic_category:
            default_templates.append({
                "name": "学术论文写作",
                "description": "学术论文写作服务模板",
                "category_id": academic_category.id,
                "template_data": {
                    "title": "学术论文写作",
                    "description": "专业学术论文写作服务",
                    "billing_method": "word_count",
                    "unit_price": 0.5,
                    "estimated_hours": 20.0,
                    "deliverables": "论文正文, 参考文献, 查重报告",
                    "priority": 3
                },
                "is_default": True,
                "sort_order": 2
            })

        for template_data in default_templates:
            # 检查是否已存在
            existing = session.query(cls).filter(cls.name == template_data["name"]).first()
            if not existing:
                template = cls(**template_data)
                session.add(template)

        session.commit()
