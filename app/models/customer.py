"""
客户数据模型

定义客户信息的数据结构和业务逻辑
包含客户基本信息、联系方式、偏好设置等
"""

from sqlalchemy import Column, String, Integer, Text, JSON, Date, DECIMAL, Boolean
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, CustomerSource

class Customer(BaseModel):
    """
    客户信息模型
    
    存储客户的基本信息、联系方式、偏好设置和统计数据
    支持客户来源跟踪和满意度评级
    """
    
    __tablename__ = "customers"
    __table_args__ = {'comment': '客户信息表'}
    
    # 基本信息
    name = Column(
        String(100),
        nullable=False,
        index=True,
        comment="客户姓名"
    )
    
    # 联系方式
    contact_phone = Column(
        String(20),
        nullable=True,
        comment="联系电话"
    )
    
    contact_email = Column(
        String(100),
        nullable=True,
        unique=True,  # 邮箱唯一性约束
        comment="邮箱地址"
    )
    
    contact_wechat = Column(
        String(50),
        nullable=True,
        comment="微信号"
    )
    
    contact_qq = Column(
        String(20),
        nullable=True,
        comment="QQ号"
    )
    
    # 客户属性
    source = Column(
        String(20),
        nullable=False,
        default=CustomerSource.DIRECT,
        index=True,
        comment="客户来源"
    )
    
    company = Column(
        String(200),
        nullable=True,
        comment="所属公司/机构"
    )
    
    position = Column(
        String(100),
        nullable=True,
        comment="职位"
    )
    
    # 偏好设置（JSON格式存储）
    preferences = Column(
        JSON,
        nullable=True,
        comment="客户偏好设置"
    )
    
    # 备注信息
    notes = Column(
        Text,
        nullable=True,
        comment="客户备注"
    )
    
    # 评级和统计
    satisfaction_rating = Column(
        Integer,
        nullable=True,
        comment="满意度评级(1-5)"
    )
    
    total_orders = Column(
        Integer,
        nullable=False,
        default=0,
        comment="总订单数"
    )
    
    total_amount = Column(
        DECIMAL(12, 2),
        nullable=False,
        default=0.00,
        comment="总交易金额"
    )
    
    last_order_date = Column(
        Date,
        nullable=True,
        index=True,
        comment="最后订单日期"
    )
    
    # 状态标记
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否活跃客户"
    )
    
    is_vip = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否VIP客户"
    )
    
    # 关联关系
    orders = relationship(
        "Order",
        back_populates="customer",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    def __repr__(self) -> str:
        return f"<Customer(id={self.id}, name={self.name}, source={self.source})>"
    
    @property
    def display_name(self) -> str:
        """
        显示名称
        
        Returns:
            str: 客户显示名称
        """
        if self.company:
            return f"{self.name} ({self.company})"
        return self.name
    
    @property
    def primary_contact(self) -> str:
        """
        主要联系方式
        
        Returns:
            str: 主要联系方式
        """
        if self.contact_phone:
            return self.contact_phone
        elif self.contact_email:
            return self.contact_email
        elif self.contact_wechat:
            return f"微信: {self.contact_wechat}"
        elif self.contact_qq:
            return f"QQ: {self.contact_qq}"
        return "无联系方式"
    
    @property
    def average_order_amount(self) -> float:
        """
        平均订单金额
        
        Returns:
            float: 平均订单金额
        """
        if self.total_orders > 0:
            return float(self.total_amount) / self.total_orders
        return 0.0
    
    def get_preferences(self) -> dict:
        """
        获取客户偏好设置
        
        Returns:
            dict: 偏好设置字典
        """
        if self.preferences:
            return self.preferences
        return {
            "preferred_categories": [],      # 偏好的项目类型
            "preferred_billing": "fixed",    # 偏好的计费方式
            "budget_range": {"min": 0, "max": 0},  # 预算范围
            "communication_preference": "wechat",   # 沟通偏好
            "delivery_preference": "email",         # 交付偏好
            "special_requirements": ""              # 特殊要求
        }
    
    def update_preferences(self, preferences: dict) -> None:
        """
        更新客户偏好设置
        
        Args:
            preferences: 新的偏好设置
        """
        current_prefs = self.get_preferences()
        current_prefs.update(preferences)
        self.preferences = current_prefs
    
    def update_statistics(self) -> None:
        """
        更新客户统计信息
        
        根据关联的订单更新统计数据
        """
        from app.models.order import Order
        
        # 计算总订单数和总金额
        orders = self.orders.all()
        self.total_orders = len(orders)
        self.total_amount = sum(order.actual_amount or 0 for order in orders)
        
        # 更新最后订单日期
        if orders:
            latest_order = max(orders, key=lambda x: x.created_at)
            self.last_order_date = latest_order.created_at.date()
        
        # 更新VIP状态（总金额超过10000或订单数超过10）
        self.is_vip = (
            float(self.total_amount) >= 10000.0 or 
            self.total_orders >= 10
        )
    
    def calculate_satisfaction_score(self) -> float:
        """
        计算综合满意度得分
        
        Returns:
            float: 满意度得分(0-100)
        """
        if not self.satisfaction_rating:
            return 0.0
        
        # 基础满意度得分
        base_score = (self.satisfaction_rating / 5.0) * 100
        
        # 根据合作次数调整权重
        cooperation_bonus = min(self.total_orders * 2, 20)  # 最多加20分
        
        return min(base_score + cooperation_bonus, 100.0)
    
    @classmethod
    def get_vip_customers(cls, session):
        """
        获取VIP客户列表
        
        Args:
            session: 数据库会话
            
        Returns:
            list: VIP客户列表
        """
        return session.query(cls).filter(cls.is_vip == True).all()
    
    @classmethod
    def get_by_source(cls, session, source: str):
        """
        根据来源获取客户列表
        
        Args:
            session: 数据库会话
            source: 客户来源
            
        Returns:
            list: 客户列表
        """
        return session.query(cls).filter(cls.source == source).all()
    
    @classmethod
    def search_customers(cls, session, keyword: str):
        """
        搜索客户
        
        Args:
            session: 数据库会话
            keyword: 搜索关键词
            
        Returns:
            list: 匹配的客户列表
        """
        return session.query(cls).filter(
            cls.name.contains(keyword) |
            cls.company.contains(keyword) |
            cls.contact_email.contains(keyword)
        ).all()
