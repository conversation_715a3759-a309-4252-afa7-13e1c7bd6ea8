"""
工时记录数据模型

定义工时记录的数据结构和业务逻辑
支持精确的时间跟踪和工作效率分析
"""

from datetime import datetime, timedelta
from sqlalchemy import Column, String, Text, DECIMAL, Integer, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class WorkLog(BaseModel):
    """
    工时记录模型
    
    记录每个订单的具体工作时间和内容
    支持计时器功能和工作效率分析
    """
    
    __tablename__ = "work_logs"
    __table_args__ = {'comment': '工时记录表'}
    
    # 关联信息
    order_id = Column(
        String(36),
        ForeignKey("orders.id"),
        nullable=False,
        index=True,
        comment="订单ID"
    )
    
    # 时间信息
    start_time = Column(
        DateTime,
        nullable=False,
        index=True,
        comment="开始时间"
    )
    
    end_time = Column(
        DateTime,
        nullable=True,
        comment="结束时间"
    )
    
    duration = Column(
        Integer,
        nullable=False,
        default=0,
        comment="工作时长(分钟)"
    )
    
    # 工作信息
    description = Column(
        Text,
        nullable=True,
        comment="工作内容描述"
    )
    
    work_type = Column(
        String(50),
        nullable=True,
        comment="工作类型"
    )
    
    # 计费信息
    hourly_rate = Column(
        DECIMAL(8, 2),
        nullable=True,
        comment="时薪"
    )
    
    billable = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否计费"
    )

    # 状态信息
    is_running = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否正在计时"
    )
    
    # 其他信息
    notes = Column(
        Text,
        nullable=True,
        comment="备注"
    )
    
    # 关联关系
    order = relationship(
        "Order",
        back_populates="work_logs"
    )
    
    def __repr__(self) -> str:
        return f"<WorkLog(id={self.id}, order_id={self.order_id}, duration={self.duration})>"
    
    @property
    def duration_hours(self) -> float:
        """
        工作时长（小时）
        
        Returns:
            float: 工作时长（小时）
        """
        return self.duration / 60.0
    
    @property
    def duration_display(self) -> str:
        """
        工作时长显示格式
        
        Returns:
            str: 格式化的工作时长
        """
        hours = self.duration // 60
        minutes = self.duration % 60
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        else:
            return f"{minutes}分钟"
    
    @property
    def earnings(self) -> float:
        """
        本次工作收入
        
        Returns:
            float: 收入金额
        """
        if self.hourly_rate and self.billable:
            return float(self.hourly_rate) * self.duration_hours
        return 0.0
    
    @property
    def work_date(self) -> str:
        """
        工作日期
        
        Returns:
            str: 工作日期
        """
        return self.start_time.strftime("%Y-%m-%d")
    
    def start_timer(self) -> None:
        """
        开始计时
        
        设置开始时间并标记为正在计时
        """
        self.start_time = datetime.now()
        self.is_running = True
        self.end_time = None
        self.duration = 0
    
    def stop_timer(self) -> None:
        """
        停止计时
        
        计算工作时长并更新记录
        """
        if self.is_running and self.start_time:
            self.end_time = datetime.now()
            self.is_running = False
            
            # 计算工作时长（分钟）
            delta = self.end_time - self.start_time
            self.duration = int(delta.total_seconds() / 60)
    
    def pause_timer(self) -> None:
        """
        暂停计时
        
        保存当前已工作的时长
        """
        if self.is_running and self.start_time:
            current_time = datetime.now()
            delta = current_time - self.start_time
            additional_minutes = int(delta.total_seconds() / 60)
            self.duration += additional_minutes
            self.is_running = False
    
    def resume_timer(self) -> None:
        """
        恢复计时
        
        重新开始计时，保留之前的工作时长
        """
        self.start_time = datetime.now()
        self.is_running = True
        self.end_time = None
    
    def get_current_duration(self) -> int:
        """
        获取当前工作时长（包括正在计时的时间）
        
        Returns:
            int: 当前总工作时长（分钟）
        """
        total_duration = self.duration
        
        if self.is_running and self.start_time:
            current_time = datetime.now()
            delta = current_time - self.start_time
            additional_minutes = int(delta.total_seconds() / 60)
            total_duration += additional_minutes
        
        return total_duration
    
    def update_duration_manually(self, minutes: int) -> None:
        """
        手动更新工作时长
        
        Args:
            minutes: 工作时长（分钟）
        """
        self.duration = minutes
        self.is_running = False
        
        if self.start_time:
            self.end_time = self.start_time + timedelta(minutes=minutes)
    
    def calculate_efficiency_score(self) -> float:
        """
        计算工作效率得分
        
        基于工作时长、描述完整性等因素计算效率得分
        
        Returns:
            float: 效率得分(0-100)
        """
        base_score = 50.0
        
        # 工作时长因子（30分钟-4小时为最佳）
        if 30 <= self.duration <= 240:
            time_factor = 1.0
        elif self.duration < 30:
            time_factor = self.duration / 30.0
        else:
            time_factor = max(0.5, 240.0 / self.duration)
        
        # 描述完整性因子
        description_factor = 1.0
        if self.description:
            if len(self.description) >= 20:
                description_factor = 1.2
            elif len(self.description) >= 10:
                description_factor = 1.1
        else:
            description_factor = 0.8
        
        # 工作类型因子
        type_factor = 1.1 if self.work_type else 1.0
        
        return min(100.0, base_score * time_factor * description_factor * type_factor)
    
    @classmethod
    def get_by_order(cls, session, order_id: str):
        """
        根据订单获取工时记录
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            
        Returns:
            list: 工时记录列表
        """
        return session.query(cls).filter(
            cls.order_id == order_id
        ).order_by(cls.start_time.desc()).all()
    
    @classmethod
    def get_by_date_range(cls, session, start_date: datetime, end_date: datetime):
        """
        根据日期范围获取工时记录
        
        Args:
            session: 数据库会话
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            list: 工时记录列表
        """
        return session.query(cls).filter(
            cls.start_time >= start_date,
            cls.start_time <= end_date
        ).order_by(cls.start_time.desc()).all()
    
    @classmethod
    def get_running_timers(cls, session):
        """
        获取正在运行的计时器
        
        Args:
            session: 数据库会话
            
        Returns:
            list: 正在运行的工时记录列表
        """
        return session.query(cls).filter(cls.is_running == True).all()
    
    @classmethod
    def get_daily_summary(cls, session, date: datetime):
        """
        获取指定日期的工时汇总
        
        Args:
            session: 数据库会话
            date: 指定日期
            
        Returns:
            dict: 工时汇总信息
        """
        start_of_day = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)
        
        logs = session.query(cls).filter(
            cls.start_time >= start_of_day,
            cls.start_time < end_of_day
        ).all()
        
        total_duration = sum(log.duration for log in logs)
        billable_duration = sum(log.duration for log in logs if log.billable)
        total_earnings = sum(log.earnings for log in logs)
        
        return {
            "date": date.strftime("%Y-%m-%d"),
            "total_logs": len(logs),
            "total_duration": total_duration,
            "total_hours": total_duration / 60.0,
            "billable_duration": billable_duration,
            "billable_hours": billable_duration / 60.0,
            "total_earnings": total_earnings,
            "average_hourly_rate": total_earnings / (billable_duration / 60.0) if billable_duration > 0 else 0
        }
    
    @classmethod
    def stop_all_running_timers(cls, session):
        """
        停止所有正在运行的计时器
        
        Args:
            session: 数据库会话
            
        Returns:
            int: 停止的计时器数量
        """
        running_logs = cls.get_running_timers(session)
        
        for log in running_logs:
            log.stop_timer()
        
        session.commit()
        return len(running_logs)
