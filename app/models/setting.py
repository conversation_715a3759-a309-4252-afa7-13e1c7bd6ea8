"""
系统设置数据模型

定义系统配置的数据结构和业务逻辑
支持灵活的配置管理和用户偏好设置
"""

from sqlalchemy import Column, String, Text, Boolean
from app.models.base import BaseModel

class Setting(BaseModel):
    """
    系统设置模型
    
    存储系统的各种配置参数和用户偏好设置
    支持分类管理和动态配置
    """
    
    __tablename__ = "settings"
    __table_args__ = {'comment': '系统设置表'}
    
    # 配置信息
    config_key = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        comment="配置键"
    )
    
    config_value = Column(
        Text,
        nullable=True,
        comment="配置值"
    )
    
    default_value = Column(
        Text,
        nullable=True,
        comment="默认值"
    )
    
    # 描述信息
    description = Column(
        Text,
        nullable=True,
        comment="配置描述"
    )
    
    category = Column(
        String(50),
        nullable=False,
        default="general",
        index=True,
        comment="配置分类"
    )
    
    # 类型信息
    value_type = Column(
        String(20),
        nullable=False,
        default="string",
        comment="值类型"
    )
    
    # 状态信息
    is_system = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为系统配置"
    )
    
    is_readonly = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否只读"
    )
    
    is_encrypted = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否加密存储"
    )
    
    def __repr__(self) -> str:
        return f"<Setting(key={self.config_key}, value={self.config_value})>"
    
    @property
    def typed_value(self):
        """
        获取类型化的配置值
        
        Returns:
            Any: 根据value_type转换后的值
        """
        if self.config_value is None:
            return self.typed_default_value
        
        try:
            if self.value_type == "boolean":
                return self.config_value.lower() in ("true", "1", "yes", "on")
            elif self.value_type == "integer":
                return int(self.config_value)
            elif self.value_type == "float":
                return float(self.config_value)
            elif self.value_type == "json":
                import json
                return json.loads(self.config_value)
            else:
                return self.config_value
        except (ValueError, TypeError):
            return self.typed_default_value
    
    @property
    def typed_default_value(self):
        """
        获取类型化的默认值
        
        Returns:
            Any: 根据value_type转换后的默认值
        """
        if self.default_value is None:
            return None
        
        try:
            if self.value_type == "boolean":
                return self.default_value.lower() in ("true", "1", "yes", "on")
            elif self.value_type == "integer":
                return int(self.default_value)
            elif self.value_type == "float":
                return float(self.default_value)
            elif self.value_type == "json":
                import json
                return json.loads(self.default_value)
            else:
                return self.default_value
        except (ValueError, TypeError):
            return None
    
    def set_value(self, value) -> None:
        """
        设置配置值
        
        Args:
            value: 要设置的值
        """
        if self.is_readonly:
            raise ValueError(f"配置项 {self.config_key} 为只读，无法修改")
        
        if value is None:
            self.config_value = None
            return
        
        if self.value_type == "boolean":
            self.config_value = "true" if value else "false"
        elif self.value_type in ("integer", "float"):
            self.config_value = str(value)
        elif self.value_type == "json":
            import json
            self.config_value = json.dumps(value, ensure_ascii=False)
        else:
            self.config_value = str(value)
    
    def reset_to_default(self) -> None:
        """
        重置为默认值
        """
        if self.is_readonly:
            raise ValueError(f"配置项 {self.config_key} 为只读，无法重置")
        
        self.config_value = self.default_value
    
    @classmethod
    def get_value(cls, session, key: str, default=None):
        """
        获取配置值
        
        Args:
            session: 数据库会话
            key: 配置键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        setting = session.query(cls).filter(cls.config_key == key).first()
        if setting:
            return setting.typed_value
        return default
    
    @classmethod
    def set_value(cls, session, key: str, value, description: str = None, 
                  category: str = "general", value_type: str = "string"):
        """
        设置配置值
        
        Args:
            session: 数据库会话
            key: 配置键
            value: 配置值
            description: 配置描述
            category: 配置分类
            value_type: 值类型
        """
        setting = session.query(cls).filter(cls.config_key == key).first()
        
        if setting:
            setting.set_value(value)
        else:
            setting = cls(
                config_key=key,
                description=description,
                category=category,
                value_type=value_type
            )
            setting.set_value(value)
            session.add(setting)
        
        session.commit()
    
    @classmethod
    def get_by_category(cls, session, category: str):
        """
        根据分类获取配置列表
        
        Args:
            session: 数据库会话
            category: 配置分类
            
        Returns:
            list: 配置列表
        """
        return session.query(cls).filter(cls.category == category).all()
    
    @classmethod
    def get_user_settings(cls, session):
        """
        获取用户设置列表
        
        Args:
            session: 数据库会话
            
        Returns:
            list: 用户设置列表
        """
        return session.query(cls).filter(cls.is_system == False).all()
    
    @classmethod
    def initialize_default_settings(cls, session):
        """
        初始化默认设置
        
        Args:
            session: 数据库会话
        """
        default_settings = [
            # 应用设置
            {
                "config_key": "app.name",
                "config_value": "兼职接单管理系统",
                "default_value": "兼职接单管理系统",
                "description": "应用名称",
                "category": "application",
                "value_type": "string",
                "is_system": True
            },
            {
                "config_key": "app.version",
                "config_value": "1.0.0",
                "default_value": "1.0.0",
                "description": "应用版本",
                "category": "application",
                "value_type": "string",
                "is_system": True,
                "is_readonly": True
            },
            
            # 界面设置
            {
                "config_key": "ui.theme",
                "config_value": "light",
                "default_value": "light",
                "description": "界面主题",
                "category": "interface",
                "value_type": "string"
            },
            {
                "config_key": "ui.language",
                "config_value": "zh_CN",
                "default_value": "zh_CN",
                "description": "界面语言",
                "category": "interface",
                "value_type": "string"
            },
            {
                "config_key": "ui.auto_save",
                "config_value": "true",
                "default_value": "true",
                "description": "自动保存",
                "category": "interface",
                "value_type": "boolean"
            },
            
            # 提醒设置
            {
                "config_key": "notification.deadline_reminder",
                "config_value": "true",
                "default_value": "true",
                "description": "截止日期提醒",
                "category": "notification",
                "value_type": "boolean"
            },
            {
                "config_key": "notification.reminder_days",
                "config_value": "3",
                "default_value": "3",
                "description": "提前提醒天数",
                "category": "notification",
                "value_type": "integer"
            },
            {
                "config_key": "notification.sound_enabled",
                "config_value": "true",
                "default_value": "true",
                "description": "声音提醒",
                "category": "notification",
                "value_type": "boolean"
            },
            
            # 数据设置
            {
                "config_key": "data.auto_backup",
                "config_value": "true",
                "default_value": "true",
                "description": "自动备份",
                "category": "data",
                "value_type": "boolean"
            },
            {
                "config_key": "data.backup_interval",
                "config_value": "7",
                "default_value": "7",
                "description": "备份间隔（天）",
                "category": "data",
                "value_type": "integer"
            },
            {
                "config_key": "data.max_backups",
                "config_value": "10",
                "default_value": "10",
                "description": "最大备份数量",
                "category": "data",
                "value_type": "integer"
            },
            
            # 业务设置
            {
                "config_key": "business.default_deposit_ratio",
                "config_value": "0.3",
                "default_value": "0.3",
                "description": "默认定金比例",
                "category": "business",
                "value_type": "float"
            },
            {
                "config_key": "business.default_hourly_rate",
                "config_value": "100.0",
                "default_value": "100.0",
                "description": "默认时薪",
                "category": "business",
                "value_type": "float"
            },
            {
                "config_key": "business.currency",
                "config_value": "CNY",
                "default_value": "CNY",
                "description": "货币单位",
                "category": "business",
                "value_type": "string"
            }
        ]
        
        for setting_data in default_settings:
            # 检查是否已存在
            existing = session.query(cls).filter(
                cls.config_key == setting_data["config_key"]
            ).first()
            
            if not existing:
                setting = cls(**setting_data)
                session.add(setting)
        
        session.commit()

# 配置分类枚举
class SettingCategory:
    """配置分类枚举"""
    APPLICATION = "application"    # 应用设置
    INTERFACE = "interface"        # 界面设置
    NOTIFICATION = "notification"  # 提醒设置
    DATA = "data"                 # 数据设置
    BUSINESS = "business"         # 业务设置
    SECURITY = "security"         # 安全设置
    GENERAL = "general"           # 一般设置
    
    @classmethod
    def get_all_categories(cls) -> list[str]:
        """获取所有配置分类列表"""
        return [
            cls.APPLICATION, cls.INTERFACE, cls.NOTIFICATION,
            cls.DATA, cls.BUSINESS, cls.SECURITY, cls.GENERAL
        ]

# 值类型枚举
class ValueType:
    """值类型枚举"""
    STRING = "string"      # 字符串
    INTEGER = "integer"    # 整数
    FLOAT = "float"        # 浮点数
    BOOLEAN = "boolean"    # 布尔值
    JSON = "json"          # JSON对象
    
    @classmethod
    def get_all_types(cls) -> list[str]:
        """获取所有值类型列表"""
        return [cls.STRING, cls.INTEGER, cls.FLOAT, cls.BOOLEAN, cls.JSON]
