"""
订单数据模型

定义订单信息的数据结构和业务逻辑
包含订单基本信息、计费信息、状态管理等
"""

from datetime import datetime, date
from sqlalchemy import Column, String, Text, DECIMAL, Integer, Date, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, OrderStatus, BillingMethod

class Order(BaseModel):
    """
    订单信息模型
    
    存储订单的完整信息，包括基本信息、客户信息、计费信息、时间信息等
    支持多种计费方式和完整的状态流转管理
    """
    
    __tablename__ = "orders"
    __table_args__ = {'comment': '订单信息表'}
    
    # 基本信息
    title = Column(
        String(200),
        nullable=False,
        index=True,
        comment="订单标题"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="项目描述"
    )
    
    requirements = Column(
        Text,
        nullable=True,
        comment="客户要求"
    )
    
    # 关联信息
    customer_id = Column(
        String(36),
        ForeignKey("customers.id"),
        nullable=False,
        index=True,
        comment="客户ID"
    )
    
    category_id = Column(
        String(36),
        ForeignKey("categories.id"),
        nullable=False,
        index=True,
        comment="项目分类ID"
    )
    
    # 状态信息
    status = Column(
        String(20),
        nullable=False,
        default=OrderStatus.PENDING,
        index=True,
        comment="订单状态"
    )
    
    priority = Column(
        Integer,
        nullable=False,
        default=3,
        comment="优先级(1-5，5最高)"
    )
    
    # 计费信息
    billing_method = Column(
        String(20),
        nullable=False,
        default=BillingMethod.FIXED,
        comment="计费方式"
    )
    
    unit_price = Column(
        DECIMAL(10, 2),
        nullable=True,
        comment="单价"
    )
    
    quantity = Column(
        DECIMAL(10, 2),
        nullable=True,
        comment="数量（小时数/字数/页数等）"
    )
    
    total_amount = Column(
        DECIMAL(10, 2),
        nullable=False,
        default=0.00,
        comment="总金额"
    )
    
    # 财务信息
    deposit_ratio = Column(
        DECIMAL(3, 2),
        nullable=True,
        comment="定金比例"
    )
    
    deposit_amount = Column(
        DECIMAL(10, 2),
        nullable=True,
        comment="定金金额"
    )
    
    final_amount = Column(
        DECIMAL(10, 2),
        nullable=True,
        comment="尾款金额"
    )
    
    actual_amount = Column(
        DECIMAL(10, 2),
        nullable=True,
        comment="实际收款金额"
    )
    
    invoice_required = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否需要发票"
    )
    
    # 时间信息
    start_date = Column(
        Date,
        nullable=True,
        comment="开始日期"
    )
    
    deadline = Column(
        DateTime,
        nullable=True,
        index=True,
        comment="截止日期"
    )
    
    completed_at = Column(
        DateTime,
        nullable=True,
        comment="完成时间"
    )
    
    settled_at = Column(
        DateTime,
        nullable=True,
        comment="结算时间"
    )
    
    # 项目信息
    tech_stack = Column(
        String(500),
        nullable=True,
        comment="技术栈/专业领域"
    )
    
    deliverables = Column(
        Text,
        nullable=True,
        comment="交付物清单"
    )
    
    estimated_hours = Column(
        DECIMAL(6, 2),
        nullable=True,
        comment="预估工时"
    )
    
    actual_hours = Column(
        DECIMAL(6, 2),
        nullable=True,
        comment="实际工时"
    )
    
    # 其他信息
    notes = Column(
        Text,
        nullable=True,
        comment="备注信息"
    )
    
    tags = Column(
        String(500),
        nullable=True,
        comment="标签（逗号分隔）"
    )
    
    # 关联关系
    customer = relationship(
        "Customer",
        back_populates="orders"
    )
    
    category = relationship(
        "Category",
        back_populates="orders"
    )
    
    work_logs = relationship(
        "WorkLog",
        back_populates="order",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    attachments = relationship(
        "Attachment",
        back_populates="order",
        cascade="all, delete-orphan"
    )
    
    communications = relationship(
        "Communication",
        back_populates="order",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Order(id={self.id}, title={self.title}, status={self.status})>"
    
    @property
    def display_title(self) -> str:
        """
        显示标题
        
        Returns:
            str: 订单显示标题
        """
        if self.customer:
            return f"{self.title} - {self.customer.name}"
        return self.title
    
    @property
    def is_overdue(self) -> bool:
        """
        是否已逾期
        
        Returns:
            bool: 是否已逾期
        """
        if not self.deadline:
            return False
        
        if self.status in [OrderStatus.COMPLETED, OrderStatus.SETTLED, OrderStatus.CANCELLED]:
            return False
        
        return datetime.now() > self.deadline
    
    @property
    def days_until_deadline(self) -> int:
        """
        距离截止日期的天数
        
        Returns:
            int: 距离截止日期的天数（负数表示已逾期）
        """
        if not self.deadline:
            return 999
        
        delta = self.deadline.date() - date.today()
        return delta.days
    
    @property
    def progress_percentage(self) -> float:
        """
        项目进度百分比
        
        Returns:
            float: 进度百分比(0-100)
        """
        status_progress = {
            OrderStatus.PENDING: 0,
            OrderStatus.IN_PROGRESS: 30,
            OrderStatus.REVISING: 60,
            OrderStatus.REVIEWING: 80,
            OrderStatus.COMPLETED: 100,
            OrderStatus.SETTLED: 100,
            OrderStatus.CANCELLED: 0,
            OrderStatus.PAUSED: 20
        }
        return status_progress.get(self.status, 0)
    
    @property
    def hourly_rate(self) -> float:
        """
        实际时薪
        
        Returns:
            float: 实际时薪
        """
        if self.actual_hours and self.actual_hours > 0 and self.actual_amount:
            return float(self.actual_amount) / float(self.actual_hours)
        return 0.0
    
    def calculate_amounts(self) -> None:
        """
        计算金额信息
        
        根据计费方式和数量计算总金额、定金、尾款
        """
        if self.billing_method == BillingMethod.FIXED:
            # 固定价格，total_amount已设置
            pass
        elif self.billing_method == BillingMethod.HOURLY and self.unit_price and self.quantity:
            # 按小时计费
            self.total_amount = self.unit_price * self.quantity
        elif self.billing_method in [BillingMethod.WORD_COUNT, BillingMethod.PAGE_COUNT]:
            # 按字数或页数计费
            if self.unit_price and self.quantity:
                self.total_amount = self.unit_price * self.quantity
        
        # 计算定金和尾款
        if self.deposit_ratio and self.total_amount:
            self.deposit_amount = self.total_amount * self.deposit_ratio
            self.final_amount = self.total_amount - self.deposit_amount
    
    def update_status(self, new_status: str, notes: str = None) -> bool:
        """
        更新订单状态
        
        Args:
            new_status: 新状态
            notes: 状态变更备注
            
        Returns:
            bool: 是否更新成功
        """
        if new_status not in OrderStatus.get_all_statuses():
            return False
        
        old_status = self.status
        self.status = new_status
        
        # 设置完成时间
        if new_status == OrderStatus.COMPLETED and old_status != OrderStatus.COMPLETED:
            self.completed_at = datetime.now()
        
        # 设置结算时间
        if new_status == OrderStatus.SETTLED and old_status != OrderStatus.SETTLED:
            self.settled_at = datetime.now()
        
        # 记录状态变更（可以在这里添加日志记录）
        if notes:
            self.notes = f"{self.notes or ''}\n[{datetime.now().strftime('%Y-%m-%d %H:%M')}] 状态变更: {old_status} -> {new_status}. {notes}"
        
        return True
    
    def calculate_work_hours(self) -> float:
        """
        计算总工时
        
        Returns:
            float: 总工时（小时）
        """
        total_minutes = sum(log.duration for log in self.work_logs.all())
        return total_minutes / 60.0
    
    def update_actual_hours(self) -> None:
        """
        更新实际工时
        
        根据工时记录更新实际工时
        """
        self.actual_hours = self.calculate_work_hours()
    
    def get_tags_list(self) -> list[str]:
        """
        获取标签列表
        
        Returns:
            list: 标签列表
        """
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []
    
    def add_tag(self, tag: str) -> None:
        """
        添加标签
        
        Args:
            tag: 要添加的标签
        """
        tags = self.get_tags_list()
        if tag not in tags:
            tags.append(tag)
            self.tags = ', '.join(tags)
    
    def remove_tag(self, tag: str) -> None:
        """
        移除标签
        
        Args:
            tag: 要移除的标签
        """
        tags = self.get_tags_list()
        if tag in tags:
            tags.remove(tag)
            self.tags = ', '.join(tags)
    
    @classmethod
    def get_active_orders(cls, session):
        """
        获取活跃订单列表
        
        Args:
            session: 数据库会话
            
        Returns:
            list: 活跃订单列表
        """
        return session.query(cls).filter(
            cls.status.in_(OrderStatus.get_active_statuses())
        ).order_by(cls.deadline.asc()).all()
    
    @classmethod
    def get_overdue_orders(cls, session):
        """
        获取逾期订单列表
        
        Args:
            session: 数据库会话
            
        Returns:
            list: 逾期订单列表
        """
        return session.query(cls).filter(
            cls.deadline < datetime.now(),
            cls.status.in_(OrderStatus.get_active_statuses())
        ).order_by(cls.deadline.asc()).all()
    
    @classmethod
    def get_by_customer(cls, session, customer_id: str):
        """
        根据客户获取订单列表
        
        Args:
            session: 数据库会话
            customer_id: 客户ID
            
        Returns:
            list: 订单列表
        """
        return session.query(cls).filter(cls.customer_id == customer_id).order_by(cls.created_at.desc()).all()
