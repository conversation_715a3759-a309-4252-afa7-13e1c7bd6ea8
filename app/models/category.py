"""
项目分类数据模型

定义项目分类的数据结构和业务逻辑
支持分层分类、计费方式配置和模板管理
"""

from sqlalchemy import Column, String, Text, DECIMAL, Boolean, Integer, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, CategoryType, BillingMethod, OrderableMixin

class Category(BaseModel, OrderableMixin):
    """
    项目分类模型
    
    支持分层分类结构，每个分类可以配置默认的计费方式和费率
    用于订单分类管理和快速创建模板
    """
    
    __tablename__ = "categories"
    __table_args__ = {'comment': '项目分类表'}
    
    # 基本信息
    name = Column(
        String(100),
        nullable=False,
        index=True,
        comment="分类名称"
    )
    
    type = Column(
        String(20),
        nullable=False,
        default=CategoryType.DEVELOPMENT,
        index=True,
        comment="分类类型"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="分类描述"
    )
    
    # 分层结构
    parent_id = Column(
        String(36),
        ForeignKey("categories.id"),
        nullable=True,
        comment="父分类ID"
    )
    
    # 计费配置
    billing_method = Column(
        String(20),
        nullable=False,
        default=BillingMethod.FIXED,
        comment="默认计费方式"
    )
    
    default_rate = Column(
        DECIMAL(8, 2),
        nullable=True,
        comment="默认费率"
    )
    
    # 状态和配置
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否启用"
    )
    
    allow_subcategories = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否允许子分类"
    )
    
    # 统计信息
    order_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="订单数量"
    )
    
    total_revenue = Column(
        DECIMAL(12, 2),
        nullable=False,
        default=0.00,
        comment="总收入"
    )
    
    # 关联关系
    parent = relationship(
        "Category",
        remote_side="Category.id",
        back_populates="children"
    )
    
    children = relationship(
        "Category",
        back_populates="parent",
        cascade="all, delete-orphan"
    )
    
    orders = relationship(
        "Order",
        back_populates="category",
        lazy="dynamic"
    )
    
    templates = relationship(
        "Template",
        back_populates="category",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Category(id={self.id}, name={self.name}, type={self.type})>"
    
    @property
    def full_name(self) -> str:
        """
        完整分类名称（包含父分类）
        
        Returns:
            str: 完整分类名称
        """
        if self.parent:
            return f"{self.parent.full_name} > {self.name}"
        return self.name
    
    @property
    def level(self) -> int:
        """
        分类层级
        
        Returns:
            int: 分类层级（从0开始）
        """
        if self.parent:
            return self.parent.level + 1
        return 0
    
    @property
    def is_leaf(self) -> bool:
        """
        是否为叶子节点（没有子分类）
        
        Returns:
            bool: 是否为叶子节点
        """
        return len(self.children) == 0
    
    @property
    def average_order_amount(self) -> float:
        """
        平均订单金额
        
        Returns:
            float: 平均订单金额
        """
        if self.order_count > 0:
            return float(self.total_revenue) / self.order_count
        return 0.0
    
    def get_all_descendants(self) -> list["Category"]:
        """
        获取所有后代分类
        
        Returns:
            list: 所有后代分类列表
        """
        descendants = []
        for child in self.children:
            descendants.append(child)
            descendants.extend(child.get_all_descendants())
        return descendants
    
    def get_path_to_root(self) -> list["Category"]:
        """
        获取到根分类的路径
        
        Returns:
            list: 从当前分类到根分类的路径
        """
        path = [self]
        current = self.parent
        while current:
            path.insert(0, current)
            current = current.parent
        return path
    
    def update_statistics(self) -> None:
        """
        更新分类统计信息
        
        根据关联的订单更新统计数据
        """
        orders = self.orders.all()
        self.order_count = len(orders)
        self.total_revenue = sum(order.actual_amount or 0 for order in orders)
    
    def can_delete(self) -> tuple[bool, str]:
        """
        检查是否可以删除
        
        Returns:
            tuple: (是否可删除, 原因说明)
        """
        if self.children:
            return False, "存在子分类，无法删除"
        
        if self.order_count > 0:
            return False, "存在关联订单，无法删除"
        
        if self.templates:
            return False, "存在关联模板，无法删除"
        
        return True, "可以删除"
    
    @classmethod
    def get_root_categories(cls, session):
        """
        获取根分类列表
        
        Args:
            session: 数据库会话
            
        Returns:
            list: 根分类列表
        """
        return session.query(cls).filter(cls.parent_id.is_(None)).order_by(cls.sort_order).all()
    
    @classmethod
    def get_by_type(cls, session, category_type: str):
        """
        根据类型获取分类列表
        
        Args:
            session: 数据库会话
            category_type: 分类类型
            
        Returns:
            list: 分类列表
        """
        return session.query(cls).filter(cls.type == category_type).order_by(cls.sort_order).all()
    
    @classmethod
    def get_active_categories(cls, session):
        """
        获取活跃分类列表
        
        Args:
            session: 数据库会话
            
        Returns:
            list: 活跃分类列表
        """
        return session.query(cls).filter(cls.is_active == True).order_by(cls.sort_order).all()
    
    @classmethod
    def build_tree(cls, categories: list["Category"]) -> list["Category"]:
        """
        构建分类树结构
        
        Args:
            categories: 分类列表
            
        Returns:
            list: 树形结构的分类列表
        """
        # 创建ID到分类的映射
        category_map = {cat.id: cat for cat in categories}
        
        # 构建树结构
        root_categories = []
        for category in categories:
            if category.parent_id is None:
                root_categories.append(category)
            else:
                parent = category_map.get(category.parent_id)
                if parent:
                    if not hasattr(parent, '_children_list'):
                        parent._children_list = []
                    parent._children_list.append(category)
        
        return root_categories
