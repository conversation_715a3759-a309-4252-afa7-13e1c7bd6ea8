"""
文件附件数据模型

定义文件附件的数据结构和业务逻辑
支持多种文件类型的存储和版本管理
"""

import os
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class Attachment(BaseModel):
    """
    文件附件模型
    
    存储订单相关的文件附件信息
    支持文件版本管理和安全访问控制
    """
    
    __tablename__ = "attachments"
    __table_args__ = {'comment': '文件附件表'}
    
    # 关联信息
    order_id = Column(
        String(36),
        ForeignKey("orders.id"),
        nullable=False,
        index=True,
        comment="订单ID"
    )
    
    # 文件信息
    file_name = Column(
        String(255),
        nullable=False,
        comment="文件名"
    )
    
    original_name = Column(
        String(255),
        nullable=False,
        comment="原始文件名"
    )
    
    file_path = Column(
        String(500),
        nullable=False,
        comment="文件路径"
    )
    
    file_type = Column(
        String(50),
        nullable=False,
        index=True,
        comment="文件类型"
    )
    
    file_size = Column(
        Integer,
        nullable=False,
        comment="文件大小(字节)"
    )
    
    mime_type = Column(
        String(100),
        nullable=True,
        comment="MIME类型"
    )
    
    # 分类信息
    category = Column(
        String(50),
        nullable=False,
        default="document",
        comment="文件分类"
    )
    
    # 版本信息
    version = Column(
        Integer,
        nullable=False,
        default=1,
        comment="文件版本"
    )
    
    parent_id = Column(
        String(36),
        ForeignKey("attachments.id"),
        nullable=True,
        comment="父文件ID（用于版本控制）"
    )
    
    # 描述信息
    description = Column(
        Text,
        nullable=True,
        comment="文件描述"
    )
    
    tags = Column(
        String(500),
        nullable=True,
        comment="文件标签"
    )
    
    # 状态信息
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否有效"
    )

    is_deliverable = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为交付物"
    )
    
    # 上传信息
    upload_time = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        comment="上传时间"
    )
    
    # 关联关系
    order = relationship(
        "Order",
        back_populates="attachments"
    )
    
    parent = relationship(
        "Attachment",
        remote_side="Attachment.id",
        back_populates="versions"
    )
    
    versions = relationship(
        "Attachment",
        back_populates="parent",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Attachment(id={self.id}, file_name={self.file_name}, order_id={self.order_id})>"
    
    @property
    def file_size_display(self) -> str:
        """
        文件大小显示格式
        
        Returns:
            str: 格式化的文件大小
        """
        size = self.file_size
        
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
    
    @property
    def file_extension(self) -> str:
        """
        文件扩展名
        
        Returns:
            str: 文件扩展名
        """
        return os.path.splitext(self.file_name)[1].lower()
    
    @property
    def is_image(self) -> bool:
        """
        是否为图片文件
        
        Returns:
            bool: 是否为图片
        """
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        return self.file_extension in image_extensions
    
    @property
    def is_document(self) -> bool:
        """
        是否为文档文件
        
        Returns:
            bool: 是否为文档
        """
        doc_extensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt']
        return self.file_extension in doc_extensions
    
    @property
    def is_code(self) -> bool:
        """
        是否为代码文件
        
        Returns:
            bool: 是否为代码
        """
        code_extensions = [
            '.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.h',
            '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.ts', '.vue',
            '.jsx', '.tsx', '.sql', '.json', '.xml', '.yaml', '.yml'
        ]
        return self.file_extension in code_extensions
    
    @property
    def is_archive(self) -> bool:
        """
        是否为压缩文件
        
        Returns:
            bool: 是否为压缩文件
        """
        archive_extensions = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']
        return self.file_extension in archive_extensions
    
    @property
    def full_path(self) -> str:
        """
        完整文件路径
        
        Returns:
            str: 完整文件路径
        """
        return os.path.join("data", "attachments", self.file_path)
    
    def exists(self) -> bool:
        """
        检查文件是否存在
        
        Returns:
            bool: 文件是否存在
        """
        return os.path.exists(self.full_path)
    
    def get_tags_list(self) -> list[str]:
        """
        获取标签列表
        
        Returns:
            list: 标签列表
        """
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []
    
    def add_tag(self, tag: str) -> None:
        """
        添加标签
        
        Args:
            tag: 要添加的标签
        """
        tags = self.get_tags_list()
        if tag not in tags:
            tags.append(tag)
            self.tags = ', '.join(tags)
    
    def remove_tag(self, tag: str) -> None:
        """
        移除标签
        
        Args:
            tag: 要移除的标签
        """
        tags = self.get_tags_list()
        if tag in tags:
            tags.remove(tag)
            self.tags = ', '.join(tags)
    
    def create_new_version(self, new_file_path: str, new_file_name: str, 
                          new_file_size: int, description: str = None) -> "Attachment":
        """
        创建新版本
        
        Args:
            new_file_path: 新文件路径
            new_file_name: 新文件名
            new_file_size: 新文件大小
            description: 版本描述
            
        Returns:
            Attachment: 新版本附件对象
        """
        new_version = Attachment(
            order_id=self.order_id,
            file_name=new_file_name,
            original_name=self.original_name,
            file_path=new_file_path,
            file_type=self.file_type,
            file_size=new_file_size,
            mime_type=self.mime_type,
            category=self.category,
            version=self.version + 1,
            parent_id=self.id if not self.parent_id else self.parent_id,
            description=description,
            tags=self.tags,
            is_deliverable=self.is_deliverable
        )
        
        return new_version
    
    def get_version_history(self) -> list["Attachment"]:
        """
        获取版本历史
        
        Returns:
            list: 版本历史列表
        """
        if self.parent_id:
            # 如果当前是子版本，获取父版本的所有子版本
            return self.parent.versions
        else:
            # 如果当前是父版本，返回所有子版本
            return self.versions
    
    def get_latest_version(self) -> "Attachment":
        """
        获取最新版本
        
        Returns:
            Attachment: 最新版本附件对象
        """
        if self.parent_id:
            # 如果当前是子版本，从父版本获取最新版本
            versions = self.parent.versions
        else:
            # 如果当前是父版本，从子版本中获取最新版本
            versions = self.versions
        
        if versions:
            return max(versions, key=lambda x: x.version)
        return self
    
    def soft_delete(self) -> None:
        """
        软删除文件
        
        标记文件为无效，但不删除物理文件
        """
        self.is_active = False
    
    def hard_delete(self) -> bool:
        """
        硬删除文件
        
        删除物理文件和数据库记录
        
        Returns:
            bool: 是否删除成功
        """
        try:
            if self.exists():
                os.remove(self.full_path)
            return True
        except OSError:
            return False
    
    @classmethod
    def get_by_order(cls, session, order_id: str, active_only: bool = True):
        """
        根据订单获取附件列表
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            active_only: 是否只获取有效附件
            
        Returns:
            list: 附件列表
        """
        query = session.query(cls).filter(cls.order_id == order_id)
        
        if active_only:
            query = query.filter(cls.is_active == True)
        
        return query.order_by(cls.upload_time.desc()).all()
    
    @classmethod
    def get_by_category(cls, session, category: str):
        """
        根据分类获取附件列表
        
        Args:
            session: 数据库会话
            category: 文件分类
            
        Returns:
            list: 附件列表
        """
        return session.query(cls).filter(
            cls.category == category,
            cls.is_active == True
        ).order_by(cls.upload_time.desc()).all()
    
    @classmethod
    def get_deliverables(cls, session, order_id: str = None):
        """
        获取交付物列表
        
        Args:
            session: 数据库会话
            order_id: 订单ID（可选）
            
        Returns:
            list: 交付物列表
        """
        query = session.query(cls).filter(
            cls.is_deliverable == True,
            cls.is_active == True
        )
        
        if order_id:
            query = query.filter(cls.order_id == order_id)
        
        return query.order_by(cls.upload_time.desc()).all()
    
    @classmethod
    def search_files(cls, session, keyword: str):
        """
        搜索文件
        
        Args:
            session: 数据库会话
            keyword: 搜索关键词
            
        Returns:
            list: 匹配的文件列表
        """
        return session.query(cls).filter(
            (cls.file_name.contains(keyword) |
             cls.original_name.contains(keyword) |
             cls.description.contains(keyword) |
             cls.tags.contains(keyword)),
            cls.is_active == True
        ).order_by(cls.upload_time.desc()).all()
