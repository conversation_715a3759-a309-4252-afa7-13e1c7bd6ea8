"""
沟通记录数据模型

定义客户沟通记录的数据结构和业务逻辑
支持多种沟通方式的记录和历史追踪
"""

from datetime import datetime
from sqlalchemy import Column, String, Text, DateTime, Integer, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class Communication(BaseModel):
    """
    沟通记录模型
    
    记录与客户的沟通历史，包括需求变更、进度汇报、问题反馈等
    支持多种沟通方式和内容分类
    """
    
    __tablename__ = "communications"
    __table_args__ = {'comment': '沟通记录表'}
    
    # 关联信息
    order_id = Column(
        String(36),
        ForeignKey("orders.id"),
        nullable=False,
        index=True,
        comment="订单ID"
    )
    
    # 沟通信息
    content = Column(
        Text,
        nullable=False,
        comment="沟通内容"
    )
    
    communication_type = Column(
        String(50),
        nullable=False,
        default="message",
        comment="沟通方式"
    )
    
    direction = Column(
        String(20),
        nullable=False,
        default="outgoing",
        comment="沟通方向"
    )
    
    # 分类信息
    category = Column(
        String(50),
        nullable=False,
        default="general",
        comment="沟通分类"
    )
    
    priority = Column(
        Integer,
        nullable=False,
        default=3,
        comment="重要程度(1-5)"
    )
    
    # 状态信息
    is_read = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否已读"
    )

    requires_action = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否需要行动"
    )

    is_resolved = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否已解决"
    )
    
    # 时间信息
    communication_time = Column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        index=True,
        comment="沟通时间"
    )
    
    # 其他信息
    attachments = Column(
        String(1000),
        nullable=True,
        comment="相关附件"
    )
    
    tags = Column(
        String(500),
        nullable=True,
        comment="标签"
    )
    
    # 关联关系
    order = relationship(
        "Order",
        back_populates="communications"
    )
    
    def __repr__(self) -> str:
        return f"<Communication(id={self.id}, order_id={self.order_id}, type={self.communication_type})>"
    
    @property
    def content_preview(self) -> str:
        """
        内容预览
        
        Returns:
            str: 内容预览（前100个字符）
        """
        if len(self.content) <= 100:
            return self.content
        return self.content[:100] + "..."
    
    @property
    def time_display(self) -> str:
        """
        时间显示格式
        
        Returns:
            str: 格式化的时间
        """
        return self.communication_time.strftime("%Y-%m-%d %H:%M")
    
    @property
    def is_recent(self) -> bool:
        """
        是否为最近的沟通（24小时内）
        
        Returns:
            bool: 是否为最近沟通
        """
        now = datetime.utcnow()
        delta = now - self.communication_time
        return delta.total_seconds() < 24 * 3600
    
    def get_tags_list(self) -> list[str]:
        """
        获取标签列表
        
        Returns:
            list: 标签列表
        """
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []
    
    def add_tag(self, tag: str) -> None:
        """
        添加标签
        
        Args:
            tag: 要添加的标签
        """
        tags = self.get_tags_list()
        if tag not in tags:
            tags.append(tag)
            self.tags = ', '.join(tags)
    
    def remove_tag(self, tag: str) -> None:
        """
        移除标签
        
        Args:
            tag: 要移除的标签
        """
        tags = self.get_tags_list()
        if tag in tags:
            tags.remove(tag)
            self.tags = ', '.join(tags)
    
    def mark_as_resolved(self) -> None:
        """
        标记为已解决
        """
        self.is_resolved = True
        self.requires_action = False
    
    def mark_as_unresolved(self) -> None:
        """
        标记为未解决
        """
        self.is_resolved = False
        self.requires_action = True
    
    @classmethod
    def get_by_order(cls, session, order_id: str):
        """
        根据订单获取沟通记录
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            
        Returns:
            list: 沟通记录列表
        """
        return session.query(cls).filter(
            cls.order_id == order_id
        ).order_by(cls.communication_time.desc()).all()
    
    @classmethod
    def get_unresolved(cls, session, order_id: str = None):
        """
        获取未解决的沟通记录
        
        Args:
            session: 数据库会话
            order_id: 订单ID（可选）
            
        Returns:
            list: 未解决的沟通记录列表
        """
        query = session.query(cls).filter(cls.is_resolved == False)
        
        if order_id:
            query = query.filter(cls.order_id == order_id)
        
        return query.order_by(cls.communication_time.desc()).all()
    
    @classmethod
    def get_requiring_action(cls, session, order_id: str = None):
        """
        获取需要行动的沟通记录
        
        Args:
            session: 数据库会话
            order_id: 订单ID（可选）
            
        Returns:
            list: 需要行动的沟通记录列表
        """
        query = session.query(cls).filter(cls.requires_action == True)
        
        if order_id:
            query = query.filter(cls.order_id == order_id)
        
        return query.order_by(cls.communication_time.desc()).all()
    
    @classmethod
    def get_by_type(cls, session, communication_type: str, order_id: str = None):
        """
        根据沟通方式获取记录
        
        Args:
            session: 数据库会话
            communication_type: 沟通方式
            order_id: 订单ID（可选）
            
        Returns:
            list: 沟通记录列表
        """
        query = session.query(cls).filter(cls.communication_type == communication_type)
        
        if order_id:
            query = query.filter(cls.order_id == order_id)
        
        return query.order_by(cls.communication_time.desc()).all()
    
    @classmethod
    def search_content(cls, session, keyword: str, order_id: str = None):
        """
        搜索沟通内容
        
        Args:
            session: 数据库会话
            keyword: 搜索关键词
            order_id: 订单ID（可选）
            
        Returns:
            list: 匹配的沟通记录列表
        """
        query = session.query(cls).filter(
            cls.content.contains(keyword) |
            cls.tags.contains(keyword)
        )
        
        if order_id:
            query = query.filter(cls.order_id == order_id)
        
        return query.order_by(cls.communication_time.desc()).all()

# 沟通方式枚举
class CommunicationType:
    """沟通方式枚举"""
    MESSAGE = "message"      # 消息
    EMAIL = "email"          # 邮件
    PHONE = "phone"          # 电话
    MEETING = "meeting"      # 会议
    VIDEO_CALL = "video_call"  # 视频通话
    WECHAT = "wechat"        # 微信
    QQ = "qq"                # QQ
    
    @classmethod
    def get_all_types(cls) -> list[str]:
        """获取所有沟通方式列表"""
        return [
            cls.MESSAGE, cls.EMAIL, cls.PHONE, cls.MEETING,
            cls.VIDEO_CALL, cls.WECHAT, cls.QQ
        ]

class CommunicationDirection:
    """沟通方向枚举"""
    INCOMING = "incoming"    # 接收
    OUTGOING = "outgoing"    # 发送
    
    @classmethod
    def get_all_directions(cls) -> list[str]:
        """获取所有沟通方向列表"""
        return [cls.INCOMING, cls.OUTGOING]

class CommunicationCategory:
    """沟通分类枚举"""
    GENERAL = "general"              # 一般沟通
    REQUIREMENT = "requirement"      # 需求讨论
    PROGRESS = "progress"            # 进度汇报
    FEEDBACK = "feedback"            # 反馈意见
    ISSUE = "issue"                  # 问题报告
    DELIVERY = "delivery"            # 交付相关
    PAYMENT = "payment"              # 付款相关
    CHANGE_REQUEST = "change_request"  # 变更请求
    
    @classmethod
    def get_all_categories(cls) -> list[str]:
        """获取所有沟通分类列表"""
        return [
            cls.GENERAL, cls.REQUIREMENT, cls.PROGRESS, cls.FEEDBACK,
            cls.ISSUE, cls.DELIVERY, cls.PAYMENT, cls.CHANGE_REQUEST
        ]
