"""
基础模型类定义

提供所有数据模型的基础类和通用字段
包含创建时间、更新时间等通用功能
"""

import uuid
from datetime import datetime
from typing import Any
from sqlalchemy import Column, String, DateTime, Integer, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declared_attr
from app.core.database import Base

class TimestampMixin:
    """
    时间戳混入类
    
    为模型添加创建时间和更新时间字段
    自动管理时间戳的设置和更新
    """
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )

class UUIDMixin:
    """
    UUID主键混入类
    
    为模型添加UUID格式的主键
    确保主键的唯一性和安全性
    """
    
    id = Column(
        String(36),  # SQLite使用String存储UUID
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        nullable=False,
        comment="主键ID"
    )

class BaseModel(Base, UUIDMixin, TimestampMixin):
    """
    基础模型类
    
    所有业务模型的基类
    提供通用的字段和方法
    """
    
    __abstract__ = True  # 抽象基类，不创建对应的数据库表
    
    def __repr__(self) -> str:
        """
        模型的字符串表示
        
        Returns:
            str: 模型的字符串描述
        """
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def to_dict(self) -> dict[str, Any]:
        """
        将模型转换为字典
        
        Returns:
            dict: 包含模型所有字段的字典
        """
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            # 处理datetime类型
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            else:
                result[column.name] = value
        return result
    
    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "BaseModel":
        """
        从字典创建模型实例
        
        Args:
            data: 包含模型字段的字典
            
        Returns:
            BaseModel: 模型实例
        """
        # 过滤掉不存在的字段
        valid_fields = {
            key: value for key, value in data.items()
            if hasattr(cls, key)
        }
        return cls(**valid_fields)
    
    def update_from_dict(self, data: dict[str, Any]) -> None:
        """
        从字典更新模型字段
        
        Args:
            data: 包含要更新字段的字典
        """
        for key, value in data.items():
            if hasattr(self, key) and key not in ['id', 'created_at']:
                setattr(self, key, value)

class SoftDeleteMixin:
    """
    软删除混入类
    
    为模型添加软删除功能
    删除时不真正删除记录，而是标记为已删除
    """
    
    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="删除时间"
    )
    
    @property
    def is_deleted(self) -> bool:
        """
        检查记录是否已被软删除
        
        Returns:
            bool: 是否已删除
        """
        return self.deleted_at is not None
    
    def soft_delete(self) -> None:
        """
        执行软删除
        
        设置删除时间为当前时间
        """
        self.deleted_at = datetime.utcnow()
    
    def restore(self) -> None:
        """
        恢复软删除的记录
        
        清除删除时间标记
        """
        self.deleted_at = None

class OrderableMixin:
    """
    排序混入类
    
    为模型添加排序功能
    支持自定义排序权重
    """
    
    sort_order = Column(
        Integer,
        nullable=False,
        default=0,
        comment="排序权重"
    )

# 枚举类型定义
class OrderStatus:
    """订单状态枚举"""
    PENDING = "pending"          # 待开始
    IN_PROGRESS = "in_progress"  # 进行中
    REVISING = "revising"        # 修改中
    REVIEWING = "reviewing"      # 待验收
    COMPLETED = "completed"      # 已完成
    SETTLED = "settled"          # 已结算
    CANCELLED = "cancelled"      # 已取消
    PAUSED = "paused"           # 已暂停
    
    @classmethod
    def get_all_statuses(cls) -> list[str]:
        """获取所有状态列表"""
        return [
            cls.PENDING, cls.IN_PROGRESS, cls.REVISING,
            cls.REVIEWING, cls.COMPLETED, cls.SETTLED,
            cls.CANCELLED, cls.PAUSED
        ]
    
    @classmethod
    def get_active_statuses(cls) -> list[str]:
        """获取活跃状态列表（排除已取消和已结算）"""
        return [
            cls.PENDING, cls.IN_PROGRESS, cls.REVISING,
            cls.REVIEWING, cls.COMPLETED, cls.PAUSED
        ]

class BillingMethod:
    """计费方式枚举"""
    FIXED = "fixed"              # 固定价格
    HOURLY = "hourly"           # 按小时计费
    WORD_COUNT = "word_count"   # 按字数计费
    PAGE_COUNT = "page_count"   # 按页数计费
    
    @classmethod
    def get_all_methods(cls) -> list[str]:
        """获取所有计费方式列表"""
        return [cls.FIXED, cls.HOURLY, cls.WORD_COUNT, cls.PAGE_COUNT]

class CustomerSource:
    """客户来源枚举"""
    FRIEND = "friend"           # 朋友推荐
    PLATFORM = "platform"      # 平台获客
    DIRECT = "direct"          # 直接联系
    REFERRAL = "referral"      # 客户推荐
    
    @classmethod
    def get_all_sources(cls) -> list[str]:
        """获取所有客户来源列表"""
        return [cls.FRIEND, cls.PLATFORM, cls.DIRECT, cls.REFERRAL]

class CategoryType:
    """项目分类类型枚举"""
    DEVELOPMENT = "development"  # 代码开发
    WRITING = "writing"         # 论文写作
    CONTENT = "content"         # 内容创作
    CONSULTING = "consulting"   # 技术咨询
    
    @classmethod
    def get_all_types(cls) -> list[str]:
        """获取所有分类类型列表"""
        return [cls.DEVELOPMENT, cls.WRITING, cls.CONTENT, cls.CONSULTING]
