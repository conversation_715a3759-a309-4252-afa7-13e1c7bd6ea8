"""
自定义异常定义模块

定义应用的自定义异常类型和错误处理机制
提供统一的错误响应格式和错误码管理
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, status

class BaseAPIException(HTTPException):
    """
    基础API异常类
    
    所有自定义异常的基类
    提供统一的异常格式和错误处理
    """
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: str = None,
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code or self.__class__.__name__

# 业务异常类
class BusinessException(BaseAPIException):
    """业务逻辑异常"""
    
    def __init__(self, detail: str, error_code: str = "BUSINESS_ERROR"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            error_code=error_code
        )

class ValidationException(BaseAPIException):
    """数据验证异常"""
    
    def __init__(self, detail: str, error_code: str = "VALIDATION_ERROR"):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code=error_code
        )

class AuthenticationException(BaseAPIException):
    """认证异常"""
    
    def __init__(self, detail: str = "认证失败", error_code: str = "AUTHENTICATION_ERROR"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code=error_code,
            headers={"WWW-Authenticate": "Bearer"}
        )

class AuthorizationException(BaseAPIException):
    """授权异常"""
    
    def __init__(self, detail: str = "权限不足", error_code: str = "AUTHORIZATION_ERROR"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code=error_code
        )

class NotFoundException(BaseAPIException):
    """资源不存在异常"""
    
    def __init__(self, detail: str = "资源不存在", error_code: str = "NOT_FOUND"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code=error_code
        )

class ConflictException(BaseAPIException):
    """资源冲突异常"""
    
    def __init__(self, detail: str = "资源冲突", error_code: str = "CONFLICT"):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code=error_code
        )

class DatabaseException(BaseAPIException):
    """数据库操作异常"""
    
    def __init__(self, detail: str = "数据库操作失败", error_code: str = "DATABASE_ERROR"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code=error_code
        )

class FileException(BaseAPIException):
    """文件操作异常"""
    
    def __init__(self, detail: str = "文件操作失败", error_code: str = "FILE_ERROR"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            error_code=error_code
        )

# 具体业务异常
class CustomerNotFoundException(NotFoundException):
    """客户不存在异常"""
    
    def __init__(self, customer_id: str):
        super().__init__(
            detail=f"客户 {customer_id} 不存在",
            error_code="CUSTOMER_NOT_FOUND"
        )

class OrderNotFoundException(NotFoundException):
    """订单不存在异常"""
    
    def __init__(self, order_id: str):
        super().__init__(
            detail=f"订单 {order_id} 不存在",
            error_code="ORDER_NOT_FOUND"
        )

class CategoryNotFoundException(NotFoundException):
    """分类不存在异常"""
    
    def __init__(self, category_id: str):
        super().__init__(
            detail=f"分类 {category_id} 不存在",
            error_code="CATEGORY_NOT_FOUND"
        )

class WorkLogNotFoundException(NotFoundException):
    """工时记录不存在异常"""
    
    def __init__(self, work_log_id: str):
        super().__init__(
            detail=f"工时记录 {work_log_id} 不存在",
            error_code="WORK_LOG_NOT_FOUND"
        )

class DuplicateEmailException(ConflictException):
    """邮箱重复异常"""
    
    def __init__(self, email: str):
        super().__init__(
            detail=f"邮箱 {email} 已存在",
            error_code="DUPLICATE_EMAIL"
        )

class InvalidOrderStatusException(BusinessException):
    """无效订单状态异常"""
    
    def __init__(self, current_status: str, target_status: str):
        super().__init__(
            detail=f"无法从状态 {current_status} 变更为 {target_status}",
            error_code="INVALID_ORDER_STATUS"
        )

class InsufficientPermissionException(AuthorizationException):
    """权限不足异常"""
    
    def __init__(self, action: str):
        super().__init__(
            detail=f"没有权限执行操作: {action}",
            error_code="INSUFFICIENT_PERMISSION"
        )

class FileUploadException(FileException):
    """文件上传异常"""
    
    def __init__(self, reason: str):
        super().__init__(
            detail=f"文件上传失败: {reason}",
            error_code="FILE_UPLOAD_ERROR"
        )

class FileSizeExceededException(FileException):
    """文件大小超限异常"""
    
    def __init__(self, max_size: int):
        super().__init__(
            detail=f"文件大小超过限制 {max_size / 1024 / 1024:.1f}MB",
            error_code="FILE_SIZE_EXCEEDED"
        )

class UnsupportedFileTypeException(FileException):
    """不支持的文件类型异常"""
    
    def __init__(self, file_type: str):
        super().__init__(
            detail=f"不支持的文件类型: {file_type}",
            error_code="UNSUPPORTED_FILE_TYPE"
        )

# 错误码常量
class ErrorCodes:
    """错误码常量"""
    
    # 通用错误
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    BUSINESS_ERROR = "BUSINESS_ERROR"
    
    # 认证授权错误
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    INSUFFICIENT_PERMISSION = "INSUFFICIENT_PERMISSION"
    
    # 资源错误
    NOT_FOUND = "NOT_FOUND"
    CONFLICT = "CONFLICT"
    DUPLICATE_EMAIL = "DUPLICATE_EMAIL"
    
    # 业务错误
    CUSTOMER_NOT_FOUND = "CUSTOMER_NOT_FOUND"
    ORDER_NOT_FOUND = "ORDER_NOT_FOUND"
    CATEGORY_NOT_FOUND = "CATEGORY_NOT_FOUND"
    WORK_LOG_NOT_FOUND = "WORK_LOG_NOT_FOUND"
    INVALID_ORDER_STATUS = "INVALID_ORDER_STATUS"
    
    # 文件错误
    FILE_ERROR = "FILE_ERROR"
    FILE_UPLOAD_ERROR = "FILE_UPLOAD_ERROR"
    FILE_SIZE_EXCEEDED = "FILE_SIZE_EXCEEDED"
    UNSUPPORTED_FILE_TYPE = "UNSUPPORTED_FILE_TYPE"
    
    # 数据库错误
    DATABASE_ERROR = "DATABASE_ERROR"

# HTTP状态码映射
STATUS_CODE_MAPPING = {
    ErrorCodes.VALIDATION_ERROR: status.HTTP_422_UNPROCESSABLE_ENTITY,
    ErrorCodes.BUSINESS_ERROR: status.HTTP_400_BAD_REQUEST,
    ErrorCodes.AUTHENTICATION_ERROR: status.HTTP_401_UNAUTHORIZED,
    ErrorCodes.AUTHORIZATION_ERROR: status.HTTP_403_FORBIDDEN,
    ErrorCodes.NOT_FOUND: status.HTTP_404_NOT_FOUND,
    ErrorCodes.CONFLICT: status.HTTP_409_CONFLICT,
    ErrorCodes.DATABASE_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
    ErrorCodes.FILE_ERROR: status.HTTP_400_BAD_REQUEST,
    ErrorCodes.UNKNOWN_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
}

def get_status_code(error_code: str) -> int:
    """
    根据错误码获取HTTP状态码
    
    Args:
        error_code: 错误码
        
    Returns:
        int: HTTP状态码
    """
    return STATUS_CODE_MAPPING.get(error_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
