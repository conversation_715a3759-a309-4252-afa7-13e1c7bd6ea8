"""
应用配置管理模块

提供应用的配置参数管理和环境变量处理
支持开发、测试、生产环境的配置切换
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """
    应用配置类
    
    使用Pydantic BaseSettings自动从环境变量读取配置
    支持配置验证和类型转换
    """
    
    # 应用基本信息
    app_name: str = Field(default="兼职接单管理系统", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    app_description: str = Field(default="专为程序员兼职接单设计的全流程管理工具", description="应用描述")
    
    # 服务器配置
    host: str = Field(default="127.0.0.1", description="服务器地址")
    port: int = Field(default=8000, description="服务器端口")
    debug: bool = Field(default=True, description="调试模式")
    reload: bool = Field(default=True, description="自动重载")
    
    # API配置
    api_prefix: str = Field(default="/api/v1", description="API路径前缀")
    docs_url: str = Field(default="/docs", description="API文档地址")
    redoc_url: str = Field(default="/redoc", description="ReDoc文档地址")
    openapi_url: str = Field(default="/openapi.json", description="OpenAPI规范地址")
    
    # 数据库配置
    database_url: str = Field(default="sqlite:///data/database.db", description="数据库连接URL")
    database_echo: bool = Field(default=False, description="是否打印SQL语句")
    
    # 安全配置
    secret_key: str = Field(default="your-secret-key-here", description="密钥")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")
    
    # CORS配置
    cors_origins: list[str] = Field(default=["*"], description="允许的跨域来源")
    cors_methods: list[str] = Field(default=["*"], description="允许的HTTP方法")
    cors_headers: list[str] = Field(default=["*"], description="允许的HTTP头")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="data/logs/app.log", description="日志文件路径")
    log_max_size: int = Field(default=10 * 1024 * 1024, description="日志文件最大大小(字节)")
    log_backup_count: int = Field(default=5, description="日志文件备份数量")
    
    # 文件上传配置
    upload_dir: str = Field(default="data/attachments", description="文件上传目录")
    max_file_size: int = Field(default=50 * 1024 * 1024, description="最大文件大小(字节)")
    allowed_file_types: list[str] = Field(
        default=[
            ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt",  # 文档
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp",  # 图片
            ".zip", ".rar", ".7z", ".tar", ".gz",  # 压缩包
            ".py", ".js", ".html", ".css", ".json", ".xml"  # 代码文件
        ],
        description="允许的文件类型"
    )
    
    # 业务配置
    default_page_size: int = Field(default=20, description="默认分页大小")
    max_page_size: int = Field(default=100, description="最大分页大小")
    
    # 缓存配置
    cache_ttl: int = Field(default=300, description="缓存过期时间(秒)")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# 创建全局配置实例
settings = Settings()

def get_settings() -> Settings:
    """
    获取应用配置
    
    Returns:
        Settings: 配置实例
    """
    return settings

def create_directories():
    """
    创建必要的目录
    
    确保日志目录、上传目录等存在
    """
    directories = [
        os.path.dirname(settings.log_file),
        settings.upload_dir,
        "data/backups",
        "data/temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def get_database_url() -> str:
    """
    获取数据库连接URL
    
    Returns:
        str: 数据库连接URL
    """
    return settings.database_url

def is_development() -> bool:
    """
    判断是否为开发环境
    
    Returns:
        bool: 是否为开发环境
    """
    return settings.debug

def is_production() -> bool:
    """
    判断是否为生产环境
    
    Returns:
        bool: 是否为生产环境
    """
    return not settings.debug

# 环境配置类
class DevelopmentConfig(Settings):
    """开发环境配置"""
    debug: bool = True
    reload: bool = True
    database_echo: bool = True
    log_level: str = "DEBUG"

class ProductionConfig(Settings):
    """生产环境配置"""
    debug: bool = False
    reload: bool = False
    database_echo: bool = False
    log_level: str = "WARNING"
    docs_url: Optional[str] = None  # 生产环境禁用API文档
    redoc_url: Optional[str] = None

class TestingConfig(Settings):
    """测试环境配置"""
    debug: bool = True
    database_url: str = "sqlite:///data/test_database.db"
    log_level: str = "DEBUG"

def get_config_by_env(env: str = None) -> Settings:
    """
    根据环境获取配置
    
    Args:
        env: 环境名称 (development/production/testing)
        
    Returns:
        Settings: 对应环境的配置
    """
    if env is None:
        env = os.getenv("ENVIRONMENT", "development")
    
    config_map = {
        "development": DevelopmentConfig,
        "production": ProductionConfig,
        "testing": TestingConfig
    }
    
    config_class = config_map.get(env.lower(), DevelopmentConfig)
    return config_class()

# 初始化配置
if __name__ == "__main__":
    # 创建必要目录
    create_directories()
    
    # 打印配置信息
    print("应用配置信息:")
    print(f"应用名称: {settings.app_name}")
    print(f"应用版本: {settings.app_version}")
    print(f"服务器地址: {settings.host}:{settings.port}")
    print(f"调试模式: {settings.debug}")
    print(f"数据库URL: {settings.database_url}")
    print(f"API前缀: {settings.api_prefix}")
    print(f"日志级别: {settings.log_level}")
    print(f"上传目录: {settings.upload_dir}")
    print(f"最大文件大小: {settings.max_file_size / 1024 / 1024:.1f}MB")
