"""
数据库连接和会话管理模块

提供SQLAlchemy数据库连接、会话管理和基础配置
支持SQLite数据库的连接池和事务管理
"""

import os
from typing import Generator
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
DATABASE_DIR = "data"
DATABASE_FILE = "database.db"
DATABASE_PATH = os.path.join(DATABASE_DIR, DATABASE_FILE)

# 确保数据目录存在
os.makedirs(DATABASE_DIR, exist_ok=True)

# SQLite数据库URL
SQLALCHEMY_DATABASE_URL = f"sqlite:///{DATABASE_PATH}"

# 创建数据库引擎
# SQLite特殊配置：
# - check_same_thread=False: 允许多线程访问
# - poolclass=StaticPool: 使用静态连接池
# - connect_args: SQLite特定参数
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={
        "check_same_thread": False,
        "timeout": 20,  # 连接超时时间
    },
    echo=False,  # 生产环境设为False，开发时可设为True查看SQL
    future=True,  # 使用SQLAlchemy 2.0风格
)

# 启用SQLite外键约束
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """
    SQLite连接时设置PRAGMA参数
    启用外键约束、WAL模式等优化设置
    """
    cursor = dbapi_connection.cursor()
    # 启用外键约束
    cursor.execute("PRAGMA foreign_keys=ON")
    # 启用WAL模式，提高并发性能
    cursor.execute("PRAGMA journal_mode=WAL")
    # 设置同步模式，平衡性能和安全性
    cursor.execute("PRAGMA synchronous=NORMAL")
    # 设置缓存大小（页数）
    cursor.execute("PRAGMA cache_size=10000")
    # 设置临时存储为内存
    cursor.execute("PRAGMA temp_store=MEMORY")
    cursor.close()

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    future=True,  # 使用SQLAlchemy 2.0风格
)

# 创建基础模型类
Base = declarative_base()

def get_database_session() -> Generator[Session, None, None]:
    """
    获取数据库会话
    
    使用上下文管理器确保会话正确关闭
    支持事务自动提交和回滚
    
    Yields:
        Session: SQLAlchemy数据库会话
    """
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"数据库操作错误: {e}")
        raise
    finally:
        session.close()

def create_tables():
    """
    创建所有数据库表
    
    根据模型定义创建表结构
    如果表已存在则跳过
    """
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise

def drop_tables():
    """
    删除所有数据库表
    
    警告：此操作会删除所有数据，仅用于开发和测试
    """
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("数据库表删除成功")
    except Exception as e:
        logger.error(f"删除数据库表失败: {e}")
        raise

def get_database_info():
    """
    获取数据库信息
    
    Returns:
        dict: 包含数据库路径、大小等信息的字典
    """
    info = {
        "database_path": DATABASE_PATH,
        "database_exists": os.path.exists(DATABASE_PATH),
        "database_size": 0,
        "engine_info": str(engine.url),
    }
    
    if info["database_exists"]:
        try:
            info["database_size"] = os.path.getsize(DATABASE_PATH)
        except OSError:
            info["database_size"] = 0
    
    return info

def test_connection():
    """
    测试数据库连接

    Returns:
        bool: 连接是否成功
    """
    try:
        from sqlalchemy import text
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            return result.fetchone()[0] == 1
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False

# 数据库会话依赖注入（用于FastAPI）
def get_db() -> Generator[Session, None, None]:
    """
    FastAPI依赖注入函数

    用于在API端点中注入数据库会话

    Yields:
        Session: SQLAlchemy数据库会话
    """
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

if __name__ == "__main__":
    # 测试数据库连接
    print("测试数据库连接...")
    if test_connection():
        print("✅ 数据库连接成功")
        print(f"📁 数据库路径: {DATABASE_PATH}")
        
        # 显示数据库信息
        info = get_database_info()
        print(f"📊 数据库信息: {info}")
    else:
        print("❌ 数据库连接失败")
