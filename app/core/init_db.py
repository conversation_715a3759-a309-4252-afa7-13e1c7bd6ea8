"""
数据库初始化脚本

创建数据库表、初始化基础数据和测试数据
"""

import os
import sys
from datetime import datetime, date, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.core.database import engine, get_database_session, create_tables, test_connection
from app.models import *

def init_database():
    """
    初始化数据库
    
    创建所有表结构
    """
    print("🔧 开始初始化数据库...")
    
    try:
        # 测试数据库连接
        if not test_connection():
            print("❌ 数据库连接失败")
            return False
        
        # 创建所有表
        create_tables()
        print("✅ 数据库表创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def init_basic_data():
    """
    初始化基础数据
    
    创建默认的分类、设置等基础数据
    """
    print("📊 开始初始化基础数据...")
    
    try:
        with next(get_database_session()) as session:
            # 初始化系统设置
            Setting.initialize_default_settings(session)
            print("✅ 系统设置初始化完成")
            
            # 创建默认项目分类
            create_default_categories(session)
            print("✅ 项目分类初始化完成")
            
            # 创建默认模板
            Template.create_default_templates(session)
            print("✅ 订单模板初始化完成")
            
        return True
    except Exception as e:
        print(f"❌ 基础数据初始化失败: {e}")
        return False

def create_default_categories(session):
    """
    创建默认项目分类
    
    Args:
        session: 数据库会话
    """
    categories_data = [
        # 代码开发类
        {
            "name": "代码开发",
            "type": CategoryType.DEVELOPMENT,
            "description": "软件开发相关项目",
            "billing_method": BillingMethod.FIXED,
            "default_rate": Decimal("5000.00"),
            "sort_order": 1,
            "children": [
                {
                    "name": "Web开发",
                    "description": "网站和Web应用开发",
                    "default_rate": Decimal("5000.00"),
                    "sort_order": 1
                },
                {
                    "name": "移动端开发", 
                    "description": "iOS/Android应用开发",
                    "default_rate": Decimal("8000.00"),
                    "sort_order": 2
                },
                {
                    "name": "桌面应用",
                    "description": "Windows/Mac桌面应用开发",
                    "default_rate": Decimal("6000.00"),
                    "sort_order": 3
                },
                {
                    "name": "脚本工具",
                    "description": "自动化脚本和工具开发",
                    "default_rate": Decimal("2000.00"),
                    "sort_order": 4
                },
                {
                    "name": "数据处理",
                    "description": "数据分析和处理脚本",
                    "default_rate": Decimal("3000.00"),
                    "sort_order": 5
                }
            ]
        },
        
        # 论文写作类
        {
            "name": "论文写作",
            "type": CategoryType.WRITING,
            "description": "学术论文写作服务",
            "billing_method": BillingMethod.WORD_COUNT,
            "default_rate": Decimal("0.50"),
            "sort_order": 2,
            "children": [
                {
                    "name": "学术论文",
                    "description": "期刊论文、会议论文写作",
                    "default_rate": Decimal("0.80"),
                    "sort_order": 1
                },
                {
                    "name": "毕业论文",
                    "description": "本科、硕士、博士毕业论文",
                    "default_rate": Decimal("0.60"),
                    "sort_order": 2
                },
                {
                    "name": "文献综述",
                    "description": "文献调研和综述写作",
                    "default_rate": Decimal("0.40"),
                    "sort_order": 3
                },
                {
                    "name": "论文修改",
                    "description": "论文润色和修改服务",
                    "default_rate": Decimal("0.30"),
                    "sort_order": 4
                }
            ]
        },
        
        # 内容创作类
        {
            "name": "内容创作",
            "type": CategoryType.CONTENT,
            "description": "各类内容创作服务",
            "billing_method": BillingMethod.WORD_COUNT,
            "default_rate": Decimal("0.20"),
            "sort_order": 3,
            "children": [
                {
                    "name": "技术文档",
                    "description": "API文档、用户手册等",
                    "default_rate": Decimal("0.30"),
                    "sort_order": 1
                },
                {
                    "name": "产品文案",
                    "description": "产品介绍、宣传文案",
                    "default_rate": Decimal("0.25"),
                    "sort_order": 2
                },
                {
                    "name": "营销文案",
                    "description": "广告文案、推广内容",
                    "default_rate": Decimal("0.35"),
                    "sort_order": 3
                },
                {
                    "name": "翻译服务",
                    "description": "中英文翻译服务",
                    "default_rate": Decimal("0.15"),
                    "sort_order": 4
                }
            ]
        },
        
        # 技术咨询类
        {
            "name": "技术咨询",
            "type": CategoryType.CONSULTING,
            "description": "技术咨询和培训服务",
            "billing_method": BillingMethod.HOURLY,
            "default_rate": Decimal("200.00"),
            "sort_order": 4,
            "children": [
                {
                    "name": "架构设计",
                    "description": "系统架构设计咨询",
                    "default_rate": Decimal("300.00"),
                    "sort_order": 1
                },
                {
                    "name": "代码审查",
                    "description": "代码质量审查服务",
                    "default_rate": Decimal("150.00"),
                    "sort_order": 2
                },
                {
                    "name": "技术培训",
                    "description": "技术培训和指导",
                    "default_rate": Decimal("250.00"),
                    "sort_order": 3
                },
                {
                    "name": "问题诊断",
                    "description": "技术问题诊断和解决",
                    "default_rate": Decimal("200.00"),
                    "sort_order": 4
                }
            ]
        }
    ]
    
    for category_data in categories_data:
        # 检查父分类是否已存在
        existing_parent = session.query(Category).filter(
            Category.name == category_data["name"]
        ).first()
        
        if not existing_parent:
            # 创建父分类
            children_data = category_data.pop("children", [])
            parent_category = Category(**category_data)
            session.add(parent_category)
            session.flush()  # 获取ID
            
            # 创建子分类
            for child_data in children_data:
                child_data.update({
                    "type": category_data["type"],
                    "billing_method": category_data["billing_method"],
                    "parent_id": parent_category.id
                })
                child_category = Category(**child_data)
                session.add(child_category)
    
    session.commit()

def create_test_data():
    """
    创建测试数据
    
    创建一些示例客户、订单等数据用于测试
    """
    print("🧪 开始创建测试数据...")
    
    try:
        with next(get_database_session()) as session:
            # 创建测试客户
            test_customers = create_test_customers(session)
            print(f"✅ 创建了 {len(test_customers)} 个测试客户")
            
            # 创建测试订单
            test_orders = create_test_orders(session, test_customers)
            print(f"✅ 创建了 {len(test_orders)} 个测试订单")
            
            # 创建测试工时记录
            test_work_logs = create_test_work_logs(session, test_orders)
            print(f"✅ 创建了 {len(test_work_logs)} 个测试工时记录")
            
        return True
    except Exception as e:
        print(f"❌ 测试数据创建失败: {e}")
        return False

def create_test_customers(session):
    """
    创建测试客户
    
    Args:
        session: 数据库会话
        
    Returns:
        list: 创建的客户列表
    """
    customers_data = [
        {
            "name": "张三",
            "contact_phone": "13800138001",
            "contact_email": "<EMAIL>",
            "contact_wechat": "zhangsan_wx",
            "source": CustomerSource.FRIEND,
            "company": "ABC科技有限公司",
            "position": "技术总监",
            "satisfaction_rating": 5,
            "notes": "老客户，合作愉快"
        },
        {
            "name": "李四",
            "contact_phone": "13800138002", 
            "contact_email": "<EMAIL>",
            "source": CustomerSource.PLATFORM,
            "company": "XYZ创业公司",
            "position": "产品经理",
            "satisfaction_rating": 4,
            "notes": "需求变更较多，需要耐心沟通"
        },
        {
            "name": "王五",
            "contact_email": "<EMAIL>",
            "contact_qq": "*********",
            "source": CustomerSource.DIRECT,
            "company": "某某大学",
            "position": "研究生",
            "satisfaction_rating": 5,
            "notes": "学术论文写作，要求严格"
        }
    ]
    
    customers = []
    for customer_data in customers_data:
        customer = Customer(**customer_data)
        session.add(customer)
        customers.append(customer)
    
    session.commit()
    return customers

def create_test_orders(session, customers):
    """
    创建测试订单
    
    Args:
        session: 数据库会话
        customers: 客户列表
        
    Returns:
        list: 创建的订单列表
    """
    # 获取分类
    web_dev_category = session.query(Category).filter(
        Category.name == "Web开发"
    ).first()
    
    academic_paper_category = session.query(Category).filter(
        Category.name == "学术论文"
    ).first()
    
    orders_data = [
        {
            "title": "企业官网开发",
            "description": "开发响应式企业官网，包含产品展示、新闻发布等功能",
            "customer_id": customers[0].id,
            "category_id": web_dev_category.id if web_dev_category else None,
            "status": OrderStatus.IN_PROGRESS,
            "billing_method": BillingMethod.FIXED,
            "total_amount": Decimal("8000.00"),
            "deposit_ratio": Decimal("0.30"),
            "deposit_amount": Decimal("2400.00"),
            "final_amount": Decimal("5600.00"),
            "start_date": date.today() - timedelta(days=10),
            "deadline": datetime.now() + timedelta(days=20),
            "estimated_hours": Decimal("60.00"),
            "tech_stack": "Vue.js, Node.js, MySQL",
            "deliverables": "源代码, 部署文档, 用户手册",
            "priority": 4,
            "tags": "Web开发, Vue.js, 企业官网"
        },
        {
            "title": "数据分析论文",
            "description": "基于机器学习的数据分析方法研究",
            "customer_id": customers[2].id,
            "category_id": academic_paper_category.id if academic_paper_category else None,
            "status": OrderStatus.REVIEWING,
            "billing_method": BillingMethod.WORD_COUNT,
            "unit_price": Decimal("0.80"),
            "quantity": Decimal("8000.00"),
            "total_amount": Decimal("6400.00"),
            "start_date": date.today() - timedelta(days=30),
            "deadline": datetime.now() + timedelta(days=5),
            "estimated_hours": Decimal("40.00"),
            "deliverables": "论文正文, 参考文献, 查重报告",
            "priority": 5,
            "tags": "学术论文, 机器学习, 数据分析"
        },
        {
            "title": "移动端APP开发",
            "description": "开发iOS和Android双平台APP",
            "customer_id": customers[1].id,
            "category_id": web_dev_category.id if web_dev_category else None,
            "status": OrderStatus.PENDING,
            "billing_method": BillingMethod.FIXED,
            "total_amount": Decimal("15000.00"),
            "deposit_ratio": Decimal("0.40"),
            "deposit_amount": Decimal("6000.00"),
            "final_amount": Decimal("9000.00"),
            "start_date": date.today() + timedelta(days=5),
            "deadline": datetime.now() + timedelta(days=60),
            "estimated_hours": Decimal("120.00"),
            "tech_stack": "React Native, Node.js, MongoDB",
            "deliverables": "iOS APP, Android APP, 后台管理系统",
            "priority": 3,
            "tags": "移动开发, React Native, 跨平台"
        }
    ]
    
    orders = []
    for order_data in orders_data:
        if order_data["category_id"]:  # 只有当分类存在时才创建订单
            order = Order(**order_data)
            order.calculate_amounts()  # 计算金额
            session.add(order)
            orders.append(order)
    
    session.commit()
    return orders

def create_test_work_logs(session, orders):
    """
    创建测试工时记录
    
    Args:
        session: 数据库会话
        orders: 订单列表
        
    Returns:
        list: 创建的工时记录列表
    """
    work_logs = []
    
    for order in orders:
        if order.status in [OrderStatus.IN_PROGRESS, OrderStatus.REVIEWING]:
            # 为进行中的订单创建一些工时记录
            for i in range(3):
                work_log = WorkLog(
                    order_id=order.id,
                    start_time=datetime.now() - timedelta(days=i*2, hours=2),
                    end_time=datetime.now() - timedelta(days=i*2),
                    duration=120,  # 2小时
                    description=f"第{i+1}阶段开发工作",
                    work_type="开发",
                    hourly_rate=Decimal("100.00"),
                    billable=True
                )
                session.add(work_log)
                work_logs.append(work_log)
    
    session.commit()
    return work_logs

def main():
    """
    主函数
    """
    print("🚀 兼职接单管理系统 - 数据库初始化")
    print("=" * 50)
    
    # 初始化数据库
    if not init_database():
        return
    
    # 初始化基础数据
    if not init_basic_data():
        return
    
    # 询问是否创建测试数据
    create_test = input("\n是否创建测试数据？(y/N): ").lower().strip()
    if create_test in ['y', 'yes']:
        create_test_data()
    
    print("\n🎉 数据库初始化完成！")
    print("📁 数据库文件位置: data/database.db")
    print("🔧 您现在可以启动应用程序了")

if __name__ == "__main__":
    main()
