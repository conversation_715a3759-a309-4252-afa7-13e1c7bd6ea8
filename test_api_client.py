"""
API客户端测试程序

测试前端API客户端与后端的通信功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from app.frontend.controllers.api_client import APIClient
    import asyncio
    
    async def test_api_client():
        """测试API客户端功能"""
        print("=" * 60)
        print("API客户端通信测试")
        print("=" * 60)
        
        # 创建API客户端
        api_client = APIClient()
        
        print("🔗 测试后端连接...")
        try:
            # 测试健康检查
            health_response = api_client.health_check()
            if health_response.get('success'):
                print("✅ 后端连接成功")
                print(f"   应用名称: {health_response['data']['app_name']}")
                print(f"   版本: {health_response['data']['version']}")
                print(f"   数据库连接: {'✅' if health_response['data']['database_connected'] else '❌'}")
            else:
                print("❌ 后端连接失败")
                return
        except Exception as e:
            print(f"❌ 连接错误: {e}")
            return
        
        print("\n📋 测试客户管理API...")
        try:
            customers_response = api_client.get_customers()
            if customers_response.get('success'):
                customers = customers_response.get('data', [])
                print(f"✅ 获取客户列表成功，共 {len(customers)} 个客户")
                for customer in customers[:3]:  # 显示前3个客户
                    print(f"   - {customer['name']} ({customer['company']})")
            else:
                print("❌ 获取客户列表失败")
        except Exception as e:
            print(f"❌ 客户API错误: {e}")
        
        print("\n📦 测试订单管理API...")
        try:
            orders_response = api_client.get_orders()
            if orders_response.get('success'):
                orders = orders_response.get('data', [])
                print(f"✅ 获取订单列表成功，共 {len(orders)} 个订单")
                for order in orders[:3]:  # 显示前3个订单
                    print(f"   - {order['title']} (状态: {order['status']})")
            else:
                print("❌ 获取订单列表失败")
        except Exception as e:
            print(f"❌ 订单API错误: {e}")
        
        print("\n⏱️ 测试工时记录API...")
        try:
            work_logs_response = api_client.get_work_logs()
            if work_logs_response.get('success'):
                work_logs = work_logs_response.get('data', [])
                print(f"✅ 获取工时记录成功，共 {len(work_logs)} 条记录")
                
                # 测试计时器状态
                timer_status = api_client.get_timer_status()
                if timer_status.get('success'):
                    timer_data = timer_status.get('data', {})
                    is_running = timer_data.get('is_running', False)
                    print(f"   计时器状态: {'🟢 运行中' if is_running else '🔴 已停止'}")
            else:
                print("❌ 获取工时记录失败")
        except Exception as e:
            print(f"❌ 工时API错误: {e}")
        
        print("\n📊 测试统计分析API...")
        try:
            analytics_response = api_client.get_analytics_overview()
            if analytics_response.get('success'):
                analytics = analytics_response.get('data', {})
                print("✅ 获取统计数据成功")
                print(f"   总客户数: {analytics.get('total_customers', 0)}")
                print(f"   总订单数: {analytics.get('total_orders', 0)}")
                print(f"   总工时: {analytics.get('total_work_hours', 0)} 小时")
                print(f"   待收金额: ¥{analytics.get('pending_revenue', 0)}")
            else:
                print("❌ 获取统计数据失败")
        except Exception as e:
            print(f"❌ 统计API错误: {e}")
        
        print("\n📁 测试文件管理API...")
        try:
            attachments_response = api_client.get_attachments()
            if attachments_response.get('success'):
                attachments = attachments_response.get('data', [])
                print(f"✅ 获取文件列表成功，共 {len(attachments)} 个文件")
                for attachment in attachments[:2]:  # 显示前2个文件
                    print(f"   - {attachment['original_name']} ({attachment['file_size_display']})")
            else:
                print("❌ 获取文件列表失败")
        except Exception as e:
            print(f"❌ 文件API错误: {e}")
        
        print("\n⚙️ 测试系统设置API...")
        try:
            system_info_response = api_client.get_system_info()
            if system_info_response.get('success'):
                system_info = system_info_response.get('data', {})
                print("✅ 获取系统信息成功")
                print(f"   系统状态: {system_info.get('system_status', 'unknown')}")
                print(f"   数据库类型: {system_info.get('database_type', 'unknown')}")
                print(f"   数据库大小: {system_info.get('database_size', 'unknown')}")
                print(f"   内存使用: {system_info.get('memory_usage', 'unknown')}")
            else:
                print("❌ 获取系统信息失败")
        except Exception as e:
            print(f"❌ 系统API错误: {e}")
        
        print("\n" + "=" * 60)
        print("API客户端测试完成")
        print("=" * 60)
    
    # 运行测试
    asyncio.run(test_api_client())

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保项目结构正确，并且所有依赖已安装。")
    
    # 简化版本的API测试
    print("\n正在进行简化版API测试...")
    
    import requests
    
    def simple_api_test():
        """简化版API测试"""
        base_url = "http://127.0.0.1:8000"
        
        print("🔗 测试后端连接...")
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("✅ 后端连接成功")
                print(f"   应用: {data['data']['app_name']}")
                print(f"   版本: {data['data']['version']}")
            else:
                print(f"❌ 连接失败，状态码: {response.status_code}")
                return
        except Exception as e:
            print(f"❌ 连接错误: {e}")
            return
        
        # 测试主要API端点
        endpoints = [
            ("/api/v1/customers", "客户管理"),
            ("/api/v1/orders", "订单管理"),
            ("/api/v1/work-logs", "工时记录"),
            ("/api/v1/analytics/overview", "统计分析"),
            ("/api/v1/attachments", "文件管理"),
            ("/api/v1/settings/system/info", "系统设置")
        ]
        
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print(f"✅ {name}API正常")
                    else:
                        print(f"❌ {name}API返回错误")
                else:
                    print(f"❌ {name}API状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {name}API错误: {e}")
        
        print("\n✅ 简化版API测试完成")
    
    simple_api_test()
