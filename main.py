"""
兼职接单管理系统主程序

应用程序入口点
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont

# 导入主窗口
from app.frontend.ui.main_window import MainWindow

def setup_logging():
    """设置日志配置"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """检查依赖项"""
    try:
        import requests
        import PyQt6
        return True
    except ImportError as e:
        QMessageBox.critical(
            None, "依赖错误", 
            f"缺少必要的依赖项: {str(e)}\n\n请运行: pip install -r requirements.txt"
        )
        return False

def create_splash_screen():
    """创建启动画面"""
    # 创建一个简单的启动画面
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.GlobalColor.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowType.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # 设置启动画面文本
    splash.showMessage(
        "兼职接单管理系统\n正在启动...", 
        Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
        Qt.GlobalColor.black
    )
    
    return splash

def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("兼职接单管理系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("兼职接单管理系统")
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("应用程序启动")
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    # 创建启动画面
    splash = create_splash_screen()
    splash.show()
    
    # 处理启动画面事件
    app.processEvents()
    
    try:
        # 创建主窗口
        splash.showMessage("正在初始化主窗口...", Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
        app.processEvents()
        
        main_window = MainWindow()
        
        # 延迟显示主窗口
        def show_main_window():
            splash.finish(main_window)
            main_window.show()
            logger.info("主窗口显示完成")
        
        # 2秒后显示主窗口
        QTimer.singleShot(2000, show_main_window)
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        QMessageBox.critical(
            None, "启动错误", 
            f"应用程序启动失败:\n{str(e)}\n\n请检查日志文件获取详细信息。"
        )
        return 1

if __name__ == "__main__":
    sys.exit(main())
