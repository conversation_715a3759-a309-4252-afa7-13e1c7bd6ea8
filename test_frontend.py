"""
前端测试程序

简化版本的前端界面测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout, 
        QWidget, QLabel, QPushButton, QTableWidget, QTableWidgetItem,
        QMessageBox, QLineEdit, QComboBox
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    
    class SimpleMainWindow(QMainWindow):
        """简化的主窗口"""
        
        def __init__(self):
            super().__init__()
            self.init_ui()
        
        def init_ui(self):
            """初始化界面"""
            self.setWindowTitle("兼职接单管理系统 - 前端测试")
            self.setGeometry(100, 100, 1000, 700)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建主布局
            main_layout = QVBoxLayout(central_widget)
            
            # 创建标题
            title_label = QLabel("兼职接单管理系统")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
            main_layout.addWidget(title_label)
            
            # 创建标签页
            self.tab_widget = QTabWidget()
            main_layout.addWidget(self.tab_widget)
            
            # 创建各个标签页
            self.create_order_tab()
            self.create_customer_tab()
            self.create_analytics_tab()
            
            # 应用样式
            self.apply_style()
        
        def create_order_tab(self):
            """创建订单管理标签页"""
            order_widget = QWidget()
            layout = QVBoxLayout(order_widget)
            
            # 工具栏
            toolbar = QHBoxLayout()
            
            new_btn = QPushButton("新建订单")
            new_btn.clicked.connect(lambda: self.show_message("新建订单功能"))
            toolbar.addWidget(new_btn)
            
            edit_btn = QPushButton("编辑订单")
            edit_btn.clicked.connect(lambda: self.show_message("编辑订单功能"))
            toolbar.addWidget(edit_btn)
            
            toolbar.addStretch()
            
            search_input = QLineEdit()
            search_input.setPlaceholderText("搜索订单...")
            toolbar.addWidget(search_input)
            
            layout.addLayout(toolbar)
            
            # 订单表格
            table = QTableWidget(5, 6)
            table.setHorizontalHeaderLabels(["订单标题", "客户", "状态", "金额", "截止日期", "创建时间"])
            
            # 添加示例数据
            sample_data = [
                ["网站开发项目", "张三", "进行中", "¥5000", "2024-02-15", "2024-01-15"],
                ["移动应用开发", "李四", "待开始", "¥8000", "2024-03-01", "2024-01-20"],
                ["数据分析报告", "王五", "已完成", "¥3000", "2024-01-30", "2024-01-10"],
                ["系统维护", "赵六", "进行中", "¥2000", "2024-02-20", "2024-01-25"],
                ["UI设计", "钱七", "待开始", "¥4000", "2024-02-28", "2024-01-28"]
            ]
            
            for row, data in enumerate(sample_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(value)
                    table.setItem(row, col, item)
            
            table.resizeColumnsToContents()
            layout.addWidget(table)
            
            self.tab_widget.addTab(order_widget, "订单管理")
        
        def create_customer_tab(self):
            """创建客户管理标签页"""
            customer_widget = QWidget()
            layout = QVBoxLayout(customer_widget)
            
            # 工具栏
            toolbar = QHBoxLayout()
            
            new_btn = QPushButton("新建客户")
            new_btn.clicked.connect(lambda: self.show_message("新建客户功能"))
            toolbar.addWidget(new_btn)
            
            edit_btn = QPushButton("编辑客户")
            edit_btn.clicked.connect(lambda: self.show_message("编辑客户功能"))
            toolbar.addWidget(edit_btn)
            
            toolbar.addStretch()
            
            search_input = QLineEdit()
            search_input.setPlaceholderText("搜索客户...")
            toolbar.addWidget(search_input)
            
            layout.addLayout(toolbar)
            
            # 客户表格
            table = QTableWidget(4, 5)
            table.setHorizontalHeaderLabels(["客户名称", "邮箱", "电话", "公司", "订单数"])
            
            # 添加示例数据
            sample_data = [
                ["张三", "<EMAIL>", "13800138001", "ABC公司", "3"],
                ["李四", "<EMAIL>", "13800138002", "XYZ科技", "2"],
                ["王五", "<EMAIL>", "13800138003", "DEF企业", "1"],
                ["赵六", "<EMAIL>", "13800138004", "GHI集团", "4"]
            ]
            
            for row, data in enumerate(sample_data):
                for col, value in enumerate(data):
                    item = QTableWidgetItem(value)
                    table.setItem(row, col, item)
            
            table.resizeColumnsToContents()
            layout.addWidget(table)
            
            self.tab_widget.addTab(customer_widget, "客户管理")
        
        def create_analytics_tab(self):
            """创建统计分析标签页"""
            analytics_widget = QWidget()
            layout = QVBoxLayout(analytics_widget)
            
            # 统计卡片
            cards_layout = QHBoxLayout()
            
            # 总订单数
            total_orders_card = self.create_stat_card("总订单数", "25", "#3498db")
            cards_layout.addWidget(total_orders_card)
            
            # 总收入
            total_revenue_card = self.create_stat_card("总收入", "¥22,000", "#2ecc71")
            cards_layout.addWidget(total_revenue_card)
            
            # 进行中订单
            active_orders_card = self.create_stat_card("进行中订单", "8", "#f39c12")
            cards_layout.addWidget(active_orders_card)
            
            # 客户数量
            total_customers_card = self.create_stat_card("客户数量", "15", "#9b59b6")
            cards_layout.addWidget(total_customers_card)
            
            layout.addLayout(cards_layout)
            
            # 图表占位符
            chart_placeholder = QLabel("图表区域\n(数据可视化图表将在这里显示)")
            chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            chart_placeholder.setStyleSheet("""
                QLabel {
                    border: 2px dashed #bdc3c7;
                    border-radius: 10px;
                    color: #7f8c8d;
                    font-size: 16px;
                    padding: 50px;
                    margin: 20px;
                }
            """)
            layout.addWidget(chart_placeholder)
            
            self.tab_widget.addTab(analytics_widget, "统计分析")
        
        def create_stat_card(self, title, value, color):
            """创建统计卡片"""
            card = QWidget()
            card.setFixedHeight(120)
            card.setStyleSheet(f"""
                QWidget {{
                    background-color: {color};
                    border-radius: 10px;
                    margin: 10px;
                }}
                QLabel {{
                    color: white;
                    font-weight: bold;
                }}
            """)
            
            layout = QVBoxLayout(card)
            layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 14px;")
            
            value_label = QLabel(value)
            value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            value_label.setStyleSheet("font-size: 24px; font-weight: bold;")
            
            layout.addWidget(title_label)
            layout.addWidget(value_label)
            
            return card
        
        def apply_style(self):
            """应用样式"""
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #ecf0f1;
                }
                
                QTabWidget::pane {
                    border: 1px solid #bdc3c7;
                    background-color: white;
                    border-radius: 5px;
                }
                
                QTabBar::tab {
                    background-color: #bdc3c7;
                    color: #2c3e50;
                    padding: 10px 20px;
                    margin-right: 2px;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
                
                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 2px solid #3498db;
                }
                
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                
                QPushButton:hover {
                    background-color: #2980b9;
                }
                
                QPushButton:pressed {
                    background-color: #21618c;
                }
                
                QLineEdit {
                    padding: 8px;
                    border: 1px solid #bdc3c7;
                    border-radius: 4px;
                    font-size: 14px;
                }
                
                QTableWidget {
                    gridline-color: #ecf0f1;
                    background-color: white;
                    alternate-background-color: #f8f9fa;
                }
                
                QHeaderView::section {
                    background-color: #34495e;
                    color: white;
                    padding: 10px;
                    border: none;
                    font-weight: bold;
                }
            """)
        
        def show_message(self, message):
            """显示消息"""
            QMessageBox.information(self, "功能提示", f"{message}\n\n这是前端界面演示版本。")
    
    def main():
        """主函数"""
        app = QApplication(sys.argv)
        app.setApplicationName("兼职接单管理系统")
        
        # 设置字体
        font = QFont("Microsoft YaHei", 9)
        app.setFont(font)
        
        # 创建主窗口
        window = SimpleMainWindow()
        window.show()
        
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(main())

except ImportError as e:
    print(f"PyQt6 未安装或导入失败: {e}")
    print("请运行: pip install PyQt6")
    print("\n正在创建一个简单的命令行演示...")
    
    # 命令行版本演示
    def cli_demo():
        print("=" * 60)
        print("兼职接单管理系统 - 前端界面演示")
        print("=" * 60)
        print()
        print("📋 订单管理模块:")
        print("  - 订单列表显示")
        print("  - 新建/编辑订单")
        print("  - 订单状态管理")
        print("  - 搜索和筛选")
        print()
        print("👥 客户管理模块:")
        print("  - 客户信息管理")
        print("  - 客户订单关联")
        print("  - 客户搜索")
        print()
        print("📊 统计分析模块:")
        print("  - 收入统计")
        print("  - 订单状态分析")
        print("  - 数据可视化图表")
        print()
        print("⚙️ 系统设置模块:")
        print("  - 系统配置管理")
        print("  - 主题切换")
        print("  - 数据备份")
        print()
        print("🎨 界面特性:")
        print("  - 现代化扁平设计")
        print("  - 响应式布局")
        print("  - 深色/浅色主题")
        print("  - 直观的用户交互")
        print()
        print("注意：这是命令行演示版本。")
        print("完整的PyQt6图形界面需要安装PyQt6依赖。")
        print("=" * 60)
    
    cli_demo()
