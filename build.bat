@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 兼职接单管理系统 - Windows 打包脚本

echo 🚀 开始打包兼职接单管理系统...

REM 设置变量
set "APP_NAME=兼职接单管理系统"
set "VERSION=1.0.0"
set "BUILD_DIR=build"
set "DIST_DIR=dist"

REM 清理旧的构建文件
echo 🧹 清理旧的构建文件...
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"

REM 删除 Python 缓存文件
for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"
del /s /q *.pyc >nul 2>&1

REM 检查 Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到 PATH
    pause
    exit /b 1
)

REM 检查 PyInstaller
python -m PyInstaller --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller 未安装，正在安装...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller 安装失败
        pause
        exit /b 1
    )
)

REM 创建基本的 spec 文件（如果不存在）
if not exist "build_config.spec" (
    echo 📝 创建基本的 spec 文件...
    python -m PyInstaller --name="%APP_NAME%" --onefile --windowed main.py
    move "%APP_NAME%.spec" build_config.spec
)

REM 执行打包
echo 📦 开始打包应用程序...
python -m PyInstaller --clean --noconfirm build_config.spec

REM 检查打包结果
if exist "%DIST_DIR%" (
    echo ✅ 打包完成!
    echo 📁 输出目录: %DIST_DIR%
    
    REM 显示输出文件
    echo 📋 输出文件:
    dir "%DIST_DIR%" /b
    
    REM 检查可执行文件
    if exist "%DIST_DIR%\%APP_NAME%.exe" (
        echo 📊 可执行文件已创建: %APP_NAME%.exe
    ) else if exist "%DIST_DIR%\%APP_NAME%" (
        echo 📊 应用程序目录已创建: %APP_NAME%
    )
    
) else (
    echo ❌ 打包失败!
    pause
    exit /b 1
)

echo 🎉 打包完成!
echo.
echo 💡 提示:
echo    - 可执行文件位于 %DIST_DIR% 目录中
echo    - 首次运行可能需要一些时间来初始化
echo    - 如遇到问题，请查看 build.log 文件
echo.
pause
